package com.ybm.app.adapter;

import androidx.annotation.IdRes;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;

/**
 * Created by Administrator on 2016/7/7.
 */
public class YBMBaseHolder extends BaseViewHolder {

    protected YBMBaseHolder(View view) {
        super(view);
    }

    public YBMBaseHolder setImageUrl(int viewId, String url) {
        ImageView view = (ImageView) this.getView(viewId);
        ImageHelper.with(convertView.getContext()).load(url).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontTransform().dontAnimate().into(view);
        return this;
    }

    public YBMBaseHolder setImageUrl(int viewId, String url, int resId) {
        ImageView view = (ImageView) this.getView(viewId);
        ImageHelper.with(convertView.getContext()).load(url).placeholder(resId).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontTransform().dontAnimate().into(view);
        return this;
    }

    public YBMBaseHolder setImageUrl(int viewId, String url, int defResId, int errResID) {
        ImageView view = (ImageView) this.getView(viewId);
        ImageHelper.with(convertView.getContext()).load(url).placeholder(defResId).error(errResID).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontTransform().dontAnimate().into(view);
        return this;
    }

    @Override
    public BaseViewHolder setVisible(@IdRes int viewId, boolean visible) {
        if (getView(viewId) == null){
            Log.e("ViewHolder","getViewById is null");
            return this;
        }
        setGone(viewId, visible);
        return this;
    }

    @Override
    public BaseViewHolder setGone(int viewId, boolean visible) {
        if (getView(viewId) == null){
            Log.e("ViewHolder","getViewById is null");
            return this;
        }
        return super.setGone(viewId, visible);
    }

    public BaseViewHolder setVisibleOrInvisible(@IdRes int viewId, boolean visible) {
        if (getView(viewId) == null){
            Log.e("ViewHolder","getViewById is null");
            return this;
        }
        super.setVisible(viewId, visible);
        return this;
    }
}

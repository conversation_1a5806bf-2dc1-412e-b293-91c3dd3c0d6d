package com.ybm.app.common;

import android.app.Dialog;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Process;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.R;
import com.ybm.app.bean.AppVersionResponse;
import com.ybm.app.common.download.DownloadManager;
import com.ybm.app.common.download.DownloadTask;
import com.ybm.app.common.download.DownloadTaskListener;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.JsonUtils;
import com.ybm.app.utils.MemoryManager;
import com.ybm.app.utils.NetUtil;
import com.ybm.app.utils.SpUtil;
import com.ybm.app.utils.UiUtils;
import com.ybm.app.utils.Utils;
import com.ybm.app.utils.file.ExternalFileManager;

import java.io.File;
import java.io.FileFilter;
import java.util.Random;

/**
 * app更新管理
 */
public abstract class UpdateManager {

    private int retryCount = 0;
    private int retryMAX = 1;//只重新下载一次
    private static final String SUFFIX = ".apk";
    private static final String SUFFIX_PATCH = ".patch";
    private static final String PATCHVERSION = "ybm_patchversion";
    private static final String PATCHCOUNT = "ybm_patchcount";
    private static final int MAXFAIL = 4;//最大失败次数
    protected AppVersionResponse apkInfo;
    private static boolean isUpdateing;
    private static String dirString;
    private ProgressBar mProgress;
    private static final int DOWN_UPDATE = 1;
    private static final int DOWN_OVER = 2;
    private static final int DOWN_ERROR = 3;//下载失败
    private static final int DOWN_PREPARE = 4;//下载准备
    private static final int DOWN_START = 5;//下载开始
    private int progress;
    private Dialog downloadDialog;
    private TextView tv;
    private TextView btn;
    private int errorCount = 0;

    public UpdateManager() {

    }

    //修改app下载的名字
    private static String getApkName() {
        String name = null;
        try {
            name = BaseYBMApp.getAppContext().getPackageName();
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
        if (TextUtils.isEmpty(name)) {
            name = "ybm100";
        }
        return name;
    }

    //检测更新
    public abstract void checkUpdate(boolean isShowDialog);

    // 当有新版本提示示更新，有apkFile 是提示安装，没有是提示安装后下载
    public void showNoticeDialog(final AppVersionResponse apkInfo, final File apkFile) {
        if (BaseYBMApp.getApp().getCurrActivity() == null) {
            return;
        }
        final BaseDialog alert = new BaseDialog(BaseYBMApp.getApp().getCurrActivity());
        alert.setCanceledOnTouchOutside(false);
        alert.setCancelable(false);
        alert.setTitle("App更新");
        if (!TextUtils.isEmpty(apkInfo.apkRemark)) {
            alert.setMsg(apkInfo.apkRemark);
        } else {
            if (apkFile == null) {
                alert.setMsg("安装更新，体验更优惠价格！");
            } else {
                alert.setMsg("安装包已经下载完成！");
            }
        }
        alert.setConfirmButton("立即安装", new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                alert.dismiss();
                if (apkFile == null || !apkFile.exists()) {
                    showDownloadDialog(apkInfo.apkUrl, apkInfo.latestVersion, true);
                } else {
                    installSDApk(apkFile.toString(), apkInfo.isForceUpdate);
                }
            }
        });
        if (apkInfo.isForceUpdate) {//强制更新
            if (!TextUtils.isEmpty(apkInfo.apkRemark)) {
                alert.setMsg(apkInfo.apkRemark);
            } else {
                alert.setMsg("为保证应用正常使用请立即安装更新");
            }
        } else {
            alert.setCancelButton("取消安装", new View.OnClickListener() {
                @Override
                public void onClick(View view) {//apk没有就下载但是这次不安装
                    alert.dismiss();
                    if (apkFile == null || !apkFile.exists()) {
                        downloadApk(apkInfo.apkUrl, apkInfo.latestVersion, false, false, false);
                    }
                }
            });
        }
        alert.show();
    }

    // 进度条下载apk
    public void showDownloadDialog(String url, int ver, boolean showProgres) {
        LayoutInflater inflater = null;
        if (BaseYBMApp.getApp().getCurrActivity() != null && showProgres) {
            downloadDialog = new Dialog(BaseYBMApp.getApp().getCurrActivity(), R.style.Dialog);
            inflater = LayoutInflater.from(BaseYBMApp.getApp().getCurrActivity());
            View v = inflater.inflate(R.layout.progress, null);
            mProgress = (ProgressBar) v.findViewById(R.id.progress_bar);
            tv = (TextView) v.findViewById(R.id.tv_state);
            btn = (TextView) v.findViewById(R.id.btn_ok);
            btn.setTextColor(Color.parseColor("#E2E2E2"));
            downloadDialog.setCanceledOnTouchOutside(false);
            downloadDialog.setCancelable(false);
            downloadDialog.show();
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams((int) (UiUtils.getScreenWidth() * 0.75), ViewGroup.LayoutParams.WRAP_CONTENT);
            downloadDialog.setContentView(v, params);
            downloadApk(url, ver, false, false, true);
        } else {
            downloadApk(url, ver, false, true, false);
        }
    }

    //强制下载并更新，不去管理版本号与其它的东西，只要签名一致就可以下载安装
    public final void forceUpdateApp(final String url, final boolean showProgress) {
        final int ver = new Random().nextInt(1000);
        SmartExecutorManager.getInstance().executeUI(new Runnable() {
            @Override
            public void run() {
                showDownloadDialog(url, 8000 + ver, showProgress);
            }
        });
    }

    //开始下载
    public void onStart(DownloadTask downloadTask) {

    }

    //取消
    public void onCancel(DownloadTask downloadTask) {

    }

    //下载中
    public void onDownloading(DownloadTask downloadTask) {

    }

    //完成
    public void onCompleted(DownloadTask downloadTask, File apkFile) {

    }

    //下载错误
    public void onError(DownloadTask downloadTask, int errorCode) {

    }

    // 有更新了，可以更新,先检测本地有没有，有检测签名，
    public void update(AppVersionResponse apkInfo) {
        if (apkInfo == null || TextUtils.isEmpty(apkInfo.apkUrl)) {
            LogUtils.d("没有下载地址");
            return;
        }
        final int versionCode = getVersionCode();
        final int patchVersionCode = getPatchVersionCode();
        if (versionCode >= apkInfo.latestVersion && !apkInfo.isPatchUpdate) {//不是apk更新
            return;
        } else if (apkInfo.isPatchUpdate && (apkInfo.patchVersion <= patchVersionCode || TextUtils.isEmpty(apkInfo.patchUrl))) {//又不是补丁更新
            return;
        }
        this.apkInfo = apkInfo;
        setApkInfo(apkInfo);
        File file = hasFile(apkInfo);
        if (file != null) {//本地可以安装
            LogUtils.d("本地包可以安装");
            installApkBg(apkInfo, file);
        } else {
            setPatchcount(0);//去掉错误提示
            if (apkInfo.isPatchUpdate) {//热更新不出对话框，自动后台下载,然后更新
                downloadApk(apkInfo.patchUrl, apkInfo.patchVersion, true, true, false);
            } else {
                showNoticeDialog(apkInfo, null);
            }
        }
    }


    private Handler mHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case DOWN_UPDATE:
                    tv.setText("已经下载" + progress + "%");
                    mProgress.setProgress(progress);
                    btn.setText("最新App下载中...");
                    btn.setEnabled(false);
                    btn.setTextColor(Color.parseColor("#E2E2E2"));
                    break;
                case DOWN_OVER:
                    final int ver = msg.getData().getInt("ver");
                    final boolean isPatch = msg.getData().getBoolean("isPatch");
                    tv.setText("已经下载完成，请点击安装");
                    mProgress.setProgress(100);
                    btn.setEnabled(true);
                    btn.setText("立 即 安 装");
                    btn.setTextColor(Color.parseColor("#ffffff"));
                    btn.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            downloadDialog.dismiss();
                            installSDApk(getSaveFile(ver, isPatch), true);
                        }
                    });
                    break;
                case DOWN_ERROR:
                    int errorCode = -5;
                    try {
                        errorCode = msg.getData().getInt("errorCode");
                    } catch (Throwable e) {
                        BugUtil.sendBug(e);
                    }
                    if (errorCode == DownloadTaskListener.DOWNLOAD_ERROR_NET_NOT_ERROR) {
                        tv.setText("下载错误，请打开网络后重新下载");
                        btn.setText("知道了");
                    } else if (errorCode == DownloadTaskListener.DOWNLOAD_ERROR_IO_ERROR_SOCKETTIMEOUTEXCEPTION || errorCode == DownloadTaskListener.DOWNLOAD_ERROR_IO_ERROR_SOCKETEXCEPTION) {
                        tv.setText("下载错误，当前网速太慢");
                        btn.setText("知道了");
                    } else if (errorCode == DownloadTaskListener.DOWNLOAD_ERROR_FILE_NOT_FOUND) {
                        tv.setText("下载错误，下载文件没有找到");
                        btn.setText("知道了");
                    } else if (errorCode == DownloadTaskListener.CHECK_APK_SIGNATURES_ERROR) {
                        tv.setText("下载错误，下载文件签名错误");
                        btn.setText("知道了");
                    } else if (errorCode == DownloadTaskListener.DOWNLOAD_ERROR_IO_ERROR) {
                        tv.setText("下载错误，请检测网络后重新下载");
                        btn.setText("知道了");
                    } else {
                        tv.setText("下载错误，请检测网络后重新下载");
                        btn.setText(" 取 消 ");
                    }
                    mProgress.setProgress(0);
                    btn.setEnabled(true);
                    btn.setTextColor(Color.parseColor("#ffffff"));
                    btn.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            downloadDialog.dismiss();
                        }
                    });
                    break;
                case DOWN_START:
                    btn.setEnabled(false);
                    btn.setText("准备下载最新App");
                    btn.setTextColor(Color.parseColor("#E2E2E2"));
                    break;
            }
        }
    };

    private class MyDownloadListener implements DownloadTaskListener {
        private boolean autoInstall = true;
        private boolean showProgress = false;
        private int version = 0;
        private boolean isPatch;

        public MyDownloadListener(boolean autoInstall, int ver, boolean showProgress, boolean isPatch) {
            this.autoInstall = autoInstall;
            this.showProgress = showProgress;
            version = ver;
            this.isPatch = isPatch;
        }

        @Override
        public void onPrepare(DownloadTask downloadTask) {//清空下载目前
            cleanDir();
        }

        @Override
        public void onStart(DownloadTask downloadTask) {
            LogUtils.d("开始下载apk:" + downloadTask.saveDirPath + downloadTask.fileName);
            UpdateManager.this.onStart(downloadTask);
            if (showProgress) {
                mHandler.sendEmptyMessage(DOWN_START);
            }
        }

        @Override
        public void onDownloading(DownloadTask downloadTask) {
            UpdateManager.this.onDownloading(downloadTask);
            progress = (int) downloadTask.getPercent();
            if (showProgress) {
                mHandler.sendEmptyMessage(DOWN_UPDATE);
            }
        }

        @Override
        public void onPause(DownloadTask downloadTask) {

        }

        @Override
        public void onCancel(DownloadTask downloadTask) {
            UpdateManager.this.onCancel(downloadTask);
        }

        @Override
        public void onCompleted(DownloadTask downloadTask) {//下载完成后修改名字
            File file = new File(getSaveFileTemp(version, isPatch));
            if (file == null || !file.exists() || (!isPatch && !isSignatureRight(file))) {//下载异常
                if (file != null) {
                    delFile(file.getAbsolutePath());
                }
                onError(downloadTask, CHECK_APK_SIGNATURES_ERROR);//重新下载
                return;
            } else {//修改名字
                checkAndHandlerApk(1, file.getAbsolutePath());
                File apkFile = new File(getSaveFile(version, isPatch));
                file.renameTo(apkFile);
                checkAndHandlerApk(2, apkFile.getAbsolutePath());
                retryCount = 0;
                if (apkInfo != null) {
                    setApkInfo(apkInfo);
                }
                if (isPatch) {
                    LogUtils.d("补丁下载完成:" + getSaveFile(version, isPatch));
                } else {
                    LogUtils.d("APK下载完成:" + getSaveFile(version, isPatch));
                }
                if (autoInstall) {
                    installApkBg(getApkInfo(), apkFile);
                }
                UpdateManager.this.onCompleted(downloadTask, apkFile);
                if (showProgress) {
                    Bundle bundle = new Bundle();
                    bundle.putInt("ver", version);
                    bundle.putBoolean("isPatch", isPatch);
                    Message msg = mHandler.obtainMessage();
                    msg.setData(bundle);
                    msg.what = DOWN_OVER;
                    mHandler.sendMessage(msg);
                }
            }
        }

        @Override
        public void onError(DownloadTask downloadTask, int errorCode) {
            if (retryMAX > retryCount) {//重试一下
                retryCount++;
                BugUtil.sendBug(new NullPointerException("重新下载中。。。。" + errorCode));
                DownloadManager.getInstance().reDownload(downloadTask);
            } else {//上传错误数据
                BugUtil.sendBug(new NullPointerException((retryMAX + 1) + "次重新下载失败。。。。" + errorCode));
                UpdateManager.this.onError(downloadTask, errorCode);
                delFile(getSaveFileTemp(version, isPatch));
                if (showProgress) {
                    Bundle bundle = new Bundle();
                    bundle.putInt("errorCode", errorCode);
                    Message msg = mHandler.obtainMessage();
                    msg.setData(bundle);
                    msg.what = DOWN_ERROR;
                    mHandler.sendMessage(msg);
                }
            }
        }
    }

    public static String getSaveDir() {
        if (!TextUtils.isEmpty(dirString) && dirString.length() >= 8) {
            return dirString;
        }
        try {
            if (MemoryManager.InternalFull() || MemoryManager.SDFull()) {
                SmartExecutorManager.getInstance().executeUI(new Runnable() {
                    @Override
                    public void run() {
                        UiUtils.toast("手机存储空间不足100MB了，为保存app正常使用，请清理存储空间后继续使用！");
                        BugUtil.sendBug(new NullPointerException("手机存储空间不足100MB了"));
                    }
                });
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
        try {
            if (BaseYBMApp.getAppContext().getExternalCacheDir() == null) {
                if (BaseYBMApp.getApp().getCurrActivity() == null || BaseYBMApp.getApp().getCurrActivity().getExternalCacheDir() == null) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        dirString = ExternalFileManager.get().getExternalApkFile().getAbsolutePath();
                    } else
                        dirString = Environment.getExternalStorageDirectory().getAbsolutePath() + "/ybm/";
                } else {
                    dirString = BaseYBMApp.getApp().getCurrActivity().getExternalCacheDir().getAbsolutePath() + "/ybm/";
                }
            } else {
                dirString = BaseYBMApp.getAppContext().getExternalCacheDir().getAbsolutePath() + "/";
            }
        } catch (Exception e) {
            BugUtil.sendBug(new NullPointerException("获取手机存储路径异常"));
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    dirString = ExternalFileManager.get().getExternalApkFile().getAbsolutePath();
                } else
                    dirString = Environment.getExternalStorageDirectory().getAbsolutePath() + "/ybm/";
            } catch (Exception e1) {
                BugUtil.sendBug(new NullPointerException("获取手机存储路径异常2"));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    dirString = ExternalFileManager.get().getExternalApkFile().getAbsolutePath();
                } else
                    dirString = Environment.getExternalStorageDirectory().getAbsolutePath() + "/ybm/";
            }
        }
        if (TextUtils.isEmpty(dirString) || dirString.length() < 8) {//如果路径为null 写死路径
            dirString = "/storage/emulated/0/ybm/";
            BugUtil.sendBug(new NullPointerException("使用默认路径了"));
        }
        return dirString;
    }

    private static void cleanDir() {
        String dir = getSaveDir();
        File file = new File(dir);
        if (file != null && file.exists()) {
            if (file.isDirectory()) {
                File[] files = file.listFiles(new FileFilter() {
                    @Override
                    public boolean accept(File pathname) {
                        if (pathname != null && pathname.getName() != null && pathname.getName().endsWith(SUFFIX) || pathname.getName().endsWith(SUFFIX_PATCH)) {
                            return true;
                        }
                        return false;
                    }
                });
                if (files != null && files.length > 0) {
                    for (File file1 : files) {
                        try {
                            LogUtils.d("删除文件：" + file1.getAbsolutePath());
                            file1.delete();
                        } catch (Throwable e) {
                            BugUtil.sendBug(e);
                        }
                    }
                }
            } else if (file.isFile()) {
                try {
                    file.delete();
                } catch (Throwable e) {
                    BugUtil.sendBug(e);
                }
            }

        }
    }

    public static void cleanDirBg() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            SmartExecutorManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    cleanDir();
                }
            });
        } else {
            cleanDir();
        }
    }

    private String getSaveFile(int ver, boolean isPatch) {
        return getSaveDir() + getSaveFileName(ver, isPatch);
    }


    private static String getSaveFileName(int ver, boolean isPatch) {
        if (!isPatch) {
            return getApkName() + "_" + ver + SUFFIX;
        } else {
            return getApkName() + "_" + ver + SUFFIX_PATCH;
        }
    }

    private String getSaveFileTemp(int ver, boolean isPatch) {
        return getSaveDir() + getSaveFileNameTemp(ver, isPatch);
    }

    private String getSaveFileNameTemp(int ver, boolean isPatch) {
        if (!isPatch) {
            return getApkName() + "_" + ver + "_temp" + SUFFIX;
        } else {
            return getApkName() + "_" + ver + "_temp" + SUFFIX_PATCH;
        }
    }

    //下载文件是否存在
    private static File hasFile(AppVersionResponse apkInfo) {
        if (apkInfo == null) {
            return null;
        }
        File old;
        if (apkInfo.isPatchUpdate) {
            old = new File(getSaveDir(), getSaveFileName(apkInfo.patchVersion, true));
        } else {
            old = new File(getSaveDir(), getSaveFileName(apkInfo.latestVersion, false));
        }
        if (old != null) {
            try {
                if (old.exists()) {
                    if (apkInfo.isPatchUpdate) {
                        if (old.length() > 1024 && old.getName().toLowerCase().endsWith(SUFFIX_PATCH)) {//文件大于1024b
                            return old;
                        }
                    } else {
                        if (old.length() > 3 * 1024 * 1024 && old.getName().toLowerCase().endsWith(SUFFIX)) {//文件大于3MB
                            return old;
                        }
                    }
                    delFile(old.getAbsolutePath());
                    return null;

                }
            } catch (Exception e) {
                delFile(old.getAbsolutePath());
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    private AppVersionResponse getApkInfo() {
        if (apkInfo != null) {
            return apkInfo;
        }
        apkInfo = getFromSD();
        return apkInfo;
    }

    private static AppVersionResponse getFromSD() {
        String json = SpUtil.readString("apkinfo", null);
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, AppVersionResponse.class);
    }


    private static void setApkInfo(AppVersionResponse apkInfo) {
        String json = JsonUtils.toJson(apkInfo, AppVersionResponse.class);
        SpUtil.writeString("apkinfo", json);
    }

    //删除下载的文件
    private static void delFile(String filePath) {
        try {
            File file = new File(filePath);
            if (file == null || !file.exists()) {
                return;
            } else {
                file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 下载apk
    protected void downloadApk(String url, int ver, boolean isPatch, boolean autoInstall, boolean showProgress) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        DownloadManager.getInstance().addTask(url.hashCode() + "", url, getSaveDir(), getSaveFileNameTemp(ver, isPatch), new MyDownloadListener(autoInstall, ver, showProgress, isPatch));
    }

    // 安装apk,有两种方式，对话框安装，直接补丁安装
    private void installApkBg(final AppVersionResponse apkInfo, final File apkFile) {
        if (Looper.getMainLooper() == Looper.myLooper()) {//ui
            SmartExecutorManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    installApk(apkInfo, apkFile);
                }
            });
        } else {
            installApk(apkInfo, apkFile);
        }
    }

    // 安装apk,有两种方式，对话框系统安装，直接补丁安装
    private synchronized void installApk(final AppVersionResponse apkInfo, File apkFile) {
        if (apkFile == null || !apkFile.exists()) {//重新调用检测更新
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    update(apkInfo);
                }
            });
            return;
        }
        if (!apkInfo.isPatchUpdate && !isSignatureRight(apkFile)) {
            LogUtils.d("apk签名异常，重新弹窗下载安装");
            if (apkFile.exists()) {
                delFile(apkFile.getAbsolutePath());
            }
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    showNoticeDialog(apkInfo, null);
                }
            });
            return;
        }
        if (apkInfo.isPatchUpdate) {//补丁安装
            if (supportPatch(apkFile, apkInfo.patchVersion)) {//补丁安装
                addPatch(apkFile, apkInfo.patchVersion, apkInfo.isForceUpdate);
            }
        } else {//全量安装
            if (Utils.getVersionCode(BaseYBMApp.getAppContext(), apkFile) <= apkInfo.latestVersion) {
                LogUtils.d("apk版本异常，重新弹窗下载安装");
                if (apkFile.exists()) {
                    delFile(apkFile.getAbsolutePath());
                }
                SmartExecutorManager.getInstance().executeUI(new Runnable() {
                    @Override
                    public void run() {
                        showNoticeDialog(apkInfo, null);
                    }
                });
                return;
            }
            androidIntall(apkInfo, apkFile);
        }
    }

    private void androidIntall(final AppVersionResponse apkInfo, final File apkFile) {
        SmartExecutorManager.getInstance().executeUI(new Runnable() {
            @Override
            public void run() {
                showNoticeDialog(apkInfo, apkFile);
            }
        });
    }

    //增加安装前的检测功能
    protected void installSDApk(final String apkFile, final boolean isForceUpdate) {
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                if (checkAndHandlerApk(3, apkFile)) {
                    SmartExecutorManager.getInstance().executeUI(new Runnable() {
                        @Override
                        public void run() {
                            installSDApkUI(apkFile, isForceUpdate);
                        }
                    });
                }
            }
        });
    }

    protected static void installSDApkUI(String apkFile, boolean isForceUpdate) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setDataAndType(Uri.parse("file://" + apkFile),
                "application/vnd.android.package-archive");
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        BaseYBMApp.getAppContext().startActivity(intent);
        if (isForceUpdate) {//强制升级退出应用，
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        System.exit(0);
                        Process.killProcess(Process.myPid());
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }
            }, 600);
        }
    }


    //加载指定的补丁文件
    public void addPatch(final File path, int patchVer, boolean isForce) {
        if (path == null || !path.exists()) {
            return;
        }
        if (Utils.isMainProcess(BaseYBMApp.getAppContext())) {//主进程
            if (path.exists()) {
                if (isUpdateing) {
                    LogUtils.d("补丁更新中，不可重复执行");
                    return;
                }
                LogUtils.d("开始安装补丁：" + path.getAbsolutePath());
                isUpdateing = true;
                try {
                    SpUtil.writeInt(PATCHVERSION, patchVer);//成功之后，写入版本号
                    setPatchcount(0);
                    addPatch(path.getAbsolutePath(), isForce);
                    LogUtils.d("补丁更新完成");
                } catch (Throwable e) {//补丁安装异常,删除补丁文件
                    delFile(path.getAbsolutePath());
                    e.printStackTrace();
                    LogUtils.d(e.getCause());
                }
                isUpdateing = false;
            }
        }
    }

    //开始安装补丁了
    protected void addPatch(String pathAbsolutePath, boolean isForce) {

    }

    //获取补丁版本
    public static String getVersion() {
        try {
            return BaseYBMApp.getAppContext().getPackageManager().getPackageInfo(BaseYBMApp.getAppContext().getPackageName(), 0).versionName;
        } catch (Throwable e) {
            return "";
        }
    }

    //获取Apk版本号
    public static int getVersionCode() {
        return Utils.getVersionCode(BaseYBMApp.getAppContext());
    }

    //获取补丁版本号
    public abstract int getPatchVersionCode();

    //是否补丁模式运行
    public abstract boolean isPatchRuning();

    //获取补丁版本号
    public static int getVersionCodeSp() {
        return SpUtil.readInt("app_version_code", -1);
    }

    //设置补丁版本号
    public static void setVersionCodeSp(int version) {
        SpUtil.writeInt("app_version_code", version);
    }

    //签名一样
    private static boolean isSignatureRight(File apkFile) {
        try {
            Signature appSig = BaseYBMApp.getAppContext().getPackageManager().getPackageInfo(BaseYBMApp.getAppContext().getPackageName(), PackageManager.GET_SIGNATURES).signatures[0];
            Signature demoSig = BaseYBMApp.getAppContext().getPackageManager().getPackageArchiveInfo(apkFile.getAbsolutePath(), PackageManager.GET_SIGNATURES).signatures[0];
            if (appSig.hashCode() != demoSig.hashCode()) {
                String pckName = BaseYBMApp.getAppContext().getPackageManager().getPackageArchiveInfo(apkFile.getAbsolutePath(), PackageManager.GET_ACTIVITIES).packageName;
                if (!TextUtils.isEmpty(pckName)) {
                    BugUtil.sendBug(new NullPointerException("下载签名不同的包：" + pckName + " 网络是" + NetUtil.getNetWorkTypeString(BaseYBMApp.getAppContext())));
                }
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    //获取下载包的版本号
    private int getAPKVersionCode(String path) {
        try {
            PackageInfo info = BaseYBMApp.getAppContext().getPackageManager().getPackageArchiveInfo(path, PackageManager.GET_ACTIVITIES);
            if (info != null) {
                return info.versionCode;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * @param path
     * @return -3 下载包获取失败 -2 当前包获取失败  -1 小于 0 相等 1正常大于当前版本
     */
    private int checkDownloadApkVersion(String path) {
        int down = getAPKVersionCode(path);
        if (down <= 0) {
            return -3;
        }
        int my = getVersionCode();
        if (my <= 0) {
            return -2;
        }
        if (down - my < 0) {
            return -1;
        }
        return down - my > 0 ? 1 : 0;
    }

    /**
     * @param from 1 下载完成 2 下载完成修改名字 3 安装前
     * @param path
     */
    private boolean checkAndHandlerApk(int from, String path) {
        try {
            int result = checkDownloadApkVersion(path);
            if (result < 1) {
                LogUtils.d("检测安装包：" + getFromStr(from) + "的检测情况是" + getCheckStr(result));
            }
            if (result < 0) {
                BugUtil.sendBug(new NullPointerException("检测安装包：" + getFromStr(from) + "的检测情况是" + getCheckStr(result)));
                return false;
            } else if (result == 0) {//版本号一样的情况
                errorCount++;
                if (from == 3) {
                    UiUtils.toast("下载包版本和当前包一致，取消安装");
                    if (errorCount == 3) {//app整个下载过程没有问题
                        LogUtils.d("检测安装包：下载过程没有问题");
                    } else {//app下载有问题
                        LogUtils.d("检测安装包：下载过程有问题，可能修改app名出问题了");
                    }
                    BugUtil.sendBug(new NullPointerException("检测安装包错误数：" + errorCount + " 网络是" + NetUtil.getNetWorkTypeString(BaseYBMApp.getAppContext())));
                }
                BugUtil.sendBug(new NullPointerException("检测安装包：" + getFromStr(from) + "的检测情况是" + getCheckStr(result) + " 网络是" + NetUtil.getNetWorkTypeString(BaseYBMApp.getAppContext())));
                return false;
            } else {
                return true;
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
        return false;
    }

    private String getFromStr(int from) {
        switch (from) {
            case 1:
                return "下载完成没有修改名字";
            case 2:
                return "修改名字后";
            case 3:
                return "安装前";
            case 4:
                return "";
        }
        return "";
    }

    private String getCheckStr(int result) {
        switch (result) {
            case -3:
                return "获取下载包版本号错误";
            case -2:
                return "获取当前包版本号错误";
            case -1:
                return "下载包版本小于当前包版本";
            case 0:
                return "下载包版本等于当前包版本";
            case 1:
                return "下载包版本大于当前包版本";
        }
        return "";
    }


    //安装包是否支持补丁升级
    private static boolean supportPatch(File apkFile, int lastVersion) {
        if (SpUtil.readInt(PATCHCOUNT, 0) > MAXFAIL) {//多失败
            LogUtils.d("多次失败，不支持补丁更新：" + lastVersion);
            return false;
        }
        if (apkFile != null && apkFile.getName().endsWith(SUFFIX_PATCH)) {
            return true;
        }
        return false;
    }

    public static void setPatchcount(int patchcount) {
        SpUtil.writeInt(PATCHCOUNT, patchcount);
    }

    public static int getPatchcount() {
        return SpUtil.readInt(PATCHCOUNT, 0);
    }

    public static boolean isPatchError() {
        return getPatchcount() > MAXFAIL;
    }

    //清除补丁，回滚操作
    public static boolean clearPatch(Throwable e) {
        if (!needhandler(e)) {
            try {
                BugUtil.sendBug(e);
            } catch (Throwable er) {

            }
            return false;
        } else {
            try {
                BugUtil.sendBug(e);
            } catch (Throwable ee) {

            }
        }
        if (e != null && BaseYBMApp.getApp().isDebug()) {
            LogUtils.d(e);
            System.exit(0);
        }
        try {
            LogUtils.d("补丁运行异常，清除补丁中");
            if (getPatchcount() < 1000) {
                UpdateManager.setPatchcount(1000);
                //重启app
                LogUtils.d("重启应用中。。。。");
                Intent launchIntent = BaseYBMApp.getAppContext().getPackageManager().getLaunchIntentForPackage(BaseYBMApp.getAppContext().getPackageName());
                launchIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                BaseYBMApp.getAppContext().startActivity(launchIntent);
            }
            try {//上传bug
                BugUtil.sendBug(e);
            } catch (Throwable e2) {

            }
            System.exit(0);
            Process.killProcess(Process.myPid());
        } catch (Throwable e2) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public static boolean needhandler(Throwable e) {
        if (e == null) {
            return false;
        }
        if (e instanceof ClassNotFoundException || e instanceof android.view.InflateException || e instanceof ClassCastException) {
            return true;
        } else if (e.getCause() != null) {
            Throwable e2 = e.getCause();
            if (e2 instanceof ClassNotFoundException || e2 instanceof android.view.InflateException || e2 instanceof ClassCastException) {
                return true;
            }
        }
        return false;
    }

}

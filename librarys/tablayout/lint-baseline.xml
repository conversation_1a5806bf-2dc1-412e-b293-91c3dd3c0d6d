<?xml version="1.0" encoding="UTF-8"?>
<issues format="5" by="lint 3.6.3" client="gradle" variant="release" version="3.6.3">

    <issue
        id="CustomViewStyleable"
        message="By convention, the custom view (`SlidingHomeSteadyTabLayout`) and the declare-styleable (`SlidingTabLayout`) should have the same name (various editor features rely on this convention)"
        errorLine1="        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.SlidingTabLayout);"
        errorLine2="                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/SlidingHomeSteadyTabLayout.java"
            line="167"
            column="63"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                tv_tab_title.setText(tv_tab_title.getText().toString().toUpperCase());"
        errorLine2="                                                                       ~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/CommonTabLayout.java"
            line="312"
            column="72"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                tv_tab_title.setText(tv_tab_title.getText().toString().toUpperCase());"
        errorLine2="                                                                       ~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/SegmentTabLayout.java"
            line="267"
            column="72"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                    tv_tab_title.setText(tv_tab_title.getText().toString().toUpperCase());"
        errorLine2="                                                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/SlidingHomeSteadyTabLayout.java"
            line="386"
            column="76"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                    tv_tab_title.setText(tv_tab_title.getText().toString().toUpperCase());"
        errorLine2="                                                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/SlidingTabLayout.java"
            line="349"
            column="76"/>
    </issue>

    <issue
        id="SpUsage"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        errorLine1="            android:textSize=&quot;12dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_home_steady_tab.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {//16"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/widget/MsgView.java"
            line="150"
            column="13"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_bottom.xml"
            line="9"
            column="6"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_left.xml"
            line="9"
            column="6"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_right.xml"
            line="9"
            column="6"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_top.xml"
            line="9"
            column="6"/>
    </issue>

    <issue
        id="UseSparseArrays"
        message="Use `new SparseBooleanArray(...)` instead for better performance"
        errorLine1="    private SparseArray&lt;Boolean> mInitSetMap = new SparseArray&lt;>();"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/CommonTabLayout.java"
            line="851"
            column="48"/>
    </issue>

    <issue
        id="UseSparseArrays"
        message="Use `new SparseBooleanArray(...)` instead for better performance"
        errorLine1="    private SparseArray&lt;Boolean> mInitSetMap = new SparseArray&lt;>();"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/SegmentTabLayout.java"
            line="705"
            column="48"/>
    </issue>

    <issue
        id="UseSparseArrays"
        message="Use `new SparseBooleanArray(...)` instead for better performance"
        errorLine1="    private SparseArray&lt;Boolean> mInitSetMap = new SparseArray&lt;>();"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/SlidingHomeSteadyTabLayout.java"
            line="894"
            column="48"/>
    </issue>

    <issue
        id="UseSparseArrays"
        message="Use `new SparseBooleanArray(...)` instead for better performance"
        errorLine1="    private SparseArray&lt;Boolean> mInitSetMap = new SparseArray&lt;>();"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/SlidingTabLayout.java"
            line="839"
            column="48"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `12sp`: `11.5sp`"
        errorLine1="        android:textSize=&quot;11.5sp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_home_steady_tab.xml"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `12sp`: `11.5sp`"
        errorLine1="        android:textSize=&quot;11.5sp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab.xml"
            line="26"
            column="9"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `12sp`: `11.5sp`"
        errorLine1="        android:textSize=&quot;11.5sp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_bottom.xml"
            line="38"
            column="9"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `12sp`: `11.5sp`"
        errorLine1="        android:textSize=&quot;11.5sp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_left.xml"
            line="37"
            column="9"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `12sp`: `11.5sp`"
        errorLine1="        android:textSize=&quot;11.5sp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_right.xml"
            line="38"
            column="9"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `12sp`: `11.5sp`"
        errorLine1="        android:textSize=&quot;11.5sp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_segment.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `12sp`: `11.5sp`"
        errorLine1="        android:textSize=&quot;11.5sp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_top.xml"
            line="37"
            column="9"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_bottom.xml"
            line="23"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_left.xml"
            line="17"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_right.xml"
            line="23"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_top.xml"
            line="17"
            column="10"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                msgView.setText(num + &quot;&quot;);"
        errorLine2="                                ~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/utils/UnreadMsgUtils.java"
            line="35"
            column="33"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                msgView.setText(num + &quot;&quot;);"
        errorLine2="                                ~~~~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/utils/UnreadMsgUtils.java"
            line="39"
            column="33"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                msgView.setText(&quot;99+&quot;);"
        errorLine2="                                ~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/utils/UnreadMsgUtils.java"
            line="43"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;热销精选&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;热销精选&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_home_steady_tab.xml"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        errorLine1="            if (mIconGravity == Gravity.LEFT) {"
        errorLine2="                                        ~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/CommonTabLayout.java"
            line="250"
            column="41"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        errorLine1="            } else if (mIconGravity == Gravity.RIGHT) {"
        errorLine2="                                               ~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/CommonTabLayout.java"
            line="252"
            column="48"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        errorLine1="                if (mIconGravity == Gravity.LEFT) {"
        errorLine2="                                            ~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/CommonTabLayout.java"
            line="335"
            column="45"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        errorLine1="                } else if (mIconGravity == Gravity.RIGHT) {"
        errorLine2="                                                   ~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/CommonTabLayout.java"
            line="337"
            column="52"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        errorLine1="                        mIconGravity == Gravity.LEFT || mIconGravity == Gravity.RIGHT ? 4 : 0);"
        errorLine2="                                                                                ~~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/CommonTabLayout.java"
            line="877"
            column="81"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        errorLine1="                        mIconGravity == Gravity.LEFT || mIconGravity == Gravity.RIGHT ? 4 : 0);"
        errorLine2="                                                ~~~~">
        <location
            file="src/main/java/com/flyco/tablayout/CommonTabLayout.java"
            line="877"
            column="49"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:paddingRight` with `android:paddingEnd=&quot;6dp&quot;` to better support right-to-left layouts"
        errorLine1="    android:paddingRight=&quot;6dp&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_home_steady_tab.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@+id/ll_tap&quot;` to better support right-to-left layouts"
        errorLine1="        android:layout_toRightOf=&quot;@+id/ll_tap&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_bottom.xml"
            line="35"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@+id/ll_tap&quot;` to better support right-to-left layouts"
        errorLine1="        android:layout_toRightOf=&quot;@+id/ll_tap&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_left.xml"
            line="30"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@+id/ll_tap&quot;` to better support right-to-left layouts"
        errorLine1="        android:layout_toRightOf=&quot;@+id/ll_tap&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_right.xml"
            line="35"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@+id/ll_tap&quot;` to better support right-to-left layouts"
        errorLine1="        android:layout_toRightOf=&quot;@+id/ll_tap&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_segment.xml"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@+id/ll_tap&quot;` to better support right-to-left layouts"
        errorLine1="        android:layout_toRightOf=&quot;@+id/ll_tap&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_tab_top.xml"
            line="34"
            column="9"/>
    </issue>

</issues>

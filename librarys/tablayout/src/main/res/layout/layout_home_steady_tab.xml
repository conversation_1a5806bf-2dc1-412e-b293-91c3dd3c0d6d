<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingStart="6dp"
    android:paddingRight="6dp"
    android:clipChildren="false"
    android:clipToPadding="false">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:paddingBottom="4dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_tab_title"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:gravity="bottom"
            tools:text="12345"
            android:layout_marginTop="4dp"
            android:textColor="#000000"
            android:singleLine="true" />

        <TextView
            android:id="@+id/tv_descriptor"
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:text="热销精选"
            android:background="@drawable/shape_home_steady_tab"
            android:textColor="#ffffff"
            android:textSize="12dp"
            android:layout_marginTop="1dp"
            android:paddingStart="5dp"
            android:paddingEnd="5dp" />
    </LinearLayout>

    <com.flyco.tablayout.widget.MsgView
        android:id="@+id/rtv_msg_tip"
        xmlns:mv="http://schemas.android.com/apk/res-auto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="#ffffff"
        android:textSize="11.5sp"
        android:visibility="gone"
        mv:mv_backgroundColor="#FD481F"
        mv:mv_isRadiusHalfHeight="true"
        mv:mv_strokeColor="#ffffff"
        mv:mv_strokeWidth="1dp" />

</RelativeLayout>
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="RoundCornerLayout">
        <attr name="cornerRadius" format="dimension" />
        <attr name="topEnabled" format="boolean" />
        <attr name="bottomEnabled" format="boolean" />
        <attr name="topLeftEnabled" format="boolean" />
        <attr name="topRightEnabled" format="boolean" />
        <attr name="bottomLeftEnabled" format="boolean" />
        <attr name="bottomRightEnabled" format="boolean" />
        <attr name="topLeftRadius" format="dimension" />
        <attr name="topRightRadius" format="dimension" />
        <attr name="bottomLeftRadius" format="dimension" />
        <attr name="bottomRightRadius" format="dimension" />
    </declare-styleable>


    <!-- 以下是重用attr的正确姿势,一切为了在布局中可以自动提示-->
    <!-- 圆角矩形背景色 -->
    <attr name="rv_backgroundColor" format="color"/>
    <!-- 圆角矩形背景色数组 -->
    <attr name="rv_backgroundColor_array" format="reference"/>
    <!-- 圆角矩形背景色press -->
    <attr name="rv_backgroundPressColor" format="color"/>
    <!-- 圆角矩形背景色数组 -->
    <attr name="rv_backgroundPressColor_array" format="reference"/>
    <!-- 圆角弧度,单位dp-->
    <attr name="rv_cornerRadius" format="dimension"/>
    <!-- 圆角弧度,单位dp-->
    <attr name="rv_strokeWidth" format="dimension"/>
    <!-- 圆角边框颜色-->
    <attr name="rv_strokeColor" format="color"/>
    <!-- 圆角边框颜色press -->
    <attr name="rv_strokePressColor" format="color"/>
    <!-- 文字颜色press-->
    <attr name="rv_textPressColor" format="color"/>
    <!-- 圆角弧度是高度一半-->
    <attr name="rv_isRadiusHalfHeight" format="boolean"/>
    <!-- 圆角矩形宽高相等,取较宽高中大值-->
    <attr name="rv_isWidthHeightEqual" format="boolean"/>
    <!-- 圆角弧度,单位dp,TopLeft-->
    <attr name="rv_cornerRadius_TL" format="dimension"/>
    <!-- 圆角弧度,单位dp,TopRight-->
    <attr name="rv_cornerRadius_TR" format="dimension"/>
    <!-- 圆角弧度,单位dp,BottomLeft-->
    <attr name="rv_cornerRadius_BL" format="dimension"/>
    <!-- 圆角弧度,单位dp,BottomRight-->
    <attr name="rv_cornerRadius_BR" format="dimension"/>
    <!-- 是否有Ripple效果,api21+有效-->
    <attr name="rv_isRippleEnable" format="boolean"/>

    <declare-styleable name="RoundTextView">
        <attr name="rv_backgroundColor"/>
        <attr name="rv_backgroundColor_array"/>
        <attr name="rv_backgroundPressColor"/>
        <attr name="rv_backgroundPressColor_array"/>
        <attr name="rv_cornerRadius"/>
        <attr name="rv_strokeWidth"/>
        <attr name="rv_strokeColor"/>
        <attr name="rv_strokePressColor"/>
        <attr name="rv_textPressColor"/>
        <attr name="rv_isRadiusHalfHeight"/>
        <attr name="rv_isWidthHeightEqual"/>
        <attr name="rv_cornerRadius_TL"/>
        <attr name="rv_cornerRadius_TR"/>
        <attr name="rv_cornerRadius_BL"/>
        <attr name="rv_cornerRadius_BR"/>
        <attr name="rv_isRippleEnable"/>
    </declare-styleable>

    <declare-styleable name="RoundLinearLayout">
        <attr name="rv_backgroundColor"/>
        <attr name="rv_backgroundColor_array"/>
        <attr name="rv_backgroundPressColor"/>
        <attr name="rv_backgroundPressColor_array"/>
        <attr name="rv_cornerRadius"/>
        <attr name="rv_strokeWidth"/>
        <attr name="rv_strokeColor"/>
        <attr name="rv_strokePressColor"/>
        <attr name="rv_isRadiusHalfHeight"/>
        <attr name="rv_isWidthHeightEqual"/>
        <attr name="rv_cornerRadius_TL"/>
        <attr name="rv_cornerRadius_TR"/>
        <attr name="rv_cornerRadius_BL"/>
        <attr name="rv_cornerRadius_BR"/>
        <attr name="rv_isRippleEnable"/>
    </declare-styleable>

    <declare-styleable name="RoundRelativeLayout">
        <attr name="rv_backgroundColor"/>
        <attr name="rv_backgroundColor_array"/>
        <attr name="rv_backgroundPressColor"/>
        <attr name="rv_backgroundPressColor_array"/>
        <attr name="rv_cornerRadius"/>
        <attr name="rv_strokeWidth"/>
        <attr name="rv_strokeColor"/>
        <attr name="rv_strokePressColor"/>
        <attr name="rv_isRadiusHalfHeight"/>
        <attr name="rv_isWidthHeightEqual"/>
        <attr name="rv_cornerRadius_TL"/>
        <attr name="rv_cornerRadius_TR"/>
        <attr name="rv_cornerRadius_BL"/>
        <attr name="rv_cornerRadius_BR"/>
        <attr name="rv_isRippleEnable"/>
    </declare-styleable>

    <declare-styleable name="RoundFrameLayout">
        <attr name="rv_backgroundColor"/>
        <attr name="rv_backgroundColor_array"/>
        <attr name="rv_backgroundPressColor"/>
        <attr name="rv_backgroundPressColor_array"/>
        <attr name="rv_cornerRadius"/>
        <attr name="rv_strokeWidth"/>
        <attr name="rv_strokeColor"/>
        <attr name="rv_strokePressColor"/>
        <attr name="rv_isRadiusHalfHeight"/>
        <attr name="rv_isWidthHeightEqual"/>
        <attr name="rv_cornerRadius_TL"/>
        <attr name="rv_cornerRadius_TR"/>
        <attr name="rv_cornerRadius_BL"/>
        <attr name="rv_cornerRadius_BR"/>
        <attr name="rv_isRippleEnable"/>
    </declare-styleable>

    <declare-styleable name="RoundConstraintLayout">
        <attr name="rv_backgroundColor"/>
        <attr name="rv_backgroundColor_array"/>
        <attr name="rv_backgroundPressColor"/>
        <attr name="rv_backgroundPressColor_array"/>
        <attr name="rv_cornerRadius"/>
        <attr name="rv_strokeWidth"/>
        <attr name="rv_strokeColor"/>
        <attr name="rv_strokePressColor"/>
        <attr name="rv_isRadiusHalfHeight"/>
        <attr name="rv_isWidthHeightEqual"/>
        <attr name="rv_cornerRadius_TL"/>
        <attr name="rv_cornerRadius_TR"/>
        <attr name="rv_cornerRadius_BL"/>
        <attr name="rv_cornerRadius_BR"/>
        <attr name="rv_isRippleEnable"/>
    </declare-styleable>

    <declare-styleable name="RoundedImageView">
        <attr name="riv_corner_radius" format="dimension" />
        <attr name="riv_corner_radius_top_left" format="dimension" />
        <attr name="riv_corner_radius_top_right" format="dimension" />
        <attr name="riv_corner_radius_bottom_left" format="dimension" />
        <attr name="riv_corner_radius_bottom_right" format="dimension" />
        <attr name="riv_border_width" format="dimension" />
        <attr name="riv_border_color" format="color" />
        <attr name="riv_mutate_background" format="boolean" />
        <attr name="riv_oval" format="boolean" />
        <attr name="android:scaleType" />
        <attr name="riv_tile_mode">
            <enum name="clamp" value="0" />
            <enum name="repeat" value="1" />
            <enum name="mirror" value="2" />
        </attr>
        <attr name="riv_tile_mode_x">
            <enum name="clamp" value="0" />
            <enum name="repeat" value="1" />
            <enum name="mirror" value="2" />
        </attr>
        <attr name="riv_tile_mode_y">
            <enum name="clamp" value="0" />
            <enum name="repeat" value="1" />
            <enum name="mirror" value="2" />
        </attr>
    </declare-styleable>


    <declare-styleable name="SwitchButton">
        <attr name="kswThumbDrawable" format="reference"/>
        <attr name="kswThumbColor" format="color|reference"/>
        <attr name="kswThumbMargin" format="dimension|reference"/>
        <attr name="kswThumbMarginTop" format="dimension|reference"/>
        <attr name="kswThumbMarginBottom" format="dimension|reference"/>
        <attr name="kswThumbMarginLeft" format="dimension|reference"/>
        <attr name="kswThumbMarginRight" format="dimension|reference"/>
        <attr name="kswThumbWidth" format="dimension|reference"/>
        <attr name="kswThumbHeight" format="dimension|reference"/>
        <attr name="kswThumbRadius" format="dimension|reference"/>
        <attr name="kswBackRadius" format="dimension|reference"/>
        <attr name="kswBackDrawable" format="reference"/>
        <attr name="kswBackColor" format="color|reference"/>
        <attr name="kswFadeBack" format="boolean"/>
        <attr name="kswBackMeasureRatio" format="float"/>
        <attr name="kswAnimationDuration" format="integer"/>
        <attr name="kswTintColor" format="color|reference"/>
        <attr name="kswTextOn" format="string"/>
        <attr name="kswTextOff" format="string"/>
        <attr name="kswTextMarginH" format="dimension"/>
        <attr name="kswAutoAdjustTextPosition" format="boolean"/>
    </declare-styleable>

    <!--加载中 加载错误 空数据 网络错误界面-->
    <declare-styleable name="StatusViewLayout">
        <attr name="loading_view" format="reference"/>
        <attr name="error_view" format="reference"/>
        <attr name="empty_view" format="reference"/>
        <attr name="no_network_view" format="reference"/>
    </declare-styleable>

    <style name="status_view_message_text">
        <item name="android:layout_marginTop">15dp</item>
        <item name="android:textColor">#999</item>
        <item name="android:textSize">14sp</item>
    </style>

</resources>
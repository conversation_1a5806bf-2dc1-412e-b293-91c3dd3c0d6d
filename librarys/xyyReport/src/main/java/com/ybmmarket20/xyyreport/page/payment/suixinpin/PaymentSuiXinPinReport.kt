package com.ybmmarket20.xyyreport.page.payment.suixinpin

import android.content.Context
import com.ybmmarket20.report.ReportActionSearchProductButtonClickBean
import com.ybmmarket20.report.ReportActionSubModuleSearchGoodsClickBean
import com.ybmmarket20.report.ReportPageSubModuleSearchGoodsExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.payment.IPaymentSuiXinPinGoods
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object PaymentSuiXinPinExposureReport {

    /**
     * 随心拼View曝光
     */
    @JvmStatic
    fun trackGoodsViewExposure(context: Context, goodsInfo: IPaymentSuiXinPinGoods) {
        SuiXinGoodsViewExposure(context, goodsInfo).track()
    }

    /**
     * 顺手买View曝光
     */
    @JvmStatic
    fun trackGoodsViewRecommendExposure(context: Context, goodsInfo: IPaymentSuiXinPinGoods) {
        RecommendPayGoodsViewExposure(context, goodsInfo).track()
    }

    /**
     * 随心拼弹窗曝光
     */
    @JvmStatic
    fun trackGoodsPopWindowViewExposure(context: Context, goodsInfo: IPaymentSuiXinPinGoods) {
        SuiXinGoodsViewPopWindowExposure(context, goodsInfo).track()
    }

    /**
     * 顺手买弹窗曝光
     */
    @JvmStatic
    fun trackGoodsViewPopWindowRecommendExposure(context: Context, goodsInfo: IPaymentSuiXinPinGoods) {
        RecommendPayGoodsViewPopWindowExposure(context, goodsInfo).track()
    }

    /**
     * 随心拼View点击
     */
    @JvmStatic
    fun trackGoodsViewClick(context: Context, goodsInfo: IPaymentSuiXinPinGoods): PaymentSuiXinGoodsClick {
        return SuiXinGoodsViewClick(context, goodsInfo)
    }

    /**
     * 顺手买View点击
     */
    @JvmStatic
    fun trackGoodsViewRecommendClick(context: Context, goodsInfo: IPaymentSuiXinPinGoods): PaymentSuiXinGoodsClick {
        return RecommendPayGoodsViewClick(context, goodsInfo)
    }

    /**
     * 随心拼弹窗点击
     */
    @JvmStatic
    fun trackGoodsPopWindowViewClick(context: Context, goodsInfo: IPaymentSuiXinPinGoods): PaymentSuiXinGoodsClick {
        return SuiXinGoodsViewPopWindowClick(context, goodsInfo)
    }

    /**
     * 顺手买弹窗点击
     */
    @JvmStatic
    fun trackGoodsViewPopWindowRecommendClick(context: Context, goodsInfo: IPaymentSuiXinPinGoods): PaymentSuiXinGoodsClick {
        return RecommendPayGoodsViewPopWindowClick(context, goodsInfo)
    }
}
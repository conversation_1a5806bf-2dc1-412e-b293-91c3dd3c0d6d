package com.ybmmarket20.xyyreport.page.payment.suixinpin

open class SuiXinPinState

object SuiXinPinSXP: SuiXinPinState()
object SuiXinPinSSM: SuiXinPinState()

object SuiXinPinStateCheck {
    fun checkoutState(state: SuiXinPinState, iState: ISuiXinPinStateState) {
        if (state is SuiXinPinSXP) {
            iState.onSuiXinPin()
        } else iState.onRecommendPay()
    }
}

interface ISuiXinPinStateState {
    fun onSuiXinPin()
    fun onRecommendPay()
}
package com.ybmmarket20.xyyreport.page.mainFrequently

import android.content.Context
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.SpmUtil

object MainFrequentlyEvent {

    /**
     * 页面曝光
     */
    @JvmStatic
    fun pv(context: Context) {
        SpmUtil.checkAnalysisContext(context) {
            val pageExposureBean = ReportPageExposureBean()
            val spm = SpmUtil.getSpmPv("oftenBuySearch_0-0_0")
            SpmLogUtil.print("pv-常购常搜")
            ReportUtil.pvTrack(context, pageExposureBean, spm)
        }
    }

}
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_140"
    android:background="@color/white">

    <ImageView
        android:id="@+id/ivStandardGoods"
        android:layout_width="@dimen/dimen_dp_120"
        android:layout_height="@dimen/dimen_dp_120"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_placeholder" />

    <TextView
        android:id="@+id/tvStandardGoodsTitle"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_16"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivStandardGoods"
        app:layout_constraintTop_toTopOf="@+id/ivStandardGoods"
        tools:text="双蚁 复方感冒灵颗粒双蚁 复方感冒灵颗粒(双蚁)/14g*10" />

    <TextView
        android:id="@+id/tvStandardGoodsManufacturer"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_12"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:lines="1"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivStandardGoods"
        app:layout_constraintTop_toBottomOf="@+id/tvStandardGoodsTitle"
        tools:text="广西双蚁药业有限公司" />

    <TextView
        android:id="@+id/tvStandardGoodsPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_20"
        android:layout_marginBottom="@dimen/dimen_dp_5"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/tvStandardGoodsSaleCount"
        app:layout_constraintStart_toEndOf="@+id/ivStandardGoods"
        tools:text="¥16.90 ~ 25.06" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:layout_width="@dimen/dimen_dp_57"
        android:layout_height="@dimen/dimen_dp_22"
        android:textColor="@color/color_00b377"
        android:textSize="@dimen/dimen_dp_12"
        android:gravity="center"
        android:text="选购 >"
        android:textStyle="bold"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/tvStandardGoodsPrice"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvStandardGoodsPrice"
        app:rv_cornerRadius="@dimen/dimen_dp_11"
        app:rv_strokeColor="@color/color_00b377"
        app:rv_strokeWidth="@dimen/dimen_dp_1" />

    <TextView
        android:id="@+id/tvStandardGoodsSaleCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:drawableStart="@drawable/icon_list_item_shop"
        android:textSize="@dimen/dimen_dp_11"
        android:textColor="@color/color_676773"
        app:layout_constraintBottom_toBottomOf="@+id/ivStandardGoods"
        app:layout_constraintStart_toEndOf="@+id/ivStandardGoods"
        tools:text=" 1003个商家在售" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/color_f7f7f8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10" />

</androidx.constraintlayout.widget.ConstraintLayout>
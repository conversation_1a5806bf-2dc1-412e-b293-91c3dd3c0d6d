<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="6"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="60dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="退款原因"
            android:textColor="@color/text_292933"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/iv_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="12dp"
            android:src="@drawable/icon_detail_service_close" />

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_refund"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="6"
        tools:itemCount="7"
        tools:listitem="@layout/item_refund_optimize" />

    <TextView
        android:id="@+id/tv_btn_ok"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/base_colors"
        android:gravity="center"
        android:text="确定"
        android:textColor="@color/white"
        android:textSize="16sp" />

</LinearLayout>
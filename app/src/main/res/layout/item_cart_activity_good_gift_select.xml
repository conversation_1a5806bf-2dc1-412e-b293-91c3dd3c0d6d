<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:layout_height="wrap_content"
    android:layout_width="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:id="@+id/cl_root"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_38"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_height="30dp"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/shape_cart_goods_gift_item">

        <TextView
            android:id="@+id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="9dp"
            android:layout_marginVertical="6dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="选择赠品"
            android:textSize="12dp"
            android:textColor="@color/text_color_333333"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@drawable/icon_cart_arrow"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="9dp"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
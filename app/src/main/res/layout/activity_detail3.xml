<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/ll_detail"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/header_height" />

            <LinearLayout
                android:id="@+id/ll_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                # 商品详情头部通知栏

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_top_tips"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:background="@drawable/bg_product_detail_top_tips"
                    android:visibility="gone"
                    tools:visibility="visible">


                    <TextView
                        android:id="@+id/tv_top_tips_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="85dp"
                        android:layout_marginTop="9dp"
                        android:text="智鹿用户专享"
                        android:textColor="#ffffffff"
                        android:textSize="14dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    # 倒计时控件

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="39dp"
                        android:layout_marginRight="17dp"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_top_tips_title1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="距结束仅剩  "
                            android:textColor="#ffffffff"
                            android:textSize="12dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_tip_countdown_h"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/bg_count_down"
                            android:gravity="center"
                            android:textColor="#005cb9"
                            android:textSize="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@id/tv_top_tips_title1"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="11" />

                        <TextView
                            android:id="@+id/tv_tip_countdown_split1"
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_marginLeft="2dp"
                            android:text=":"
                            android:textColor="#ffffff"
                            android:textSize="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@id/tv_tip_countdown_h"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_tip_countdown_m"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_marginLeft="2dp"
                            android:background="@drawable/bg_count_down"
                            android:gravity="center"
                            android:textColor="#005cb9"
                            android:textSize="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@id/tv_tip_countdown_split1"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="20" />

                        <TextView
                            android:id="@+id/tv_tip_countdown_split2"
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_marginLeft="2dp"
                            android:text=":"
                            android:textColor="#ffffff"
                            android:textSize="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@id/tv_tip_countdown_m"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_tip_countdown_s"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_marginLeft="2dp"
                            android:background="@drawable/bg_count_down"
                            android:gravity="center"
                            android:textColor="#005cb9"
                            android:textSize="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@id/tv_tip_countdown_split2"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="59" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.ybmmarket20.view.ViewPagerSlide
                    android:id="@+id/vp_client"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white" />
            </LinearLayout>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/header_height"
            android:background="@drawable/bg_goods_details_title"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:gravity="center">

                <ImageView
                    android:id="@+id/title_left_b"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:scaleType="center"
                    android:src="@drawable/icon_detail_left_b" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="4">

                <com.flyco.tablayout.SlidingTabLayout
                    android:id="@+id/ps_tab"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:background="#00FFFFFF"
                    android:minWidth="200dp"
                    android:orientation="horizontal"
                    android:paddingStart="@dimen/dimen_dp_15"
                    android:paddingEnd="@dimen/dimen_dp_15"
                    app:tl_indicator_color="@color/base_colors_new"
                    app:tl_indicator_corner_radius="2dp"
                    app:tl_indicator_height="4dp"
                    app:tl_indicator_margin_bottom="6dp"
                    app:tl_indicator_width_equal_title="true"
                    app:tl_tab_space_equal="true"
                    app:tl_textAllCaps="true"
                    app:tl_textBold="BOTH"
                    app:tl_textSelectColor="@color/text_292933"
                    app:tl_textSelectSize="18sp"
                    app:tl_textUnselectColor="@color/text_9494A6"
                    app:tl_textsize="16sp" />

                <LinearLayout
                    android:id="@+id/detail_ll_collect"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    tools:visibility="gone"
                    android:visibility="gone"
                    android:orientation="vertical">

                    <CheckBox
                        android:id="@+id/collect_cb"
                        style="@style/shoucangCheckboxTheme3"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:clickable="false" />

                </LinearLayout>

<!--                <ImageView-->
<!--                    android:id="@+id/iv_normal_share"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:visibility="gone"-->
<!--                    tools:visibility="visible"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:src="@drawable/icon_pdf_share" />-->

                <LinearLayout
                    android:id="@+id/ll_suixinpin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/iv_kefu_suixinpin"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dimen_dp_10"
                        android:src="@drawable/icon_service_detail" />

                    <ImageView
                        android:id="@+id/iv_share_suixinpin"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/icon_pdf_share" />

                </LinearLayout>

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_detail_more"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/tv_menu_b"
                    android:layout_width="@dimen/dimen_dp_22"
                    android:layout_height="@dimen/dimen_dp_22"
                    android:layout_centerInParent="true"
                    android:src="@drawable/icon_goods_detail_right" />

            </RelativeLayout>
        </LinearLayout>
        <!-- 扫一扫无结果展示无商品倒计时空布局 -->
        <ViewStub
            android:id="@+id/vs_no_result"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout="@layout/layout_no_result_detail" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_full_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:clickable="true"
            android:background="@color/black">
            
            <FrameLayout
                android:id="@+id/fl_full_container"
                android:layout_width="match_parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="300dp" />
            
            <ImageView
                android:id="@+id/iv_video_close"
                android:layout_width="@dimen/dimen_dp_25"
                android:layout_height="@dimen/dimen_dp_25"
                android:src="@drawable/icon_full_video_close"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginStart="@dimen/dimen_dp_15"
                android:layout_marginTop="@dimen/dimen_dp_30" />
            
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

</LinearLayout>

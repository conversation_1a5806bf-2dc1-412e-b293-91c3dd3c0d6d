<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:divider="@drawable/divider_line_gred_1dp"
        android:orientation="vertical"
        android:showDividers="middle">

        <LinearLayout
            android:id="@+id/ll_api"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_btn_text"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/tv_api"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="保存网络请求"
                android:textColor="#222222"
                android:textSize="16sp" />

            <CheckBox
                android:id="@+id/cb_api"
                style="@style/addressCheckboxTheme"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_crash"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_btn_text"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/tv_crash"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="保存应用崩溃"
                android:textColor="#222222"
                android:textSize="16sp" />

            <CheckBox
                android:id="@+id/cb_crash"
                style="@style/addressCheckboxTheme"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_device"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_btn_text"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/tv_device"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="查看设备信息"
                android:textColor="#222222"
                android:textSize="16sp" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_api_url"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_btn_text"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/tv_api_url"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="设置接口Host"
                android:textColor="#222222"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_set_x5_inspect"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_btn_text"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/tv_set_x5_inspect"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="设置X5WebView Inspect"
                android:textColor="#222222"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_get_qt_device_id"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_btn_text"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/tv_get_qt_device_id"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="qt设备id"
                android:textColor="#222222"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_get_qt_session"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_btn_text"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/tv_get_qt_session"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="qtSession"
                android:textColor="#222222"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llQtAllTrackSwitch"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_btn_text"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/tvQtAllTrackSwitch"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="qt全埋点开关"
                android:textColor="#222222"
                android:textSize="16sp" />

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/smQtAllTrackSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <Button
            android:id="@+id/btnTrack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="埋点采样率"/>
    </LinearLayout>
</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F7F7F7"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/ll_pwd"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/page_ic_password"
                    android:text="修改密码" />

                <ImageView
                    android:id="@+id/iv_else_icon"
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />

                <TextView
                    android:id="@+id/iv_else_pwd_mention"
                    style="@style/about_layout_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toLeftOf="@id/iv_else_icon"
                    android:textColor="@color/text_676773"
                    android:textSize="13dp"
                    tools:text="ddddd" />
            </RelativeLayout>

            <View style="@style/address_line" />

            <RelativeLayout
                android:id="@+id/llAccountRela"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/ic_account_rela"
                    android:text="账号关联" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="绑定微信账号，快捷登录"
                    android:textColor="@color/color_9494A5"
                    android:textSize="@dimen/dimen_dp_13"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/dimen_dp_20"
                    android:layout_alignRight="@+id/ivLinkAccount" />

                <ImageView
                    android:id="@+id/ivLinkAccount"
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ll_msg"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/page_ic_inform"
                    android:text="消息通知" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View style="@style/address_line" />

            <RelativeLayout
                android:id="@+id/ll_notification"
                style="@style/about_layout">

                <TextView
                    android:id="@+id/ll_notification_tv"
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/page_ic_notification"
                    android:text="通知栏快捷设置" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View style="@style/address_line" />

            <RelativeLayout
                android:id="@+id/rl_clear_cache"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/icon_clear_cache"
                    android:text="清理缓存" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/color_F7F7F7" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/account_certification"
                style="@style/about_layout">

                <TextView
                    android:id="@+id/title"
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/certification_icon"
                    android:text="被委托人信息认证"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.ybmmarket20.common.widget.RoundedImageView
                    android:id="@+id/iv_remind_flag"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_marginLeft="4dp"
                    android:src="#FF2121"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@+id/title"
                    app:layout_constraintTop_toTopOf="parent"
                    app:riv_oval="true"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_certification"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginRight="10dp"
                    android:drawableRight="@drawable/right_new"
                    android:gravity="center_vertical"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="去认证" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View style="@style/address_line" />

            <RelativeLayout
                android:id="@+id/ll_permission"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/permission"
                    android:text="系统权限" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/color_F7F7F7" />

            <RelativeLayout
                android:id="@+id/rl_user_protocol"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/icon_privacy_protocol"
                    android:text="用户服务协议" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View style="@style/address_line" />

            <RelativeLayout
                android:id="@+id/rl_privacy_protocol"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/icon_user_protocol"
                    android:text="隐私政策" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View style="@style/address_line" />

            <RelativeLayout
                android:id="@+id/rl_privacy_protocol_setting"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/icon_recommend_manager"
                    android:text="推荐管理" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View
                android:id="@+id/rl_real_name_divider"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                tools:visibility="visible"
                android:visibility="gone"
                android:background="@color/color_F7F7F7" />

            <RelativeLayout
                android:id="@+id/rl_real_name"
                tools:visibility="visible"
                android:visibility="gone"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/icon_real_name_authentication"
                    android:text="实名认证" />

                <TextView
                    android:id="@+id/tv_real_name_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="已认证"
                    android:textColor="#9494A6"
                    android:textSize="@dimen/dimen_dp_12"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/dimen_dp_28" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/color_F7F7F7" />


            <RelativeLayout
                android:id="@+id/rl_certificate_info"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/icon_certificate_info"
                    android:text="证照信息" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View style="@style/address_line" />

            <RelativeLayout
                android:id="@+id/rl_unregister"
                android:visibility="gone"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/icon_unregister"
                    android:text="账号注销" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/color_F7F7F7" />
            <RelativeLayout
                android:id="@+id/rl_pay_setting"
                style="@style/about_layout">

                <TextView
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/icon_pay_set"
                    android:text="支付设置" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <View style="@style/address_line" />
            <RelativeLayout
                android:id="@+id/ll_about"
                style="@style/about_layout">

                <TextView
                    android:id="@+id/ll_about_tv1"
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/page_ic_us"
                    android:text="关于我们" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_environment"
                tools:visibility="visible"
                android:visibility="gone"
                style="@style/about_layout">

                <TextView
                    android:id="@+id/ll_environment"
                    style="@style/about_layout_tv"
                    android:drawableLeft="@drawable/page_ic_us"
                    android:text="环境切换" />

                <ImageView
                    style="@style/about_layout_iv"
                    android:src="@drawable/right_new" />
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/btn_logout"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="15dp"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:gravity="center"
        android:text="退出"
        android:textColor="@color/colors_F4333C"
        android:textSize="16sp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="2dp"
        app:rv_strokeColor="@color/colors_E4E4EB"
        app:rv_strokeWidth="0.5dp" />

</LinearLayout>
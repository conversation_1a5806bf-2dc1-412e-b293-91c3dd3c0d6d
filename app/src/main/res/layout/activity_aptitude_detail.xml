<?xml version="1.0" encoding="utf-8"?>
<!-->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>废弃<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colors_fff7ef"
        android:paddingBottom="7dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="7dp"
        android:text="@string/aptitude_tv"
        android:textColor="@color/colors_99664D"
        android:textSize="14sp" />

    <com.ybmmarket20.view.MyScrollView
        android:id="@+id/msv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/activity_bg"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/mRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/invoice_tv_F7F7F8" />
            </RelativeLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginTop="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp">

                <LinearLayout
                    android:id="@+id/ll_tv_customer_phone"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_customer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            android:text="客户经理"
                            android:textColor="#292933"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_customer_phone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:text=""
                            android:textColor="#676773"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <View
                        android:layout_width="1px"
                        android:layout_height="40dp"
                        android:layout_gravity="center_vertical"
                        android:background="#f5f5f5" />

                    <ImageView
                        android:id="@+id/iv_customer_phone"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_gravity="center_vertical"
                        android:padding="18dp"
                        android:src="@drawable/icon_aptitude_phone" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="#f5f5f5" />

                <LinearLayout
                    android:id="@+id/ll_tv_customer_phone2"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_customer2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            android:text="客户服务"
                            android:textColor="#292933"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_customer_phone2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:text="400-0505-111"
                            android:textColor="#676773"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <View
                        android:layout_width="1px"
                        android:layout_height="40dp"
                        android:layout_gravity="center_vertical"
                        android:background="#f5f5f5" />

                    <ImageView
                        android:id="@+id/iv_customer_phone2"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_gravity="center_vertical"
                        android:padding="18dp"
                        android:src="@drawable/icon_aptitude_phone" />

                </LinearLayout>

            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:layout_marginTop="7dp"
                android:text="资质续期或其他问题，请联系您的专属客户经理或拨打客服电话"
                android:textColor="#9494a6"
                android:textSize="12sp" />

        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>

    <include
        layout="@layout/layout_empty_view"
        android:visibility="gone" />

</LinearLayout>
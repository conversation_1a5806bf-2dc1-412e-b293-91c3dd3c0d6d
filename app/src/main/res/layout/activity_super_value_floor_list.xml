<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title"
            android:layout_width="0dp"
            android:layout_height="44dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:padding="10dp"
                android:src="@drawable/icon_iv_back_black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="常购清单"
                android:textColor="@color/text_color_333333"
                android:textSize="17dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_shop_car"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:padding="10dp"
                android:src="@drawable/icon_title_cart"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="@id/iv_shop_car"
                app:layout_constraintTop_toTopOf="@id/iv_shop_car"
                android:layout_marginEnd="5dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/bg_message"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="10dp"
                android:visibility="gone"
                tools:text="9+"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smart_refresh_layout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_title"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="0dp"
            android:layout_height="0dp">

            <com.ybmmarket20.view.HomeSteadyHeader
                android:layout_width="match_parent"
                android:background="@color/transparent"
                android:layout_height="@dimen/dimen_dp_70" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_list"
                android:overScrollMode="never"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <ImageView
            android:id="@+id/iv_topping"
            android:layout_width="65dp"
            android:padding="10dp"
            android:layout_marginBottom="20dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:src="@drawable/icon_topping"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_height="65dp"/>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

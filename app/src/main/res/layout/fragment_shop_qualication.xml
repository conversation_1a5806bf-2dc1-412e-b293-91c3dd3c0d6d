<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f7f7f7">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintBottom_toTopOf="@+id/v_bottom_line"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintVertical_weight="1" />

    <View
        android:id="@+id/v_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="#ddddde"
        android:layout_marginBottom="@dimen/dimen_dp_9"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tv_download_to_email" />

    <TextView
        android:id="@+id/tv_download_to_email"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_15"
        android:background="@color/color_00b377"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        android:text="下载全部到邮箱"
        app:layout_constraintBottom_toBottomOf="parent" />

    <include
        android:id="@+id/emptyView"
        layout="@layout/layout_empty_view"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
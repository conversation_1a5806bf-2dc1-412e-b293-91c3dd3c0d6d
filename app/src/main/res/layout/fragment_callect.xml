<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg"
    android:orientation="vertical">

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/crv_refresh_common"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/collect_rl"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="@color/cart_bg"
        android:clickable="true"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/collect_ll_01"
            android:layout_width="40dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/shop_check"
                style="@style/CustomCheckboxTheme"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:enabled="false"
                android:gravity="center" />

        </LinearLayout>

        <TextView
            android:id="@+id/collect_tv"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/collect_ll_01"
            android:gravity="center_vertical"
            android:text="全选"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <Button
            android:id="@+id/collect_btn"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:background="#dc0000"
            android:text="取消收藏"
            android:textColor="@color/white"
            android:textSize="17sp" />

    </RelativeLayout>

    <com.ybmmarket20.view.MyFastScrollView
        android:id="@+id/iv_fastscroll"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="@dimen/fast_scroll_view_02"
        android:layout_marginBottom="@dimen/fast_scroll_view_01" />

</RelativeLayout>
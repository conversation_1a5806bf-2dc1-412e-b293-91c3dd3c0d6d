<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/dimen_dp_10"
    android:paddingEnd="@dimen/dimen_dp_10"
    android:paddingBottom="@dimen/dimen_dp_10"
    android:layout_marginTop="@dimen/dimen_dp_10"
    android:orientation="vertical"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="确认商业收货信息"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_15"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtvCopyReceive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="一键复制"
            android:textSize="@dimen/dimen_dp_11"
            android:textColor="@color/color_292933"
            android:paddingTop="@dimen/dimen_dp_4"
            android:paddingBottom="@dimen/dimen_dp_4"
            android:paddingStart="@dimen/dimen_dp_9"
            android:paddingEnd="@dimen/dimen_dp_9"
            app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvTitle"
            app:rv_cornerRadius="@dimen/dimen_dp_2"
            app:rv_strokeColor="@color/color_ccc"
            app:rv_strokeWidth="@dimen/dimen_dp_1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_23"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_60"
            android:layout_height="wrap_content"
            android:text="收件人"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvRecipient"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_60"
            android:layout_height="wrap_content"
            android:text="收货地址"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvAddress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_60"
            android:layout_height="wrap_content"
            android:text="联系电话"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvPhone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_60"
            android:layout_height="wrap_content"
            android:text="快递说明"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvExpressTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>


</LinearLayout>
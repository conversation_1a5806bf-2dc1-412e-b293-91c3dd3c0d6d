<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#fafafa"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <!--<RadioGroup-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="50dp"-->
    <!--android:background="@color/white"-->
    <!--android:orientation="horizontal">-->

    <!--<FrameLayout-->
    <!--android:id="@+id/fl_electronic_plan"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_height="match_parent"-->
    <!--android:layout_weight="1">-->

    <!--<RadioButton-->
    <!--android:id="@+id/rb_electronic_plan"-->
    <!--android:layout_width="wrap_content"-->
    <!--android:layout_height="match_parent"-->
    <!--android:layout_gravity="center"-->
    <!--android:background="@drawable/product_rb_selector_bg"-->
    <!--android:button="@null"-->
    <!--android:checked="true"-->
    <!--android:clickable="false"-->
    <!--android:gravity="center"-->
    <!--android:text="电子计划单"-->
    <!--android:textColor="@drawable/product_base_selector_textcolor"-->
    <!--android:textSize="14sp" />-->
    <!--</FrameLayout>-->

    <!--<FrameLayout-->
    <!--android:id="@+id/fl_image_cart"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_height="match_parent"-->
    <!--android:layout_weight="1">-->

    <!--<RadioButton-->
    <!--android:id="@+id/rb_image_cart"-->
    <!--android:layout_width="wrap_content"-->
    <!--android:layout_height="match_parent"-->
    <!--android:layout_gravity="center"-->
    <!--android:background="@drawable/product_rb_selector_bg"-->
    <!--android:button="@null"-->
    <!--android:checked="false"-->
    <!--android:clickable="false"-->
    <!--android:gravity="center"-->
    <!--android:text="图片采购单"-->
    <!--android:textColor="@drawable/product_base_selector_textcolor"-->
    <!--android:textSize="14sp" />-->
    <!--</FrameLayout>-->

    <!--</RadioGroup>-->

    <com.flyco.tablayout.CommonTabLayout
        android:id="@+id/ctl"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="#ffffff"
        android:paddingBottom="5dp"
        android:paddingTop="5dp"
        app:tl_indicator_bounce_enable="false"
        app:tl_indicator_color="@color/base_colors_new"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="4dp"
        app:tl_indicator_width="42.5dp"
        app:tl_textBold="BOTH"
        app:tl_textSelectColor="@color/text_292933"
        app:tl_textSelectSize="17sp"
        app:tl_textUnselectColor="@color/color_676773"
        app:tl_textsize="15sp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f8f8f8"
        android:paddingTop="10dp">

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/crv_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/ll_create_list" />


        <LinearLayout
            android:id="@+id/ll_create_list"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="20dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/bg_btn_upload_cart_detail"
            android:gravity="center">

            <ImageView
                android:id="@+id/iv_scan_qrcode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@drawable/saoyisao"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_btn_ok"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="4dp"
                android:text="新建电子计划单"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </LinearLayout>
    </RelativeLayout>

</LinearLayout>
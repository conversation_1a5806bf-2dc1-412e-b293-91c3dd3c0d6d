<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dimen_dp_10">

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_25"
        android:gravity="center"
        android:minWidth="@dimen/dimen_dp_67"
        android:text="全部"
        android:textColor="@color/color_555"
        android:textSize="@dimen/dimen_dp_12"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:background="@drawable/selector_coupon_related_goods_price_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
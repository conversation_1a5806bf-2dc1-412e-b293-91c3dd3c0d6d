<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             xmlns:app="http://schemas.android.com/apk/res-auto"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             android:orientation="vertical">

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/guide_view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"/>

    <TextView
        android:id="@+id/guide_button"
        android:layout_width="140dp"
        android:layout_height="44dp"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="35dp"
        android:background="@drawable/shape_guide_btn"
        android:gravity="center"
        android:text="立即进入"
        android:textColor="#00B377"
        android:visibility="visible"/>
    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/btn_skip"
        android:layout_width="60dp"
        android:layout_height="33dp"
        android:layout_gravity="right"
        android:layout_marginRight="10dp"
        android:layout_marginTop="28dp"
        android:alpha="0.3"
        android:gravity="center"
        android:text="跳过"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:rv_backgroundColor="#FF000000"
        app:rv_cornerRadius="3dp"/>
</FrameLayout>
<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <TextView
        android:id="@+id/tv_recommend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:text="@string/recommend_with_symbol"
        android:textColor="@color/colors_515163"
        android:textSize="@dimen/dimen_dp_11"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="@+id/rc_recommend"
        app:layout_constraintBottom_toBottomOf="@+id/rc_recommend"
        app:layout_constraintStart_toStartOf="parent" />

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/rc_recommend"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_22"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tv_recommend"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_5" />

</merge>
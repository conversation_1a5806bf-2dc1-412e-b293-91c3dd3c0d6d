<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFAFAFA"
        android:elevation="0dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_shop_notice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:paddingBottom="@dimen/dimen_dp_5"
                android:visibility="gone"
                android:layout_marginTop="@dimen/dimen_dp_10"
                app:layout_constraintTop_toBottomOf="@+id/v_tag_divider"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_shop_notices_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/dimen_dp_10"
                    android:text="商家公告"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_15"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_find_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/dimen_dp_12"
                    android:drawableRight="@drawable/icon_shopnotice_more"
                    android:text="查看更多"
                    android:textColor="@color/color_676773"
                    android:textSize="@dimen/dimen_dp_11"
                    app:layout_constraintBaseline_toBaselineOf="@id/tv_shop_notices_title"
                    app:layout_constraintRight_toRightOf="parent" />

                <TextView
                    android:id="@+id/tv_express_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:layout_marginTop="@dimen/dimen_dp_11"
                    android:text="快递类型:"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_12"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_shop_notices_title" />

                <com.ybmmarket20.view.ShopNameWithTagView
                    android:id="@+id/snwtv_express_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_5"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_express_type"
                    app:layout_constraintStart_toEndOf="@+id/tv_express_type"
                    app:layout_constraintTop_toTopOf="@+id/tv_express_type" />

                <TextView
                    android:id="@+id/tv_order_express_remark"
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="wrap_content"
                    android:textColor="#666883"
                    android:textSize="@dimen/dimen_dp_13"
                    app:layout_constraintTop_toBottomOf="@+id/tv_express_type"
                    app:layout_constraintStart_toEndOf="@+id/tv_express_type"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginEnd="@dimen/dimen_dp_10"
                    app:layout_constraintHorizontal_weight="1"
                    tools:text="快递类型备注" />

                <TextView
                    android:id="@+id/tv_order_send_local"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_dp_6"
                    android:textColor="#666883"
                    android:textSize="@dimen/dimen_dp_13"
                    android:paddingStart="@dimen/dimen_dp_10"
                    android:paddingEnd="@dimen/dimen_dp_10"
                    app:layout_constraintTop_toBottomOf="@+id/tv_order_express_remark"
                    tools:text="发货省市：工作日早八点半晚5点半非工作日不处理" />

                <TextView
                    android:id="@+id/tv_shop_notices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_dp_6"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:paddingStart="@dimen/dimen_dp_10"
                    android:paddingEnd="@dimen/dimen_dp_10"
                    android:textColor="@color/color_666883"
                    android:textSize="@dimen/dimen_dp_13"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_order_send_local"
                    tools:text="秋天,无论在什么地方的秋天,总是好的;可是啊,北国的秋,却特别来得清,来得静,来得悲凉，不逢北国之秋，已将近十余年了。在南方每年到了秋天，总要想起陶然亭的芦花，钓鱼台的柳影，西山的虫唱，玉泉的夜月，潭柘的钟声。" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <RelativeLayout
                android:id="@+id/rl_cvp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingTop="@dimen/dimen_dp_16"
                android:visibility="gone"
                tools:visibility="visible">

                <com.ybmmarket20.view.ClipViewPager
                    android:id="@+id/vp_banner"
                    android:layout_width="match_parent"
                    android:layout_height="160dp"
                    android:layout_centerHorizontal="true" />

                <LinearLayout
                    android:id="@+id/ll_indicate"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_alignParentBottom="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp" />

            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_pop_coupon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:padding="@dimen/dimen_dp_10"
                tools:itemCount="1" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_streamer_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_dp_10"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                android:orientation="vertical" />

        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_shop_proprietary_home_floor"
            android:layout_width="@dimen/dimen_dp_76"
            android:layout_height="match_parent" />

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartrefresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_shop_proprietary_home_goods"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </LinearLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="@dimen/dp_10"
    android:paddingRight="@dimen/dp_10"
    android:layout_marginTop="30dp"
    android:paddingBottom="@dimen/dp_10"
    >
    <TextView
        android:id="@+id/tv_history_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#676773"
        android:textSize="13sp"
        tools:text="2019-07-01" />

    <com.ybmmarket20.view.taggroupview.TagContainerLayout
        android:id="@+id/tag_cl_more_history"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@android:color/white"
        app:horizontal_interval="8dp"
        app:tag_background_color="#F7F7F8"
        app:tag_clickable="true"
        app:tag_corner_radius="2dp"
        app:tag_enable_cross="true"
        app:tag_text_color="#292933"
        app:vertical_interval="8dp" />
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clSingleShopHeader"
    android:layout_marginBottom="@dimen/dimen_dp_5"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivOPSingleShopLogo"
        android:layout_width="@dimen/dimen_dp_35"
        android:layout_height="@dimen/dimen_dp_35"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginTop="@dimen/dimen_dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/logo" />

    <TextView
        android:id="@+id/tvOPSingleShopName"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_60"
        android:ellipsize="end"
        android:maxLines="1"
        android:textSize="@dimen/dimen_dp_14"
        android:text="修正药业有限公司"
        android:textColor="@color/color_292933"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivOPSingleShopLogo"
        app:layout_constraintTop_toTopOf="@+id/ivOPSingleShopLogo" />

    <TextView
        android:id="@+id/tvOPSingleShopEntry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:text="进店"
        android:textSize="@dimen/dimen_dp_11"
        android:textColor="@color/color_676773"
        android:drawableEnd="@drawable/icon_home_shop_entry_arrow"
        app:layout_constraintBottom_toBottomOf="@+id/tvOPSingleShopName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvOPSingleShopName"
        tools:ignore="UseCompatTextViewDrawableXml" />

    <com.ybmmarket20.view.ShopNameWithTagView
        android:id="@+id/tvOPSingleShopTags"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_20"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginTop="@dimen/dimen_dp_5"
        app:layout_constraintStart_toEndOf="@+id/ivOPSingleShopLogo"
        app:layout_constraintTop_toBottomOf="@+id/tvOPSingleShopName" />

</androidx.constraintlayout.widget.ConstraintLayout>
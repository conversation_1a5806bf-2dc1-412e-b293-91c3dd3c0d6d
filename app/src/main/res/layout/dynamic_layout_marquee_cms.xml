<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout xmlns:app="http://schemas.android.com/apk/res-auto"

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_item"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:gravity="center_vertical"
                android:singleLine="true"
                tools:text="药提示:" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="11dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="15dp"
                android:background="@drawable/bg_header_divider" />

            <com.ybmmarket20.view.cms.MarqueeViewCms
                android:id="@+id/marquee_view"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_weight="1"
                app:mvAnimDuration="500"
                app:mvInterval="4000"
                app:mvTextColor="@color/record_red">

            </com.ybmmarket20.view.cms.MarqueeViewCms>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:src="@drawable/icon_more_cms" />
        </LinearLayout>

    </LinearLayout>
</merge>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/color_f7f7f8">

    <include layout="@layout/common_header_items" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:paddingBottom="@dimen/dimen_dp_10">

        <TextView
            android:id="@+id/tvSelectDownloadType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_12"
            android:textSize="@dimen/dimen_dp_14"
            android:textColor="@color/color_292933"
            android:text="选择下载类型"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <com.ybmmarket20.view.DownloadRelatedAptitudeTagsView
            android:id="@+id/flReason"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvSelectDownloadType" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="@dimen/dimen_dp_16"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_10">

        <TextView
            android:id="@+id/tvMailInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_12"
            android:textSize="@dimen/dimen_dp_14"
            android:textColor="@color/color_292933"
            android:text="邮箱信息"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="收不到邮件"
            android:textSize="@dimen/dimen_dp_12"
            android:drawableEnd="@drawable/icon_aptitude_download_tips"
            android:drawablePadding="@dimen/dimen_dp_3"
            app:layout_constraintBottom_toBottomOf="@+id/tvMailInfo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvMailInfo" />

        <EditText
            android:id="@+id/etMail"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_40"
            android:layout_marginTop="@dimen/dimen_dp_11"
            android:background="@drawable/shape_aptitude_download_recode_edit"
            android:hint="请输入邮箱地址"
            android:textSize="@dimen/dimen_dp_13"
            android:paddingStart="@dimen/dimen_dp_10"
            android:textColorHint="#999999"
            android:textColor="@color/color_292933"
            app:layout_constraintTop_toBottomOf="@+id/tvMailInfo" />
        
        <ImageView
            android:id="@+id/ivEditClear"
            android:layout_width="@dimen/dimen_dp_30"
            android:layout_height="@dimen/dimen_dp_30"
            android:src="@drawable/clear_sousou"
            android:padding="@dimen/dimen_dp_5"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@+id/etMail"
            app:layout_constraintBottom_toBottomOf="@+id/etMail"
            app:layout_constraintEnd_toStartOf="@+id/tvErrorTips" />

        <TextView
            android:id="@+id/tvErrorTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="输入有误"
            android:textSize="@dimen/dimen_dp_12"
            android:textColor="#FF1D1D"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@+id/etMail"
            app:layout_constraintBottom_toBottomOf="@+id/etMail"
            app:layout_constraintEnd_toEndOf="@+id/etMail"
            android:layout_marginEnd="@dimen/dimen_dp_10" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_59"
        android:background="@color/white"
        android:paddingStart="@dimen/dimen_dp_16"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:orientation="horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAptitudeDownloadRecord"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical">

            <ImageView
                android:id="@+id/ivAptitudeDownloadRecord"
                android:layout_width="@dimen/dimen_dp_18"
                android:layout_height="@dimen/dimen_dp_18"
                android:src="@drawable/icon_aptitude_download_record"
                app:layout_constraintEnd_toEndOf="@+id/tvAptitudeDownloadRecord"
                app:layout_constraintStart_toStartOf="@+id/tvAptitudeDownloadRecord"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvAptitudeDownloadRecord"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="下载记录"
                android:textColor="@color/color_333"
                android:textSize="@dimen/dimen_dp_10"
                android:layout_marginTop="@dimen/dp_4"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivAptitudeDownloadRecord" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtvAptitudeAfterSale"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_36"
            android:layout_weight="1"
            android:textSize="@dimen/dimen_dp_14"
            android:textColor="@color/color_00b955"
            android:text="查看资质售后"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginStart="@dimen/dimen_dp_19"
            android:layout_gravity="center_vertical"
            app:rv_cornerRadius="@dimen/dimen_dp_5"
            app:rv_strokeColor="@color/color_00b955"
            app:rv_strokeWidth="@dimen/dimen_dp_1" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtvConfirm"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_36"
            android:layout_weight="1"
            android:textSize="@dimen/dimen_dp_14"
            android:textColor="@color/white"
            android:text="确定"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_gravity="center_vertical"
            app:rv_backgroundColor="#37B955"
            app:rv_cornerRadius="@dimen/dimen_dp_5" />
    </LinearLayout>
</LinearLayout>
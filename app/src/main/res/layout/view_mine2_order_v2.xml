<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/dimen_dp_10">

    <TextView
        android:id="@+id/tvMyOrderTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="我的订单"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_16"
        android:textStyle="bold"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvMyOrderEntry"
        android:layout_width="@dimen/dimen_dp_50"
        android:layout_height="@dimen/dimen_dp_30"
        android:gravity="center_vertical|right"
        android:text="全部"
        android:textSize="@dimen/dimen_dp_13"
        android:textColor="@color/color_9494A6"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:drawableEnd="@drawable/icon_arrow_right_gray"
        app:layout_constraintBottom_toBottomOf="@+id/tvMyOrderTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvMyOrderTitle" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_mine2_order"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_58"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvMyOrderTitle" />


</androidx.constraintlayout.widget.ConstraintLayout>
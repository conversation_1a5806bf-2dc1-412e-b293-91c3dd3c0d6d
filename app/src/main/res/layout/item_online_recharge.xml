<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginTop="10dp"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_recharge"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="3dp"
        android:layout_marginTop="3dp"
        android:background="@drawable/selecter_item_online_recharge"
        app:layout_constraintDimensionRatio="109:85"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:textColor="@color/black"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/tv_tips"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="¥10,000" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:paddingStart="4dp"
            android:paddingTop="2dp"
            android:paddingEnd="4dp"
            android:paddingBottom="2dp"
            android:textColor="@color/color_white_FFFFFF"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_price"
            app:rv_backgroundColor="#FC443D"
            app:rv_cornerRadius="2dp"
            tools:text="返4元红包" />

        <ImageView
            android:id="@+id/ivGoldCheck"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_gold_check"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_recommend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingStart="4dp"
        android:paddingTop="3dp"
        android:paddingEnd="4dp"
        android:paddingBottom="3dp"
        android:textColor="@color/color_FF223B"
        android:textSize="11sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="@color/colors_FFF3F3"
        app:rv_cornerRadius="3dp"
        tools:text="超值推荐"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>

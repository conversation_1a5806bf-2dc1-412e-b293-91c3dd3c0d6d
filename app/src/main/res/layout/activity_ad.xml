<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_bg">

    <ScrollView
        android:id="@+id/sv"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_ad"
                android:scaleType="centerCrop"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </FrameLayout>
    </ScrollView>

    <TextView
        android:id="@+id/tv_skip"
        android:layout_width="50dp"
        android:layout_height="30dp"
        android:layout_marginEnd="@dimen/dimen_dp_18"
        android:layout_marginTop="@dimen/dimen_dp_30"
        android:background="@drawable/bg_skip_ad"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_touchScope"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>
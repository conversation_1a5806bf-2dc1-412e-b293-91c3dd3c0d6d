<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/dimen_dp_5">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_common_tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_3"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="4" />

    <com.ybmmarket20.home.mine.Mine2CommonToolsProgressView
        android:id="@+id/mine2_common_tools_progress"
        android:layout_width="@dimen/dimen_dp_20"
        android:layout_height="@dimen/dimen_dp_6"
        app:layout_constraintTop_toBottomOf="@+id/rv_common_tools"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dimen_dp_8"
        app:barBackgroundColor="#EEEEEE"
        app:barColor="#37B378"
        app:barProgress="20"/>

</androidx.constraintlayout.widget.ConstraintLayout>
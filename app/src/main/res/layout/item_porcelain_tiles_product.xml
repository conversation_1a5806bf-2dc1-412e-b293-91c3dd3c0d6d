<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="7dp"
        android:paddingBottom="@dimen/dimen_dp_5"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_product"
            android:layout_width="75dp"
            android:scaleType="fitCenter"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintDimensionRatio="1"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:layout_height="@dimen/dimen_dp_75"/>

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="￥22.31"
            android:gravity="center"
            android:textSize="11dp"
            android:textStyle="bold"
            android:textColor="@color/color_ff2121"
            app:layout_constraintTop_toBottomOf="@+id/iv_product"
            android:layout_marginTop="@dimen/dimen_dp_3"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

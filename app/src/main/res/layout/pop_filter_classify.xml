<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/viewSpace"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_weight="1"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_search_filter_title_bg">

            <TextView
                android:id="@+id/tvSearchFilterTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_13"
                android:text="筛选"
                android:textColor="@color/color_292933"
                android:textSize="@dimen/dimen_dp_17"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivSearchFilterClose"
                android:layout_width="@dimen/dimen_dp_36"
                android:layout_height="@dimen/dimen_dp_36"
                android:layout_marginEnd="@dimen/dimen_dp_5"
                android:padding="@dimen/dimen_dp_10"
                android:src="@drawable/icon_search_filter_close"
                app:layout_constraintBottom_toBottomOf="@+id/tvSearchFilterTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvSearchFilterTitle"
                app:layout_constraintVertical_bias="0.384" />

            <View
                android:id="@+id/vSearchFilterTitleLine"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_0_5"
                android:layout_marginTop="@dimen/dimen_dp_15"
                android:background="#e7e7e7"
                app:layout_constraintTop_toBottomOf="@+id/tvSearchFilterTitle" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/shop_service_title"
                style="@style/filtrate_classify_pop_tv_title"
                android:layout_marginTop="0dp"
                android:text="服务"
                android:textSize="13.5sp" />

            <LinearLayout
                android:id="@+id/shop_service_options"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="horizontal"
                android:paddingLeft="9dp">

                <TextView
                    android:id="@+id/tvAvailable"
                    style="@style/filtrate_classify_pop_tv3"
                    android:layout_marginTop="@dimen/dimen_dp_0"
                    android:text="仅看有货" />

                <TextView
                    android:id="@+id/tvExpressSF"
                    style="@style/filtrate_classify_pop_tv3"
                    android:layout_marginLeft="9dp"
                    android:layout_marginTop="@dimen/dimen_dp_0"
                    android:text="顺丰快递" />

                <TextView
                    android:id="@+id/tvExpressJD"
                    style="@style/filtrate_classify_pop_tv3"
                    android:layout_marginLeft="9dp"
                    android:layout_marginTop="@dimen/dimen_dp_0"
                    android:text="京东快递" />

            </LinearLayout>

            <TextView
                style="@style/filtrate_classify_pop_tv_02"
                android:layout_marginTop="18dp"
                android:text="药品类型"
                android:textSize="13.5sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="9dp"
                android:paddingRight="9dp">

                <TextView
                    android:id="@+id/tvClassA"
                    style="@style/filtrate_classify_pop_tv2"
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="@dimen/dimen_dp_29"
                    android:layout_weight="1"
                    android:text="甲类OTC" />

                <TextView
                    android:id="@+id/tvClassB"
                    style="@style/filtrate_classify_pop_tv2"
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="@dimen/dimen_dp_29"
                    android:layout_marginLeft="9dp"
                    android:layout_weight="1"
                    android:text="乙类OTC" />

                <TextView
                    android:id="@+id/tvClassRX"
                    style="@style/filtrate_classify_pop_tv2"
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="@dimen/dimen_dp_29"
                    android:layout_marginLeft="9dp"
                    android:layout_weight="1"
                    android:text="处方药RX" />

                <TextView
                    android:id="@+id/tvClassOthers"
                    style="@style/filtrate_classify_pop_tv2"
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="@dimen/dimen_dp_29"
                    android:layout_marginStart="@dimen/dimen_dp_9"
                    android:layout_weight="1"
                    android:text="其他" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnReset"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@drawable/bg_filtrate_classify_btn2_reset"
                android:text="重置"
                android:textColor="@color/color_292933"
                android:textSize="16sp" />

            <Button
                android:id="@+id/btnConfirm"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@color/detail_tv_00B377"
                android:elevation="0dp"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="16sp" />

        </LinearLayout>

    </LinearLayout>
</LinearLayout>
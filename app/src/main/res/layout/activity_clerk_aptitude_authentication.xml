<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/nv"
        layout="@layout/common_header_items" />

    <TextView
        android:id="@+id/hintTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFF7EF"
        android:paddingStart="20dp"
        android:paddingTop="10dp"
        android:paddingEnd="20dp"
        android:paddingBottom="10dp"
        android:text="*请上传资质进行关联审核，审核通过后可登入药帮忙APP"
        android:textColor="#99664d"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintTop_toBottomOf="@+id/nv"
        tools:layout_editor_absoluteX="0dp" />

    <TextView
        android:id="@+id/tv_aptitude_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="资质信息"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_15"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hintTv" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toTopOf="@+id/tv_confirm"
        app:layout_constraintTop_toBottomOf="@+id/tv_aptitude_info"
        app:layout_constraintVertical_weight="1" />

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="335dp"
        android:layout_height="44dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/selector_link_shop_confirm"
        android:gravity="center"
        android:text="确定"
        android:textColor="#FFFFFF"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:enabled="false"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
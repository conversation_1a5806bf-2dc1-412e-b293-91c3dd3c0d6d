<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/line_bg_1px_bottom"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="赠品"
            android:textColor="@color/color_292933"
            android:textSize="16sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="12dp"
            android:src="@drawable/icon_detail_service_close" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:background="@drawable/line_bg_1px_bottom"
        android:orientation="horizontal"
        android:padding="@dimen/dimen_dp_10">

        <TextView
            android:id="@+id/tvTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_brand_item_type2"
            android:paddingLeft="2dp"
            android:paddingRight="2dp"
            android:text="满减"
            android:textColor="@color/white"
            android:textSize="11sp" />

        <TextView
            android:id="@+id/tvTagDes"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:layout_weight="1"
            android:textColor="@color/text_292933"
            android:textSize="14sp"
            tools:text="满10元另加9.90元即赠热销商品，赠完即止， 请在购物车点击领取满10元另加9.90元即赠热销商品，赠完即止， 请在购物车点击领取" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"/>

</LinearLayout>
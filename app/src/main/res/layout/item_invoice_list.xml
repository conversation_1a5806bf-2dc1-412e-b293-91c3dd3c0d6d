<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_invoice_information"
            android:orientation="vertical"
            android:showDividers="middle">

            <LinearLayout
                android:id="@+id/ll_invoice"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:paddingRight="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/InvoicePopTitle"
                        android:text="发票号码:" />

                    <TextView
                        android:id="@+id/tv_title_class"
                        style="@style/InvoicePopText"
                        android:layout_marginStart="@dimen/dimen_dp_10"
                        android:layout_width="wrap_content"
                        android:text="电子普通发票" />

                    <View
                        android:layout_width="@dimen/dimen_dp_0"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tv_title_class_check"
                        android:layout_width="@dimen/dimen_dp_72"
                        android:layout_height="@dimen/dimen_dp_25"
                        android:layout_marginRight="@dimen/dimen_dp_10"
                        android:gravity="center"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text="查看"
                        android:background="@drawable/shape_invoice_btn"
                        android:textColor="@color/color_292933"
                        android:textSize="13sp" />

                    <TextView
                        android:id="@+id/tv_email"
                        android:layout_width="@dimen/dimen_dp_72"
                        android:layout_height="@dimen/dimen_dp_25"
                        android:layout_marginRight="@dimen/dimen_dp_10"
                        android:gravity="center"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text="发送邮箱"
                        android:textColor="@color/color_292933"
                        android:background="@drawable/shape_invoice_btn"
                        android:visibility="gone"
                        android:textSize="13sp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:orientation="horizontal"
                android:paddingRight="10dp">

                <TextView
                    style="@style/InvoicePopTitle"
                    android:text="开票日期:" />

                <TextView
                    android:id="@+id/tv_date"
                    style="@style/InvoicePopText"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:text="2016-12-01" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:orientation="horizontal"
                android:paddingRight="10dp">

                <TextView
                    style="@style/InvoicePopTitle"
                    android:text="税        额:" />

                <TextView
                    android:id="@+id/tv_limit"
                    style="@style/InvoicePopText"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:text="6.66" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:orientation="horizontal"
                android:paddingRight="10dp">

                <TextView
                    style="@style/InvoicePopTitle"
                    android:text="价税合计:" />

                <TextView
                    android:id="@+id/tv_total"
                    style="@style/InvoicePopText"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:text="66.89" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_invoice_star"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="12dp"
            android:drawableLeft="@drawable/icon_invoice_star"
            android:text="发票类型以最终开具的发票为准"
            android:textColor="@color/text_3f3f3f"
            android:textSize="13sp"
            android:visibility="gone" />

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_invalid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right|center_vertical"
        android:paddingRight="20dp"
        android:src="@drawable/invoice_list_invalid" />

</FrameLayout>
<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/common_header_items" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsvPayment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/rebateView"
        android:layout_below="@id/ll_title"
        android:background="@color/base_bg_color"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:background="@color/colors_f5f5f5"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include layout="@layout/layout_aptitude_overdue_tip" />

            <!--地址选择-->
            <com.ybmmarket20.common.widget.RoundRelativeLayout
                android:id="@+id/ll_address"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp">

                <ImageView
                    android:id="@+id/add_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="18dp"
                    android:src="@drawable/dqddd_adress"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/iv_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="12dp"
                    android:src="@drawable/common_more" />

                <TextView
                    android:id="@+id/tv_userName"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="15dp"
                    android:layout_toLeftOf="@id/iv_more"
                    android:layout_toRightOf="@id/add_address"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/text_292933"
                    android:textSize="18dp"
                    android:textStyle="bold"
                    tools:text="收货人：小蜜 400-0101-555" />

                <TextView
                    android:id="@+id/tv_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_userName"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="18dp"
                    android:layout_toLeftOf="@id/iv_more"
                    android:layout_toRightOf="@id/add_address"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="2"
                    android:textColor="@color/text_676773"
                    android:textSize="14dp"
                    tools:text="收货地址：这里是收货地址，最多可以展示两行超过两行的情况下用省略号…" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:visibility="gone"
                    android:src="@drawable/payment_address" />
            </com.ybmmarket20.common.widget.RoundRelativeLayout>
            
            <!--支付方式选择-->
            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                android:visibility="gone"
                app:rv_cornerRadius="2dp">

                <RelativeLayout
                    style="@style/payment_item_layout"
                    android:layout_width="match_parent"
                    android:layout_height="30dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="3dp"
                        android:background="@color/white"
                        android:paddingLeft="0dp"
                        android:paddingTop="3dp"
                        android:paddingRight="4dp"
                        android:text="支付方式"
                        android:textColor="@color/text_292933"
                        android:textSize="15dp"
                        android:textStyle="bold" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_alignParentBottom="true"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/colors_f5f5f5" />

                <com.ybmmarket20.view.PayTypeLayout
                    android:id="@+id/ptl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <!--商品列表-->
            <com.ybmmarket20.common.widget.RoundRelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp"
                tools:visibility="visible">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/lv_product"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/color_f7f7f8" />
            </com.ybmmarket20.common.widget.RoundRelativeLayout>

            <!--随心拼-->
            <com.ybmmarket20.view.PaymentSpellGroupRecommendGoodsView
                android:id="@+id/spell_group_recommend_goods"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:layout_marginStart="@dimen/dimen_dp_10"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                tools:visibility="visible"
                android:visibility="gone" />
            <!--顺手买一件-->
            <com.ybmmarket20.view.PaymentBuyWithoutGoodsView
                android:id="@+id/spell_group_without_goods"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:layout_marginStart="@dimen/dimen_dp_10"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                tools:visibility="visible"
                android:visibility="gone" />

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/colors_f5f5f5"
                app:rv_cornerRadius="2dp">
                <!--发票选择-->
                <LinearLayout
                    android:id="@+id/ll_bill"
                    style="@style/payment_item_layout">

                    <TextView
                        android:id="@+id/tv_bill_type"
                        style="@style/payment_item_layout_text"
                        android:layout_width="wrap_content"
                        android:layout_weight="0"
                        android:text="@string/payment_tv20"
                        android:textSize="14dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/tv_bill_tips"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:maxLines="1"
                            android:paddingLeft="4dp"
                            android:paddingRight="2dp"
                            android:singleLine="true"
                            android:textColor="@color/text_9494A6"
                            android:textSize="15sp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_bill"
                        style="@style/payment_item_layout_text_left"
                        android:text="@string/payment_tv21"
                        android:textStyle="bold" />
                </LinearLayout>

                <!--优惠券-->
                <LinearLayout
                    android:id="@+id/ll_coupon"
                    style="@style/payment_item_layout"
                    android:layout_marginTop="0dp"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_coupon"
                        style="@style/payment_item_layout_text"
                        android:layout_width="wrap_content"
                        android:layout_weight="0"
                        android:text="@string/payment_tv05"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/tv_coupon_num"
                            style="@style/payment_item_layout_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="9dp"
                            android:layout_weight="0"
                            android:background="@drawable/payment_green_bg"
                            android:paddingLeft="4dp"
                            android:paddingTop="2dp"
                            android:paddingRight="4dp"
                            android:paddingBottom="2dp"
                            android:text=""
                            android:textColor="@color/home_back_selected"
                            android:textSize="12sp"
                            android:visibility="invisible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_coupon_num_value"
                        style="@style/payment_item_layout_text_left"
                        android:text="@string/payment_tv06"
                        android:textStyle="bold" />
                </LinearLayout>

                <!--余额-->
                <LinearLayout
                    android:id="@+id/ll_balance"
                    style="@style/payment_item_layout"
                    android:visibility="gone"
                    android:layout_marginTop="0dp">

                    <TextView
                        android:id="@+id/tv_balance"
                        style="@style/payment_item_layout_text"
                        android:layout_width="wrap_content"
                        android:layout_weight="0"
                        android:text="@string/payment_tv23"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp"
                        android:visibility="gone" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_balance_tips"
                            style="@style/payment_item_layout_text"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:drawablePadding="5dp"
                            android:gravity="center_vertical"
                            android:maxLines="1"
                            android:paddingLeft="2dp"
                            android:paddingRight="2dp"
                            android:singleLine="true"
                            android:text=""
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/iv_balance"
                            android:layout_width="26dp"
                            android:layout_height="26dp"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="10dp"
                            android:gravity="center_vertical"
                            android:padding="5dp"
                            android:src="@drawable/icon_payment_balance"
                            android:visibility="gone" />
                    </LinearLayout>

                    <CheckBox
                        android:id="@+id/balance_on_off"
                        style="@style/CustomCheckboxTheme"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />
                </LinearLayout>
                <!--红包-->
                <LinearLayout
                    android:id="@+id/ll_red_envelope"
                    style="@style/payment_item_layout"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_marginTop="0dp">

                    <TextView
                        android:id="@+id/tv_red_envelope"
                        style="@style/payment_item_layout_text"
                        android:layout_width="wrap_content"
                        android:layout_weight="0"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp"
                        android:visibility="gone" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_red_envelope_tips"
                            style="@style/payment_item_layout_text"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:drawablePadding="5dp"
                            android:gravity="center_vertical"
                            android:maxLines="1"
                            android:paddingLeft="2dp"
                            android:paddingRight="2dp"
                            android:singleLine="true"
                            android:text="xxxxxxxxxxx"
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/iv_red_envelope"
                            android:layout_width="26dp"
                            android:layout_height="26dp"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="10dp"
                            android:gravity="center_vertical"
                            android:padding="5dp"
                            android:src="@drawable/icon_payment_balance"
                            android:visibility="gone" />
                    </LinearLayout>

                    <CheckBox
                        android:id="@+id/cb_red_envelope_on_off"
                        style="@style/CustomCheckboxTheme"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false" />
                </LinearLayout>
                <!--购物金-->
                <com.ybmmarket20.common.widget.RoundConstraintLayout
                    android:id="@+id/cl_shopping_gold"
                    android:layout_width="match_parent"
                    android:layout_marginTop="10dp"
                    app:rv_backgroundColor="@color/white"
                    android:minHeight="50dp"
                    app:rv_cornerRadius="2dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/ll_virtual_money"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="18dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/dp_10"
                        android:paddingRight="10dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">
                        <TextView
                            android:id="@+id/tv_virtual_money"
                            style="@style/payment_item_layout_text"
                            android:layout_width="wrap_content"
                            android:layout_weight="0"
                            android:text="@string/payment_tv23"
                            android:textColor="@color/text_292933"
                            android:textSize="14sp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_virtual_money_tips"
                                style="@style/payment_item_layout_text"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:drawablePadding="5dp"
                                android:gravity="center_vertical"
                                android:maxLines="1"
                                android:paddingLeft="2dp"
                                android:paddingRight="2dp"
                                android:singleLine="true"
                                android:text="xxxxxxxxxxx"
                                android:textSize="14sp" />

                            <ImageView
                                android:id="@+id/iv_virtual_money"
                                android:layout_width="26dp"
                                android:layout_height="26dp"
                                android:layout_marginLeft="5dp"
                                android:layout_marginRight="10dp"
                                android:gravity="center_vertical"
                                android:padding="5dp"
                                android:src="@drawable/icon_payment_balance"
                                android:visibility="gone" />
                        </LinearLayout>

<!--                        <CheckBox-->
<!--                            android:id="@+id/cb_virtual_money_on_off"-->
<!--                            style="@style/CustomCheckboxTheme"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:checked="false" />-->

                        <CheckBox
                            android:id="@+id/cb_virtual_money_on_off"
                            android:layout_width="@dimen/dimen_dp_18"
                            android:layout_height="@dimen/dimen_dp_18"
                            android:background="@drawable/payway_bg_selector"
                            android:checked="false"
                            android:button="@null" />
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_shopping_gold"
                        android:layout_width="0dp"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginTop="10dp"
                        app:layout_constraintTop_toBottomOf="@id/ll_virtual_money"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:layout_marginHorizontal="10dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        android:layout_marginBottom="10dp"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        android:orientation="horizontal"
                        tools:listitem="@layout/item_shopping_gold_recharge"
                        android:layout_height="wrap_content"/>

                    <com.ybmmarket20.common.widget.RoundConstraintLayout
                        android:id="@+id/cl_shopping_gold_detail"
                        android:layout_width="0dp"
                        app:rv_backgroundColor="@color/color_F7F7F7"
                        android:visibility="gone"
                        app:rv_cornerRadius="2dp"
                        android:layout_marginHorizontal="10dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/ll_virtual_money"
                        android:layout_marginTop="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:layout_marginBottom="10dp"
                        android:layout_height="32dp">

                        <TextView
                            android:id="@+id/tv_shopping_gold_detail"
                            android:layout_width="wrap_content"
                            android:textSize="14dp"
                            android:textColor="@color/color_292933"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            android:layout_marginStart="8dp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintEnd_toStartOf="@id/iv_shopping_gold_detail"
                            tools:text="含购物金不可购买店铺，暂不可用"
                            android:layout_height="wrap_content"/>

                        <ImageView
                            android:id="@+id/iv_shopping_gold_detail"
                            android:layout_width="14dp"
                            app:layout_constraintStart_toEndOf="@id/tv_shopping_gold_detail"
                            app:layout_constraintEnd_toEndOf="parent"
                            android:src="@drawable/icon_shopping_gold_tips"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:layout_marginStart="6dp"
                            android:layout_height="14dp"/>

                    </com.ybmmarket20.common.widget.RoundConstraintLayout>

                </com.ybmmarket20.common.widget.RoundConstraintLayout>

                <!--留言标题-->
                <LinearLayout
                    android:id="@+id/payment_message"
                    style="@style/payment_item_layout"
                    android:layout_marginTop="0dp"
                    android:visibility="gone">

                    <TextView
                        style="@style/payment_item_layout_text"
                        android:text="@string/payment_tv09"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/payment_message_leave_et"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/payment_tv10"
                        android:imeOptions="actionGo"
                        android:maxLines="3"
                        android:padding="0dp"
                        android:textColor="#434343"
                        android:textCursorDrawable="@drawable/color_cursor"
                        android:textSize="14sp" />
                </LinearLayout>
            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.view.payType.PayTypeLayoutV2
                android:id="@+id/payType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_dp_10"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:layout_marginEnd="@dimen/dimen_dp_10" />

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp">
                <!--商品总金额-->
                <LinearLayout
                    android:id="@+id/ll_order_total"
                    style="@style/payment_item_layout">

                    <TextView
                        android:id="@+id/tv_total"
                        style="@style/payment_item_order_text_key"
                        android:text="@string/payment_tv24" />

                    <TextView
                        android:id="@+id/tv_total_num"
                        style="@style/payment_item_order_text_value"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <!--订单价格信息-->
                <LinearLayout
                    android:id="@+id/ll_order_details"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="0dp"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/dimen_dp_10">
                    <!--运费-->
                    <LinearLayout
                        android:id="@+id/ll_order_freight"
                        style="@style/payment_item_layout"
                        android:layout_marginTop="0dp">

                        <TextView
                            android:id="@+id/tv_freight"
                            style="@style/payment_item_order_text_key"
                            android:layout_width="wrap_content"
                            android:layout_weight="0"
                            android:text="运费" />

                        <TextView
                            android:id="@+id/tv_freight_num"
                            style="@style/payment_item_order_text_value"
                            android:layout_weight="1"
                            android:gravity="end|center_vertical"
                            android:textColor="@color/record_red"
                            tools:text="1234" />
                    </LinearLayout>
                    <!--满减-->
                    <LinearLayout
                        android:id="@+id/ll_order_fl"
                        style="@style/payment_item_layout"
                        android:layout_marginTop="0dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tv_order_fl"
                            style="@style/payment_item_order_text_key"
                            android:text="活动优惠" />

                        <TextView
                            android:id="@+id/tv_order_fl_num"
                            style="@style/payment_item_order_text_value"
                            android:textColor="@color/record_red" />
                    </LinearLayout>
                    <!--一口价-->
                    <LinearLayout
                        android:id="@+id/ll_order_one_price"
                        style="@style/payment_item_layout"
                        android:layout_marginTop="0dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tv_order_one_price_text"
                            style="@style/payment_item_order_text_key"
                            android:text="一口价优惠" />

                        <TextView
                            android:id="@+id/tv_order_one_price_num"
                            style="@style/payment_item_order_text_value"
                            android:textColor="@color/record_red" />
                    </LinearLayout>
                    <!--优惠券-->
                    <LinearLayout
                        android:id="@+id/ll_order_coupon"
                        style="@style/payment_item_layout"
                        android:layout_marginTop="0dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tv_order_coupon"
                            style="@style/payment_item_order_text_key"
                            android:text="优惠券" />

                        <TextView
                            android:id="@+id/tv_order_coupon_num"
                            style="@style/payment_item_order_text_value"
                            android:drawableEnd="@drawable/common_more"
                            android:drawablePadding="1dp"
                            android:textColor="@color/record_red" />
                    </LinearLayout>
                    <!--红包抵扣-->
                    <LinearLayout
                        android:id="@+id/ll_order_red_envelope"
                        style="@style/payment_item_layout"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginTop="0dp">

                        <TextView
                            android:id="@+id/tv_order_red_envelope"
                            style="@style/payment_item_order_text_key"
                            android:text="红包" />

                        <TextView
                            android:id="@+id/tv_order_red_envelope_num"
                            style="@style/payment_item_order_text_value"
                            android:textColor="@color/record_red" />
                    </LinearLayout>
                    <!--余额抵扣-->
                    <LinearLayout
                        android:id="@+id/ll_order_balance"
                        style="@style/payment_item_layout"
                        android:visibility="gone"
                        android:layout_marginTop="0dp">

                        <TextView
                            android:id="@+id/tv_order_balance"
                            style="@style/payment_item_order_text_key"
                            android:text="余额抵扣" />

                        <TextView
                            android:id="@+id/tv_order_balance_num"
                            style="@style/payment_item_order_text_value"
                            android:textColor="@color/record_red" />
                    </LinearLayout>

                    <!--购物金充值-->
                    <LinearLayout
                        android:id="@+id/ll_order_virtual_money_recharge"
                        style="@style/payment_item_layout"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginTop="0dp">

                        <TextView
                            android:id="@+id/tv_order_virtual_money_recharge"
                            style="@style/payment_item_order_text_key"
                            android:text="购物金充值" />

                        <TextView
                            android:id="@+id/tv_order_virtual_money_recharge_num"
                            style="@style/payment_item_order_text_value"
                            android:textColor="@color/record_red" />
                    </LinearLayout>

                    <!--购物金抵扣-->
                    <LinearLayout
                        android:id="@+id/ll_order_virtual_money"
                        style="@style/payment_item_layout"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginTop="0dp">

                        <TextView
                            android:id="@+id/tv_order_virtual_money"
                            style="@style/payment_item_order_text_key"
                            android:text="购物金抵扣" />

                        <TextView
                            android:id="@+id/tv_order_virtual_money_num"
                            style="@style/payment_item_order_text_value"
                            android:textColor="@color/record_red" />
                    </LinearLayout>

                    <!--返点-->
                    <LinearLayout
                        android:id="@+id/ll_order_rebate"
                        style="@style/payment_item_layout"
                        android:layout_marginTop="0dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tv_order_rebate"
                            style="@style/payment_item_order_text_key"
                            android:text="返点" />

                        <TextView
                            android:id="@+id/tv_order_rebate_num"
                            style="@style/payment_item_order_text_value"
                            android:textColor="@color/record_red" />
                    </LinearLayout>

                    <!-- 支付优惠 -->
                    <LinearLayout
                        android:id="@+id/ll_order_pay_discount"
                        style="@style/payment_item_layout"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginTop="0dp">

                        <TextView
                            android:id="@+id/tv_order_pay_discount"
                            style="@style/payment_item_order_text_key"
                            android:text="支付优惠" />

                        <TextView
                            android:id="@+id/tv_order_pay_discount_num"
                            style="@style/payment_item_order_text_value"
                            android:textColor="@color/record_red" />
                    </LinearLayout>

                    <com.ybmmarket20.common.widget.RoundTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dimen_dp_10"
                        android:layout_marginRight="@dimen/dimen_dp_10"
                        android:padding="@dimen/dimen_dp_10"
                        android:text="请确认您所加购的商品品种、数量、规格等是否无误，再进行结算；如加购商品中含近效期商品，不退不换商品，非质量问题概不退换。"
                        android:textColor="#FF99664D"
                        android:textSize="@dimen/dimen_dp_12"
                        app:rv_backgroundColor="#FFFFF7EF"
                        app:rv_cornerRadius="2dp" />
                </LinearLayout>
            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tv_pay_balance"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        android:layout_above="@+id/baseline_bottom"
        android:background="@color/colors_fff7ef"
        android:gravity="center"
        android:text=""
        android:textColor="@color/colors_99664D"
        android:textSize="14sp"
        android:visibility="gone"
        tools:visibility="visible" />

    <View
        android:id="@+id/baseline_bottom"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_above="@+id/bottomView"
        android:background="@color/colors_f5f5f5" />

    <!--V12.0.3购物金二期需求去掉底部支付渠道-->
    <com.ybmmarket20.view.PaymentBottomPayItemShowView
        android:id="@+id/bottomView"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/ll_ok"/>

    <LinearLayout
        android:id="@+id/ll_ok"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingLeft="@dimen/dimen_dp_10"
            android:paddingTop="@dimen/dimen_dp_6"
            android:paddingBottom="@dimen/dimen_dp_4">

            <TextView
                android:id="@+id/tv_pay_amount_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="应付总额："
                android:textColor="@color/text_292933"
                android:textSize="@dimen/dimen_dp_13" />

            <TextView
                android:id="@+id/tv_pay_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_pay_amount_tips"
                android:layout_marginTop="2dp"
                android:textColor="@color/color_292933"
                android:textSize="@dimen/dimen_dp_16"
                android:textStyle="bold"
                tools:text="¥182600000.50" />

            <TextView
                android:id="@+id/tv_freight_bottom_des"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_pay_amount_tips"
                android:layout_marginStart="3dp"
                android:layout_marginTop="4dp"
                android:layout_toEndOf="@+id/tv_pay_amount"
                android:textColor="@color/text_9494A6"
                android:textSize="12dp"
                android:visibility="gone"
                tools:text="(含运费¥12.00)" />
        </RelativeLayout>

        <Button
            android:id="@+id/btn_reject"
            android:layout_width="@dimen/dimen_dp_100"
            android:layout_height="match_parent"
            android:background="@color/color_ff982c"
            android:text="@string/agent_order_reject"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_ok"
            android:layout_width="@dimen/dimen_dp_120"
            android:layout_height="match_parent"
            android:background="@color/color_00B377"
            android:text="@string/payment_tv16"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16" />
        <!--确认订单-->
    </LinearLayout>

    <com.ybmmarket20.view.RebateVoucherView
        android:id="@+id/rebateView"
        android:layout_centerHorizontal="true"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_above="@id/tv_pay_balance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    
    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/cl_shopping_gold_tips"
        android:layout_width="wrap_content"
        android:translationZ="10dp"
        app:rv_backgroundColor="@color/color_B3000000"
        app:rv_cornerRadius="18dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_centerHorizontal="true"
        android:minWidth="277dp"
        android:layout_above="@id/rebateView"
        android:layout_marginHorizontal="20dp"
        android:layout_marginBottom="10dp"
        android:layout_height="35dp">

        <TextView
            android:id="@+id/tv_shopping_gold_tips"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0"
            android:layout_marginStart="20dp"
            android:textColor="@color/white"
            app:layout_constrainedWidth="true"
            android:textSize="15dp"
            android:maxLines="1"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            tools:text="购物金充值，本单最高再减20元购物金充值购物金充值，本单最高再减20元"
            app:layout_constraintEnd_toStartOf="@id/iv_shopping_gold_tips"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/iv_shopping_gold_tips"
            android:layout_width="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/anim_shopping_gold"
            android:layout_height="16dp"/>
        
    </com.ybmmarket20.common.widget.RoundConstraintLayout>

    <!--新手引导-->
    <!--<LinearLayout-->
    <!--android:id="@+id/ll_guide"-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="match_parent"-->
    <!--android:background="@drawable/pay_bill_gui"-->
    <!--android:orientation="horizontal"-->
    <!--android:visibility="gone"-->
    <!--tools:visibility="gone" />-->

</RelativeLayout>
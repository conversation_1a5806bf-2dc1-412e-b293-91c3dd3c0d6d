<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_order_detail_reject">

    <TextView
        android:id="@+id/tv_agent_order_reject_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_20"
        android:text="@string/select_reject_order_reason"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_17"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RadioGroup
        android:id="@+id/rg_agent_order_reject"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_16"
        android:layout_marginTop="@dimen/dimen_dp_11"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_agent_order_reject_title">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_no_purchase_demand"
            style="@style/radio_reject_reason"
            android:checked="true"
            android:text="@string/no_purchase_demand" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_spit_agent_order"
            style="@style/radio_reject_reason"
            android:text="@string/spit_agent_order" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_goods_count_bad"
            style="@style/radio_reject_reason"
            android:text="@string/goods_count_bad" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_other"
            style="@style/radio_reject_reason"
            android:text="@string/other" />


    </RadioGroup>

    <LinearLayout
        android:id="@+id/cl_agent_order_reject_input"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_138"
        android:layout_marginStart="@dimen/dimen_dp_16"
        android:layout_marginTop="@dimen/dimen_dp_8"
        android:layout_marginEnd="@dimen/dimen_dp_16"
        android:background="@drawable/shape_agent_order_input"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rg_agent_order_reject"
        tools:visibility="visible">

        <EditText
            android:id="@+id/et_agent_order_reject_input"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dimen_dp_8"
            android:layout_marginTop="@dimen/dimen_dp_8"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:gravity="top"
            android:hint="@string/agent_order_input_hint"
            android:textColor="@color/color_292933"
            android:textColorHint="@color/color_9494A6"
            android:maxLength="100"
            android:textSize="@dimen/dimen_dp_14" />

        <TextView
            android:id="@+id/tv_agent_order_reject_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginEnd="@dimen/dimen_dp_8"
            android:layout_marginBottom="@dimen/dimen_dp_5"
            android:text="@string/agent_order_input_count"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_14" />

    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/dimen_dp_23"
        android:background="@color/colors_DDDDDD"
        app:layout_constraintTop_toBottomOf="@+id/cl_agent_order_reject_input" />

    <TextView
        android:id="@+id/tv_agent_order_reject_cancel"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_50"
        android:layout_marginStart="@dimen/dimen_dp_16"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_17"
        app:layout_constraintEnd_toStartOf="@+id/divider_vertical"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <View
        android:id="@+id/divider_vertical"
        android:layout_width="0.5dp"
        android:layout_height="@dimen/dimen_dp_50"
        android:background="@color/colors_DDDDDD"
        app:layout_constraintEnd_toStartOf="@+id/tv_agent_order_reject_confirm"
        app:layout_constraintStart_toEndOf="@+id/tv_agent_order_reject_cancel"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <TextView
        android:id="@+id/tv_agent_order_reject_confirm"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_50"
        android:layout_marginEnd="@dimen/dimen_dp_16"
        android:gravity="center"
        android:text="@string/confirm"
        android:textColor="@color/color_00B377"
        android:textSize="@dimen/dimen_dp_17"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/divider_vertical"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

</androidx.constraintlayout.widget.ConstraintLayout>
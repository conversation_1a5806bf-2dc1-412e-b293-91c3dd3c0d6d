<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:background="@color/white"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:layout_below="@id/iv_loading"
        android:layout_marginLeft="26dp"
        android:layout_marginRight="26dp"
        android:background="@drawable/bg_comment_commit_round_green"
    />
    <TextView
        android:id="@+id/tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:text="语音输入"
        android:layout_centerInParent="true"
        android:textColor="@color/white"
        />
    <ImageView
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/tv"
        android:layout_width="16sp"
        android:layout_height="16sp"
        android:src="@drawable/icon_comment_recorder_mic"
        />


</RelativeLayout>
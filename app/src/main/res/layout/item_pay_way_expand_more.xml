<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_40"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tvExpandMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="展开更多支付方式"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="#676773"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:drawableEnd="@drawable/icon_double_arrow_down"
        android:drawablePadding="@dimen/dimen_dp_3" />
</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/rl_title"
        style="@style/header_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            style="@style/header_layout_left"
            android:src="@drawable/ic_back" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/header_layout_mid"
            android:text="资质/配送" />
    </RelativeLayout>

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="37dp"
        app:tl_indicator_color="@color/color_00b377"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="@dimen/dimen_dp_3"
        app:tl_indicator_width="@dimen/dimen_dp_20"
        app:tl_tab_space_equal="true"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/color_292933"
        app:tl_textSelectSize="@dimen/dimen_dp_15"
        app:tl_textUnselectColor="@color/color_676773"
        app:layout_constraintTop_toBottomOf="@+id/rl_title"
        app:tl_textsize="@dimen/dimen_dp_14" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_weight="1" />

</androidx.constraintlayout.widget.ConstraintLayout>
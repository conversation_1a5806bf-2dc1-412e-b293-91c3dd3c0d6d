<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <include layout="@layout/common_header_items" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_dp_15"
        android:text="很遗憾，药帮忙无法继续为您提供服务，感谢您一直以来的陪伴。注销账号前，请确保所有交易已完结且无纠纷，我们也将进行信息审核，以保证您的账号安全。"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_title" />

    <CheckBox
        android:id="@+id/check_unregister"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        android:layout_marginLeft="@dimen/dimen_dp_15"
        android:background="@drawable/checkbox_style"
        android:button="@null"
        android:checked="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_content" />

    <TextView
        android:id="@+id/tv_unregister_direction"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_10"
        android:paddingRight="@dimen/dimen_dp_15"
        android:text="申请注销及标识你自愿放弃账号内所有虚拟资产并同意《药帮忙账号注销须知》"
        android:textColor="@color/colors_9595A6"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintLeft_toRightOf="@id/check_unregister"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_content" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gp_gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="check_unregister,tv_unregister_direction" />

    <TextView
        android:id="@+id/btn_commit"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_margin="@dimen/dimen_dp_10"
        android:layout_marginLeft="5dp"
        android:background="@drawable/selector_color_green_gray"
        android:enabled="false"
        android:gravity="center"
        android:text="申请注销"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
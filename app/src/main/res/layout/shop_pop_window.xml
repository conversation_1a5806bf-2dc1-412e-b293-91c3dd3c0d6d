<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingRight="12dp">

    <LinearLayout
        android:layout_width="112dp"
        android:layout_height="181dp"
        android:background="@drawable/icon_bg"
        android:orientation="vertical">

        <TextView
            android:id="@+id/pop_home"
            android:layout_width="match_parent"
            android:layout_height="49dp"
            android:drawableLeft="@drawable/pop_home"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="22dp"
            android:paddingTop="8dp"
            android:text="首页"
            android:textColor="@color/white"
            android:textSize="15dp" />

        <View
            android:layout_width="112dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center"
            android:background="@color/gray_gray"
            android:visibility="visible" />

        <TextView
            android:id="@+id/pop_search"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:drawableLeft="@drawable/pop_search"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="22dp"
            android:text="搜索"
            android:textColor="@color/white"
            android:textSize="15dp" />

        <View
            android:layout_width="112dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center"
            android:background="@color/gray_gray"
            android:visibility="visible" />

        <TextView
            android:id="@+id/pop_message"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:drawableLeft="@drawable/pop_message"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="22dp"
            android:text="消息"
            android:textColor="@color/white"
            android:textSize="15dp" />

        <View
            android:layout_width="112dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center"
            android:background="@color/gray_gray"
            android:visibility="visible" />

        <TextView
            android:id="@+id/pop_more"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:drawableLeft="@drawable/pop_more"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="22dp"
            android:text="我的"
            android:textColor="@color/white"
            android:textSize="15dp" />

    </LinearLayout>

</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/dimen_dp_12">
        <LinearLayout
            android:id="@+id/ll_custom_coupon_parent"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_top"
                android:layout_width="match_parent"
                android:layout_height="83dp"
                android:scaleType="centerCrop"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/ll_custom_coupon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_custom_coupon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    app:layout_constraintHeight_max="282dp"
                    android:paddingStart="@dimen/dimen_dp_10"
                    android:paddingEnd="@dimen/dimen_dp_10" />

                <TextView
                    android:id="@+id/rtv_more"
                    android:layout_width="180dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/shape_fragment_coupon_dialog_more_btn_bg"
                    android:gravity="center"
                    android:text="@string/str_fragment_custom_coupon_dialog_btn_more"
                    android:textColor="@color/color_ff4244"
                    android:textSize="12dp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="gone" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <ImageView
        android:id="@+id/iv_custom_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:padding="15dp"
        android:src="@drawable/icon_fragment_custom_dialog_close" />
</LinearLayout>

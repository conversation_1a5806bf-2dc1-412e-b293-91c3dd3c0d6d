<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.ybmmarket20.activity.MyBankingActivity">
    <include layout="@layout/common_header_items" />
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="282dp"
        android:background="@drawable/icon_my_banking_top_banner_bg">
    <TextView
        android:id="@+id/tv_banner_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="38dp"
        android:textSize="17dp"
        android:textColor="#EC2727"
        android:textStyle="bold"
        android:gravity="center"
        tools:text="最高可贷100,000元"
        android:background="@drawable/icon_my_banking_top_banner_text_bg"/>
        <com.ybmmarket20.common.widget.RoundTextView
            android:layout_width="match_parent"
            android:layout_height="12dp"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius_TL="8dp"
            app:rv_cornerRadius_TR="8dp"
            android:layout_alignParentBottom="true"
            />
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_banking"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:overScrollMode="never"/>
</LinearLayout>

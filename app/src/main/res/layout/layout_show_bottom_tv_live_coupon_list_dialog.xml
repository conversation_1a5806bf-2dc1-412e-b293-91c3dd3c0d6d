<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_weight="6"
    app:rv_cornerRadius_TL="10dp"
    app:rv_cornerRadius_TR="10dp"
    android:background="@color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">


            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_292933"
                android:textSize="16sp"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:textStyle="bold"
                android:text="@string/str_tv_live_coupon_list_dialog_title"/>


            <ImageView
                android:id="@+id/product_detail_btn"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:layout_alignParentRight="true"
                android:src="@drawable/icon_detail_service_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RelativeLayout>

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/crv_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/white"
            android:overScrollMode="never"
            android:layout_marginBottom="10dp"
            />

    </LinearLayout>

</com.ybmmarket20.common.widget.RoundLinearLayout>
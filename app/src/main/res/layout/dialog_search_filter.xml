<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvSearchFilterTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="筛选"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_17"
            android:textStyle="bold"
            android:layout_marginTop="@dimen/dimen_dp_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivSearchFilterClose"
            android:layout_width="@dimen/dimen_dp_16"
            android:layout_height="@dimen/dimen_dp_16"
            android:src="@drawable/icon_search_filter_close"
            android:layout_marginEnd="@dimen/dimen_dp_15"
            app:layout_constraintBottom_toBottomOf="@+id/tvSearchFilterTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvSearchFilterTitle" />

        <View
            android:id="@+id/vSearchFilterTitleLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_0_5"
            android:background="#e7e7e7"
            android:layout_marginTop="@dimen/dimen_dp_15"
            app:layout_constraintTop_toBottomOf="@+id/tvSearchFilterTitle" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvSearchFilter"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_48"
        android:gravity="bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_reset"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/bg_filtrate_classify_btn2_reset"
            android:text="重置"
            android:textColor="@color/color_292933"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_affirm"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@color/detail_tv_00B377"
            android:elevation="0dp"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>


</LinearLayout>
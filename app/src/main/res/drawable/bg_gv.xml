<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true"><shape android:shape="rectangle">
            <stroke android:width="1.0px" android:color="@color/back_gray" />

            <gradient android:angle="270.0" android:endColor="#ffffffff" android:startColor="#ffffffff" />
        </shape></item>
    <item android:state_focused="true"><shape android:shape="rectangle">
            <gradient android:angle="270.0" android:endColor="#ffffffff" android:startColor="#ffffffff" />

            <stroke android:width="1.0px" android:color="@color/back_gray" />
        </shape></item>
    <item><shape android:shape="rectangle">
            <gradient android:angle="270.0" android:endColor="#ffffffff" android:startColor="#ffffffff" />

            <stroke android:width="1.0px" android:color="@color/back_gray" />
        </shape></item>

</selector>
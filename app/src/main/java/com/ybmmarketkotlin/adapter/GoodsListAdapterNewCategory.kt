package com.ybmmarketkotlin.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarketkotlin.adapter.goodslist.PreSpellGroupGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.PreSpellGroupGoodsListAdapterNewCategoryBindItem
import com.ybmmarketkotlin.adapter.goodslist.SpellGroupGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.SpellGroupGoodsListAdapterNewCategoryBindItem

/**
 * 分类商品列表
 */
class GoodsListAdapterNewCategory(
    data: MutableList<RowsBean>?,
    isAddCartShowPopupWindow: Boolean = false
) : GoodListAdapterNew(R.layout.item_goods_all_new, data, isAddCartShowPopupWindow) {

    constructor(iCouponEntryType: ICouponEntryType?, data: MutableList<RowsBean>?, isAddCartShowPopupWindow: Boolean = false): this(data, isAddCartShowPopupWindow) {
        mCouponEntryType = iCouponEntryType
    }

    override fun getSpellGroupItem(
        baseViewHolder: YBMBaseHolder,
        rowsBean: RowsBean
    ): SpellGroupGoodsListAdapterNewBindItem {
        return SpellGroupGoodsListAdapterNewCategoryBindItem(
            mContext,
            baseViewHolder,
            rowsBean,
            countDownTimerMap,
            this
        ).apply {
            mFlowData = <EMAIL>
            jgTrackBean = <EMAIL>
        }
    }

    override fun getPreSpellGroupItem(
        baseViewHolder: YBMBaseHolder,
        rowsBean: RowsBean
    ): PreSpellGroupGoodsListAdapterNewBindItem {
        return PreSpellGroupGoodsListAdapterNewCategoryBindItem(
            mContext,
            baseViewHolder,
            rowsBean,
            countDownTimerMap
        ).apply {
            setFlowData(flowData)
            jgTrackBean = <EMAIL>
        }
    }
}
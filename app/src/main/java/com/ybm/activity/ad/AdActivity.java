package com.ybm.activity.ad;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.github.mzule.activityrouter.annotation.Router;
import com.gyf.immersionbar.ImmersionBar;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.LoginActivity;
import com.ybmmarket20.bean.AdDataBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.CountDownTimerHomeManager;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import java.util.HashMap;

import butterknife.Bind;

/**
 * Created by mbdn on 2017/8/9.
 * 开屏广告
 * 改用 AdFragment了
 */
@Deprecated
@Router({"ybm100/ad", "adactivity"})
public class AdActivity extends AdAnalysisActivity {

    private static String ActionUrlKey = "actionUrl";
    private static String ACTION_URL = "action_url";
    private static String ACTION_ADBEAN = "action_ad";
    private static String IMAGE_URL = "image_url";
    private static String AD_SPM_STR = "ad_spm_str";
    private static String AD_SCM_STR = "ad_scm_str";
    @Bind(R.id.iv_ad)
    ImageView ivAd;
    @Bind(R.id.tv_skip)
    TextView tvSkip;
    @Bind(R.id.sv)
    ScrollView sv;
    @Bind(R.id.v_touchScope)
    View touchScope;

    private String mActionUrl;
    private String mImageUrl;
    private AdDataBean adDataBean;

    private int durationTime = 4;//广告倒计时时间4s
    private boolean isGoto = false;


    private Handler mHandler = new Handler();
    private Runnable mCountDown = new Runnable() {
        @Override
        public void run() {
            if (durationTime == 0) {
                if (!isGoto) {
                    toActivity();
                }
            } else if (durationTime > 0) {
                durationTime--;
                tvSkip.setText(durationTime + "S跳过");
                mHandler.postDelayed(this, 1000);
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
//        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,  WindowManager.LayoutParams.FLAG_FULLSCREEN);
        super.onCreate(savedInstanceState);
        ImmersionBar.with(this).init();
        ImmersionBar.with(this)
                .statusBarAlpha(0)
                .fullScreen(true);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void initData() {
        super.initData();
        Bundle bundle = getIntent().getBundleExtra("bundle");
        if (bundle != null) {
            mImageUrl = bundle.getString(IMAGE_URL);
            mActionUrl = bundle.getString(ACTION_URL);
            adDataBean = (AdDataBean) bundle.getSerializable(ACTION_ADBEAN);
            mHandler.postDelayed(mCountDown, 1000);
        } else {
            toActivity();
        }
        sv.setOnTouchListener((v, event) -> true);
        ImageHelper.with(this)
                .load(mImageUrl)
                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .dontAnimate()
                .dontTransform()
                .into(new SimpleTarget<GlideDrawable>() {
                    @Override
                    public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
//                        ivAd.setImageDrawable(resource);
                        float rat = resource.getIntrinsicWidth() * 1f / resource.getIntrinsicHeight();
                        float screenWidth = UiUtils.getScreenWidth();
                        float ivHeight = screenWidth / rat;
                        FrameLayout.LayoutParams lp = (FrameLayout.LayoutParams) ivAd.getLayoutParams();
                        lp.height = (int) ivHeight;
                        ivAd.setLayoutParams(lp);
                        ImageHelper.with(AdActivity.this)
                                .load(mImageUrl)
                                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                                .dontAnimate()
                                .dontTransform()
                                .into(ivAd);
                        trackAdComponentExposure();
                    }
                });
        tvSkip.setText(durationTime + "S跳过");
//        mHandler.postDelayed(mCountDown, 1000);

        touchScope.setOnClickListener(v -> {
            if (!StringUtil.isEmpty(mActionUrl)) {
                isGoto = true;
                if (isLogin()) {
                    Intent it = new Intent(AdActivity.this, MainActivity.class);
                    it.putExtra(ActionUrlKey, mActionUrl);
                    startActivity(it);
                    // 广告弹窗跳转点击埋点
                    HashMap<String, String> contentMap = new HashMap<>();
                    contentMap.put("actionType", "1");
                    contentMap.put("link", mActionUrl);
                    XyyIoUtil.track(XyyIoUtil.ACTION_OPENSCREEN_AD, contentMap);
                    trackAdSubModuleClick();
                } else {
                    gotoAtivity(LoginActivity.class);
                }
                finish();
            }
        });

        tvSkip.setOnClickListener(v -> {
            // 广告弹窗跳转点击埋点
            HashMap<String, String> contentMap = new HashMap<>();
            contentMap.put("actionType", "2");
            XyyIoUtil.track(XyyIoUtil.ACTION_OPENSCREEN_AD, contentMap);
            toActivity();
        });
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) touchScope.getLayoutParams();
        lp.height = UiUtils.getScreenHeight(this) / 3;
        touchScope.setLayoutParams(lp);
    }

    private synchronized void toActivity() {
        if (!CountDownTimerHomeManager.isHomeTimer()) {
            Class<? extends Activity> targetActivity = (isLogin() ? MainActivity.class : LoginActivity.class);
            gotoAtivity(targetActivity);
            AdActivity.this.overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
        }
        finish();
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_ad;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mHandler.removeCallbacks(mCountDown);
    }
}

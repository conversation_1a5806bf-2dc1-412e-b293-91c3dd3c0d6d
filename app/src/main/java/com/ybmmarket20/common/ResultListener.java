package com.ybmmarket20.common;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * Created by Administrator on 2016/7/28.
 */
public abstract class ResultListener<T> {
    protected Type type;//

    public ResultListener() {
        this.type = getGenericType(0, getClass());
    }

    public abstract void result(boolean isSuccess, String errorMsg, T t);

    //获取泛型
    private Type getGenericType(int index, Class<?> subclass) {
        Type superclass = subclass.getGenericSuperclass();
        if (!(superclass instanceof ParameterizedType)) {
            return Object.class;
        }
        Type[] params = ((ParameterizedType) superclass).getActualTypeArguments();
        if (index >= params.length || index < 0) {
            throw new RuntimeException("Index outof bounds");
        }

        if (!(params[index] instanceof Class)) {
            return Object.class;
        }
        return params[index];
    }
}

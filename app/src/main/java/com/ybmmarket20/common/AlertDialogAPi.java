package com.ybmmarket20.common;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.apkfuns.logutils.LogUtils;
import com.ybmmarket20.bean.CommonDialog;
import com.ybmmarket20.utils.RoutersUtils;

/**
 * api弹对话框
 */
public class AlertDialogAPi  extends AlertDialogEx {
    private CommonDialog commonDialog;
    public AlertDialogAPi(Context context) {
        super(context);
    }

    public CommonDialog getDialog() {
        return commonDialog;
    }

    public void setDialog(CommonDialog dialog) {
        this.commonDialog = dialog;
    }


    public void show(final CommonDialog commonDialog){
        if(commonDialog == null){
            return;
        }
        setDialog(commonDialog);
        setAutoDismiss(false);
        if (commonDialog.style%2 == 0) {//如果是偶数就是强制确认对话框
            setCancelable(false);
            setCanceledOnTouchOutside(false);
        }
        setTitle(commonDialog.title);
        setMessage(commonDialog.msg);
        if (commonDialog.isCancelDialog == 1) {
            //不可关闭
            dialog.setCancelable(false);
            dialog.setCanceledOnTouchOutside(false);
        }
        if (commonDialog.btnActions != null && commonDialog.btnActions.size() >= 1 && commonDialog.btnActions.get(0).text != null) {//一个按钮
            setCancelButton(commonDialog.btnActions.get(0).text, new AlertDialogEx.OnClickListener() {
                @Override
                public void onClick(AlertDialogEx dialog, int button) {
                    String action = commonDialog.btnActions.get(0).action;
                    dialog.dismiss();
                    if (!TextUtils.isEmpty(action)) {
                        if (commonDialog.isClosePage == 1 && mContext != null && mContext instanceof Activity) {
                            ((Activity) mContext).finish();
                        }
                        RoutersUtils.open(action);
                    }
                }
            });
        }
        if (commonDialog.btnActions != null && commonDialog.btnActions.size() >= 2 && commonDialog.btnActions.get(1).text != null) {//二个按钮
            setConfirmButton(commonDialog.btnActions.get(1).text, new AlertDialogEx.OnClickListener() {
                @Override
                public void onClick(AlertDialogEx dialog, int button) {
                    String action = commonDialog.btnActions.get(1).action;
                    dialog.dismiss();
                    if (!TextUtils.isEmpty(action)) {
                        if (commonDialog.isClosePage == 1 && mContext != null && mContext instanceof Activity) {
                            ((Activity) mContext).finish();
                        }
                        RoutersUtils.open( action);
                    }
                }
            });
        }
        show();
    }


}

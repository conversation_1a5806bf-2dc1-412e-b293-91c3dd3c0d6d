package com.ybmmarket20.common;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.GiftSkulistBean;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class AdDialog4 {
    private Context mContext = null;
    private static Dialog dialog = null;
    private View dialogView = null;
    ImageView iv1;
    ImageView iv2;
    TextView tv1;

    private SimpleDateFormat dateFormat;

    private int width = 0;
    private boolean isShowing = false;
    public String bigWheelText;
    public String bigWheelUrl;

    public AdDialog4(Context context, String bigWheelText, String bigWheelUrl) {
        this.bigWheelText = bigWheelText;
        this.bigWheelUrl = bigWheelUrl;
        this.mContext = context;
        create();
    }

    private void create() {
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext, R.style.AlertDialog);
        try {
            LayoutInflater inflater = LayoutInflater.from(mContext);
            dialogView = inflater.inflate(R.layout.ad_dialog_view4, null);
            width = (int) (dialogView.getResources().getDisplayMetrics().widthPixels + 0.5);
            dialog = builder.create();
            iv1 = (ImageView) dialogView.findViewById(R.id.iv_1);
            iv2 = (ImageView) dialogView.findViewById(R.id.iv_2);
            tv1 = (TextView) dialogView.findViewById(R.id.tv_content);

            dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        } catch (Throwable e) {
            BugUtil.sendBug(e);
            dismiss();
        }
    }

    /**
     * 设置点击屏幕区域是否隐藏对话框
     *
     * @param isCancel
     */
    public AdDialog4 setCanceledOnTouchOutside(boolean isCancel) {
        if (dialog != null)
            dialog.setCanceledOnTouchOutside(isCancel);
        return this;
    }

    //点击其实地方不能取消
    public AdDialog4 setCancelable(final boolean Enable) {
        if (dialog != null) {
            dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                @Override
                public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                    if (keyCode == KeyEvent.KEYCODE_APP_SWITCH || keyCode == KeyEvent.KEYCODE_SEARCH || keyCode == KeyEvent.KEYCODE_BACK || keyCode == KeyEvent.KEYCODE_MENU || keyCode == KeyEvent.KEYCODE_BACKSLASH) {
                        return !Enable;
                    }
                    return false;
                }
            });
        }
        return this;
    }

    public AdDialog4 setDismissListener(DialogInterface.OnDismissListener listener) {
        dialog.setOnDismissListener(listener);
        return this;
    }

    public static void showDialog(String bigWheelText, String bigWheelUrl) {
        Context context = BaseYBMApp.getAppContext();
        if (BaseYBMApp.getApp().getCurrActivity() != null) {
            context = BaseYBMApp.getApp().getCurrActivity();
        }
        dismiss();
        AdDialog4 dialog = new AdDialog4(context, bigWheelText, bigWheelUrl);
        dialog.show(bigWheelText, bigWheelUrl);
    }

    /**
     * 显示对话框
     */
    public void show(String bigWheelText, String bigWheelUrl) {
        if (TextUtils.isEmpty(bigWheelText) || TextUtils.isEmpty(bigWheelUrl)) {
            return;
        }

        tv1.setText(bigWheelText);
        iv1.setTag(R.id.tag_action, bigWheelUrl);
        iv1.setOnClickListener(new AdDialog4.ItemClick());

        iv2.setOnClickListener(new AdDialog4.ItemClick());
        show();
    }

    //通用点击跳转
    private class ItemClick implements View.OnClickListener {

        @Override
        public void onClick(final View v) {
            v.setEnabled(false);

            switch (v.getId()) {
                case R.id.iv_1:
                    String action = (String) v.getTag(R.id.tag_action);
                    if (!TextUtils.isEmpty(action)) {
                        RoutersUtils.open(action);
                    }
                case R.id.iv_2:
                    dismiss();
                    break;
            }

            v.postDelayed(new Runnable() {
                @Override
                public void run() {
                    v.setEnabled(true);
                }
            }, 500);
        }
    }

    /**
     * 显示对话框
     */
    private void show() {
        if (isShowing) {
            return;
        }
        isShowing = true;
        if (mContext == null || dialog == null || dialogView == null) {
            dismiss();
            AdDialog4.showDialog(bigWheelText, bigWheelUrl);//重新弹窗
            return;
        }
        if (mContext instanceof Activity) {
            if (((Activity) mContext).isFinishing() || ((Activity) mContext).isDestroyed()) {
                dismiss();
                AdDialog4.showDialog(bigWheelText, bigWheelUrl);//重新弹窗
                return;
            }
        }
        try {
            if (dialog != null && dialog.isShowing()) {
                dialog.dismiss();
            }
            if (dialog != null && !dialog.isShowing()) {
                dialog.show();
                dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
                dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
                dialog.getWindow().setWindowAnimations(R.style.AD_Dialog);
                dialog.setContentView(dialogView, new ViewGroup.LayoutParams(
                        width, LinearLayout.LayoutParams.MATCH_PARENT));
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }

    /**
     * 关闭对话框
     */
    public static void dismiss() {
        try {
            if (dialog != null && dialog.isShowing()) {
                dialog.dismiss();
            } else {
                dialog = null;
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }
}

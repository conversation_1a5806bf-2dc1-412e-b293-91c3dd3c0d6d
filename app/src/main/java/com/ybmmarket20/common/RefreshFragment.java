package com.ybmmarket20.common;

import android.os.Bundle;

import androidx.annotation.Nullable;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.loadmore.IPage;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarketkotlin.activity.FreightAddOnItemActivity;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * 分页Fragment
 *
 * <AUTHOR>
 * 2019-10-13
 */

public abstract class RefreshFragment<E, T extends IPage<E>> extends LazyFragment implements CommonRecyclerView.Listener {

    public int startPage = 1;
    public int page = getStartPage();
    public int size = 10;
    public int total;
    public CommonRecyclerView crv;
    public List<E> rows;
    public YBMBaseAdapter<E> mAdapter;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        if (rootView != null) {
            try {
                crv = rootView.findViewById(R.id.crv_refresh_common);
                crv.setShowAutoRefresh(false);
                crv.setLoadMoreEnable(true);
                crv.setRefreshEnable(enableRefresh());
                crv.setEnabled(true);
                crv.setListener(this);
                crv.getRecyclerView().setItemAnimator(null);
                if (rows == null) rows = new ArrayList<>();
                mAdapter = getAdapter(rows);
                crv.setAdapter(mAdapter);
                getAdapter(rows).setEnableLoadMore(true);
//				crv.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, getResources().getString(R.string.no_order_data));
                if (getEmptyMsg().isEmpty() || getEmptyImg() == -1) {
                    mAdapter.setEmptyView(getContext(), R.layout.layout_empty_view, R.drawable.icon_empty, getResources().getString(R.string.no_data));
                } else {
                    mAdapter.setEmptyView(getContext(), R.layout.layout_empty_view, getEmptyImg(), getEmptyMsg());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return rootView;
    }

    /**
     * 网络请求添加参数
     *
     * @param key
     */
    private void putParams(RequestParams params, String key, String value) {
        if (params.getParamsMap() == null) params.put(key, value);
        else if (!params.getParamsMap().containsKey(key)) params.put(key, value);
    }

    @Override
    public void loadData() {
        RequestParams params = getRequestParams();
        if (params == null) params = new RequestParams();
        if (getNotNullActivity() instanceof FreightAddOnItemActivity) {
            putParams(params, "pageNum", page + "");
            putParams(params, "pageSize", getLimit() + "");
        } else {
            putParams(params, "offset", page + "");
            putParams(params, "limit", getLimit() + "");
        }
        putParams(params, "merchantId", SpUtil.getMerchantid());
        putParams(params, "timestamp", System.currentTimeMillis() + "");
        HttpManager.getInstance().post(getUrl(), params, new BaseResponse<T>() {
            @Override
            public void onSuccess(String content, BaseBean<T> obj, T t) {
                super.onSuccess(content, obj, t);
                dismissProgress();
                crv.setRefreshing(false);
                if (obj.isSuccess() && t != null) {
                    if (page == getStartPage()) {
                        rows.clear();
                    }
                    boolean isMoreData = false;
                    beforeAddDataToList(t);
                    if (t.getRowsList() != null) {

                        rows.addAll(t.getRowsList());
                        onPreSuccess(content, obj, t);
//						isMoreData = !t.getRowsList().isEmpty();
                    }
//					if (!isMoreData && t.getPageRowSize()!=0) {
//						isMoreData = !getNoMoreDataCondition(t);
//					}
                    getAdapter(rows).notifyDataChangedAfterLoadMore(t.getRowsList() != null && t.getRowsList().size() >= size);

                    afterAddDataToList(getAdapter().getData());
                    afterData(t);
                }
                onResponseSuccess(content, obj, t);
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                dismissProgress();
                crv.setRefreshing(false);
                if (rows == null) rows = new ArrayList<>();
                getAdapter(rows).setNewData(rows);
                if (page != getStartPage()) page--;
                onResponseFailure(error);
            }

            @Override
            public BaseBean json(String content, Type type) {
                return super.json(content, RefreshFragment.this.getType());
            }
        });
    }

    public void beforeAddDataToList(T t) {

    }

    public void afterAddDataToList(List<E> rowsList) {

    }

    public void afterData(T data) {

    }

    /**
     * 是否没有更多
     *
     * @param t
     * @return
     */
    private boolean getNoMoreDataCondition(T t) {
        if (getStartPage() == 1) return t.getCurPage() < t.getTotalPages();
        else return t.getCurPage() + 1 < t.getTotalPages();
    }

    @Override
    public void onRefresh() {
        page = getStartPage();
        loadData();
    }

    @Override
    public void onLoadMore() {
        Log.i("onLoadMore_count", "onLoadMore");
        page++;
        loadData();
    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    protected abstract RequestParams getRequestParams();

    protected abstract YBMBaseAdapter<E> getAdapter(List<E> rows);

    protected <A extends YBMBaseAdapter<E>> A getAdapter() {
        return (A) mAdapter;
    }

    protected abstract Type getType();

    @Override
    protected void initTitle() {
    }

    protected String getEmptyMsg() {
        return "";
    }

    protected int getEmptyImg() {
        return -1;
    }

    public CommonRecyclerView getRecyclerView() {
        return crv;
    }

    protected void resetPage() {
        page = getStartPage();
    }

    /**
     * 获取初始
     *
     * @return
     */
    protected int getStartPage() {
        return startPage;
    }

    public void clearRows() {
        rows.clear();
        getAdapter(rows).notifyDataSetChanged();
    }

    /**
     * 开启下拉刷新
     *
     * @return
     */
    protected boolean enableRefresh() {
        return true;
    }

    /**
     * 获取每页数量
     *
     * @return
     */
    protected int getLimit() {
        return size;
    }

    protected void onResponseSuccess(String content, BaseBean<T> obj, T t) {
    }

    protected void onResponseFailure(NetError error) {
    }

    public void onPreSuccess(String content, BaseBean<T> obj, T t) {

    }
}

package com.ybmmarket20.utils.im.core.data

/**
 * <AUTHOR>
 * 消息数据
 */

const val MESSAGE_PRIORITY_DEFAULT: Int = 0
const val MESSAGE_PRIORITY_HIGH = 1
const val MESSAGE_PRIORITY_NORMAL = 2
const val MESSAGE_PRIORITY_LOW = 3

class IMMessage (
        var text:String?,               // 消息内容
        var msgId: String?,             //消息id
        var senderUserId: String?,      //发送用户id
        var groupId: String?,           //群组id
        var senderNickName: String?,    //发送者昵称
        var senderAvatar: String?,      //发送者头像
        var msgStatus: Int?,            //消息状态
        var timeStamp: Long,            //时间戳
        var isSelf: Boolean,            //是否是自己的消息
        var priority: Int,              //优先级
        var isRead: Boolean             //是否已读
) {
    constructor(): this("","", "", "", "", "", -1, 0, false, 0, false)
}
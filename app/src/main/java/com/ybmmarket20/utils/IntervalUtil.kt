package com.ybmmarket20.utils


import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @date 2020-01-10
 * @description 处理倒计时
 */

const val INTERVAL_WAIT_REGIST: Int = 1
const val INTERVAL_WAIT_UNREGIST: Int = 0

object IntervalUtil {
    private val registerList = mutableListOf<IntervalListener>()
    private var compositeDisposable: CompositeDisposable = CompositeDisposable()
    private val waitList = mutableListOf<IntervalWait>()

    /**
     * 注册监听器
     */
    fun registerInterval(listener: IntervalListener) {
        synchronized(registerList) {
            if(!registerList.contains(listener)){
                if(compositeDisposable.size()<=0) {
                    registerList.add(listener)
                    startInterval()
                } else {
                    val waitObj = IntervalWait(INTERVAL_WAIT_REGIST, listener)
                    waitList.add(waitObj)
                }
            }
        }
    }

    /**
     * 反注册监听器
     */
    fun unRegisterInterval(listener: IntervalListener) {
        synchronized(registerList) {
            if(registerList.contains(listener)) {
                val waitObj = IntervalWait(INTERVAL_WAIT_UNREGIST, listener)
                waitList.add(waitObj)
            }
        }
    }

    /**
     * 开始倒计时
     */
    private fun startInterval() {
        if (compositeDisposable.isDisposed) compositeDisposable = CompositeDisposable();
        compositeDisposable.add(Observable
                .interval(0, 1, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.computation())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe{
                    callback()
                    waitList.forEach {
                        if (it.type == INTERVAL_WAIT_UNREGIST) {
                            registerList.remove(it.obj)
                        } else {
                            registerList.add(it.obj)
                        }
                    }
                    waitList.clear()
                    if(registerList.size <= 0) {
                        compositeDisposable.dispose()
                    }
                })
    }

    private fun callback() {
        synchronized(registerList) {
            registerList.forEach {
                it.callback()
            }

        }
    }
}

data class IntervalWait(
        var type: Int, //类型 0：等待反注册，1：等待注册
        var obj: IntervalListener
)


interface IntervalListener {
    fun callback()
}
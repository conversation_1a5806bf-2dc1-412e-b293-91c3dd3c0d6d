package com.ybmmarket20.utils;

import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.wxapi.WXEntryActivity;

public class YBMWxUtil {

    private YBMWxUtil() {
    }

    private static YBMWxUtil mInstance;

    public static final int RET_CODE_SUCCESS = 1000;// 成功

    public interface SDKCallBack {
        default void sdkCallBack(int code, String res_msg){};
        default void sdkCallBackTwo(){};
        default void sdkOauthBack(String code){};
    }

    public static YBMWxUtil getInstance() {
        if (mInstance == null) {
            synchronized (YBMWxUtil.class) {
                if (mInstance == null) {
                    mInstance = new YBMWxUtil();
                }
            }
        }
        return mInstance;
    }

    public void ybmCall(SDKCallBack sdkCallBack) {
        WXEntryActivity.setSDKCallBack(sdkCallBack);
        ShareUtil.setPaySDKCallBack(sdkCallBack);
    }

    public void sendOauth(){
        final SendAuth.Req req = new SendAuth.Req();
        req.scope = "snsapi_userinfo"; // 只能填 snsapi_userinfo
        req.state = "xyy_wechat";
        YBMAppLike.mApi.sendReq(req);
    }

}

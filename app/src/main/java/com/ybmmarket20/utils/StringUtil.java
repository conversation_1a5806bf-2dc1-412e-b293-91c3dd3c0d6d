package com.ybmmarket20.utils;

import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Base64;

import androidx.annotation.NonNull;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.common.util.ConvertUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by zhuhua on 2017/6/8.
 */

public class StringUtil {
    /**
     * 判断给定字符串是否空白串 空白串是指由空格、制表符、回车符、换行符组成的字符串 若输入字符串为null或空字符串，返回true
     */
    public static boolean isEmpty(String input) {
        if (input == null || "".equals(input))
            return true;

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c != ' ' && c != '\t' && c != '\r' && c != '\n') {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断一个字符串是不是数字
     */
    public static boolean isDoubleNumber(String str) {
        try {
            Double.parseDouble(str);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 查看一个字符串是否可以转换为数字
     *
     * @param str 字符串
     * @return true 可以; false 不可以
     */
    public static boolean isStr2Num(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }


    /**
     * 小数点两位
     */
    public static String DecimalFormat2Double(Double v) {
        try {
            return String.format("%.2f", v);
        } catch (Throwable e) {
            e.printStackTrace();
            LogUtils.d(e);
        }
        return "0.00";
    }

    /**
     * 小数点两位
     */
    public static String DecimalFormat2Double(String v) {
        try {
            double vd = Double.parseDouble(v);
            return String.format("%.2f", vd);
        } catch (Throwable e) {
            e.printStackTrace();
            LogUtils.d(e);
        }
        return "0.00";
    }

    /**
     * 小数点四位
     */
    public static String DecimalFormat4Double(Double v) {
        try {
            return String.format(Locale.getDefault(), "%.4f", v);
        } catch (Throwable e) {
            e.printStackTrace();
            LogUtils.d(e);
        }
        return "0.0000";
    }

    /**
     * 去除小数点尾部的0
     * @param v
     * @return
     */
    public static String stripTrailingZeros(String v) {
        BigDecimal noZeros = new BigDecimal(v).stripTrailingZeros();
        return noZeros.toPlainString();
    }

    /***
     * 毛利率
     * **/
    public static String getGrossMargin2Double(String GrossMargin) {
        try {
            if (StringUtil.isEmpty(GrossMargin)) {
                return "0";
            }
            //毛利率 替换原有%号进行数据转换
            GrossMargin = GrossMargin.trim().replace("%", "");
            //毛利率 判断是否为空，为数字，则进行double转换，保留小数点两位
            if (!StringUtil.isEmpty(GrossMargin) && StringUtil.isDoubleNumber(GrossMargin)) {
                GrossMargin = DecimalFormat2Double(Double.parseDouble(GrossMargin)) + "%";
            }
        } catch (Exception ext) {
            ext.printStackTrace();
        }

        return GrossMargin;
    }

    /***
     * 控销价
     * **/
    public static String getUniformPrice2Double(String UniformPrice) {
        try {
            if (StringUtil.isEmpty(UniformPrice)) {
                return "0";
            }
            //控销价 判断是否为空，为数字，则进行double转换，保留小数点两位
            if (!StringUtil.isEmpty(UniformPrice) && StringUtil.isDoubleNumber(UniformPrice)) {
                UniformPrice = "¥" + DecimalFormat2Double(Double.parseDouble(UniformPrice));
            }

        } catch (Exception ext) {
            ext.printStackTrace();
        }
        return UniformPrice;
    }

    /**
     * 是否包含emoji
     *
     * @param str
     * @return true 包含
     */
    public static boolean containsEmoji(String str) {
        int len = str.length();
        for (int i = 0; i < len; i++) {
            if (isEmojiCharacter(str.charAt(i))) {
                return true;
            }
        }
        return false;
    }

    private static boolean isEmojiCharacter(char codePoint) {
        return !((codePoint == 0x0) ||
                (codePoint == 0x9) ||
                (codePoint == 0xA) ||
                (codePoint == 0xD) ||
                ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
                ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
                ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF)));

    }

    public static SpannableStringBuilder setDotAfterSize(String text, int size) {
        if (text == null) text = "";
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        if (text.contains(".")) {
            AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(ConvertUtils.dp2px(size));
            builder.setSpan(sizeSpan, text.indexOf(".") + 1, text.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        return builder;
    }

    public static SpannableString setDotAfterSize(String text, int size, int color) {
        if (TextUtils.isEmpty(text) || text.length() == 1) {
            return null;
        }
        try {
            int fobLastLength = text.indexOf(".");
            SpannableString spanText = new SpannableString(text);
            spanText.setSpan(new ForegroundColorSpan(UiUtils.getColor(color)) {

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setTextSize(UiUtils.sp2px(size));
                }

            }, 1, fobLastLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            return spanText;
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }
        return null;
    }

    public static SpannableString setDotAfterSize(String text, int size, int color, int start) {
        if (TextUtils.isEmpty(text) || text.length() == 1) {
            return null;
        }
        try {
            int fobLastLength = text.indexOf(".");
            SpannableString spanText = new SpannableString(text);
            spanText.setSpan(new ForegroundColorSpan(UiUtils.getColor(color)) {

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setTextSize(UiUtils.sp2px(size));
                }

            }, 0, fobLastLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            return spanText;
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }
        return null;
    }

    public static String hideData(String data, int beforeLength, int afterLength) {
        if (TextUtils.isEmpty(data)) {
            return data;
        }

        int length = data.length();
        String replaceSymbol = "*";
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            if (i < beforeLength || i >= (length - afterLength)) {
                sb.append(data.charAt(i));
            } else {
                sb.append(replaceSymbol);
            }
        }
        return sb.toString();
    }

    public static boolean equals(String str1, String str2) {
        if (str1 == str2) {
            return true;
        }
        if (str1 != null) {
            return str1.equals(str2);
        } else {
            return str2 == null;
        }
    }

    /**
     * 截取指定长度的字符串
     *
     * @param str 原字符串
     * @param len 长度
     * @return 如果str为null，则返回null；如果str长度小于len，则返回str；如果str的长度大于len，则返回截取后的字符串
     */
    public static String subStrByStrAndLen(String str, int len) {
        return null != str ? (str.length() > len ? str.substring(0, len) : str) : null;
    }


    /**
     * 字符Base64加密
     *
     * @param str
     * @return
     */
    public static String encodeToBase64String(String str) {
        try {
            return Base64.encodeToString(str.getBytes("UTF-8"), Base64.DEFAULT);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 字符Base64解密
     *
     * @param str
     * @return
     */
    public static String decodeBase64ToString(String str) {
        try {
            return new String(Base64.decode(str.getBytes("UTF-8"), Base64.DEFAULT));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 移除小数点尾部无用的0
     * @param str
     * @return
     */
    public static String removeZeroAfterDot(String str) {
        if (null != str && str.indexOf(".") > 0) {
            return str.replaceAll("0+?$", "")
            .replaceAll("[.]$", "");
        }
        return "0";
    }


    public static SpannableStringBuilder getSpannableSizeWithDot(String str, int preSize, int afterSize) {
        if (str == null) str = "0.00";
        if (!str.contains(".")) str+=".00";
        SpannableStringBuilder builder = new SpannableStringBuilder(str);
        builder.setSpan(new AbsoluteSizeSpan(preSize, true), 0, str.indexOf("."), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        builder.setSpan(new AbsoluteSizeSpan(afterSize, true), str.indexOf("."), str.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return builder;
    }

    /**
     * 字符串是否全是中文
     * @param str
     * @return
     */
    public static boolean isChineseString(String str) {
        if (str == null) return false;
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        return m.matches();
    }

}

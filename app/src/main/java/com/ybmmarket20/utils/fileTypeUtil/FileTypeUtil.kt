package com.ybmmarket20.utils.fileTypeUtil

import android.text.TextUtils
import okhttp3.internal.and
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.util.*

/**
 * 文件类型
 */
object FileTypeUtil {

    /**
     * 通过文件路径获取文件类型
     * @param path
     * @return FileTypeEnum - 文件类型与对应的文件魔数枚举类
     */
    fun getFileTypeByPath(path: String?): FileTypeEnum {
        // 获取文件头
        val magicNumberCode = getFileHeaderHexByPath(path)
        return if (!TextUtils.isEmpty(magicNumberCode)) {
            FileTypeEnum.getByMagicNumberCode(magicNumberCode?.toUpperCase(Locale.ROOT)?: "")
        } else FileTypeEnum.NOT_EXITS_ENUM
    }

    /**
     * 通过文件流获取文件类型
     * @param inputStream
     * @return FileTypeEnum - 文件类型与对应的文件魔数枚举类
     */
    fun getFileTypeByInputStream(inputStream: InputStream?): FileTypeEnum {
        // 获取文件头
        val magicNumberCode = getFileHeaderHexByInputStream(inputStream)
        return if (!TextUtils.isEmpty(magicNumberCode)) {
            FileTypeEnum.getByMagicNumberCode(magicNumberCode!!.toUpperCase(Locale.ROOT))
        } else FileTypeEnum.NOT_EXITS_ENUM
    }


    /**
     * 获取文件头（即文件魔数），根据文件路径
     * @param path
     * @return fileHeaderHex - 文件头，即文件魔数
     */
    fun getFileHeaderHexByPath(path: String?): String? {
        val b = ByteArray(28)
        var inputStream: InputStream? = null
        try {
            inputStream = FileInputStream(path)
            inputStream.read(b, 0, 28)
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return bytesToHexString(b)
    }

    /**
     * 获取文件头（即文件魔数），根据通过文件流
     * @param inputStream
     * @return fileHeaderHex - 文件头，即文件魔数
     */
    fun getFileHeaderHexByInputStream(inputStream: InputStream?): String? {
        val b = ByteArray(28)
        try {
            inputStream?.read(b, 0, 28)
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return bytesToHexString(b)
    }

    /**
     * 将文件二进制流（即字节数组）转换成16进制字符串数据
     * @param b
     * @return fileHeaderHex - 文件头，即文件魔数
     */
    private fun bytesToHexString(b: ByteArray?): String? {
        val stringBuilder = StringBuilder()
        if (b == null || b.isEmpty()) {
            return null
        }
        for (i in b.indices) {
            val v: Int = b[i].and(0xFF)
            val hv = Integer.toHexString(v)
            if (hv.length < 2) {
                stringBuilder.append(0)
            }
            stringBuilder.append(hv)
        }
        return stringBuilder.toString()
    }

}
package com.ybmmarket20.utils.analysis

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.utils.RoutersUtils

/**
 * <AUTHOR>
 * @date 2020-04-17
 * @description 管理流量数据管理
 */

/**
 * 开启带埋点参数的路由
 * @param url
 */
fun openUrl(url: String?, baseFlowData: BaseFlowData?): String? {
    return url?.let {
        try {
            XyyIoUtil.checkSpTypeField(baseFlowData, false)
            val resultUrl = Uri.parse(it).buildUpon()
                .appendQueryParameter("spType", baseFlowData?.spType ?: "0")
                .appendQueryParameter("spId", baseFlowData?.spId ?: "")
                .appendQueryParameter("sId", baseFlowData?.sId ?: "")
                .build()
                .toString()
            RoutersUtils.open(resultUrl)
            resultUrl
        } catch (e: Exception) {
            e.printStackTrace()
            RoutersUtils.open(url)
            url
        }
    }
}

/**
 * 开启带埋点参数的路由
 * @param url
 */
fun getOpenUrlNotJump(url: String?, baseFlowData: BaseFlowData?): String? {
    return url?.let {
        try {
            XyyIoUtil.checkSpTypeField(baseFlowData, false)
            val resultUrl = Uri.parse(it).buildUpon()
                    .appendQueryParameter("spType", baseFlowData?.spType ?: "0")
                    .appendQueryParameter("spId", baseFlowData?.spId ?: "")
                    .appendQueryParameter("sId", baseFlowData?.sId ?: "")
                    .build()
                    .toString()
            resultUrl
        } catch (e: Exception) {
            e.printStackTrace()
            url
        }
    }
}

/**
 * 接收埋点参数
 */
fun receiveAnalysisParams(context: Context?, baseFlowData: BaseFlowData?) {
    var activity: Activity? = null
    if (context != null && context is Activity) activity = context
    receiveAnalysisParams(activity, baseFlowData)
}

/**
 * 接收埋点参数
 */
private fun receiveAnalysisParams(activity: Activity?, baseFlowData: BaseFlowData?) {
    val receiveIntent: Intent? = activity?.intent
    baseFlowData?.also {
        it.spType =
            receiveIntent?.getStringExtra("spType") ?: receiveIntent?.getStringExtra("sptype")
                    ?: "0"
        it.spId =
            receiveIntent?.getStringExtra("spId") ?: receiveIntent?.getStringExtra("spid") ?: ""
        it.sId = receiveIntent?.getStringExtra("sId") ?: receiveIntent?.getStringExtra("sid") ?: ""
    }
}


/**
 * 组装埋点参数
 */
fun addAnalysisRequestParams(params: RequestParams?, baseFlowData: BaseFlowData?) {
    addAnalysisRequestParams(params, baseFlowData, null)
}

/**
 * 带direct组装埋点参数
 */
fun addAnalysisRequestParams(params: RequestParams?, baseFlowData: BaseFlowData?, direct: String?) {
    params?.also {
        it.put("sptype", baseFlowData?.spType ?: "0")
        it.put("spid", baseFlowData?.spId ?: "")
        it.put("sid", baseFlowData?.sId ?: "")
        if (baseFlowData?.nsid != null) {
            it.put("nsid", baseFlowData.nsid ?: "")
            it.put("sdata", baseFlowData.sdata ?: "")
        }
        if (direct != null) it.put("direct", direct)
        XyyIoUtil.checkSpTypeField(baseFlowData, false)
    }
}

/**
 * 带direct组装埋点参数
 */
fun addAnalysisRequestParams(
    params: HashMap<String, String>?,
    baseFlowData: BaseFlowData?,
    direct: String?
) {
    params?.also {
        it.put("sptype", baseFlowData?.spType ?: "0")
        it.put("spid", baseFlowData?.spId ?: "")
        it.put("sid", baseFlowData?.sId ?: "")
        if (baseFlowData?.nsid != null) {
            it.put("nsid", baseFlowData.nsid ?: "")
            it.put("sdata", baseFlowData.sdata ?: "")
        }
        if (direct != null) it.put("direct", direct)
        XyyIoUtil.checkSpTypeField(baseFlowData, false)
    }
}

/**
 * 更新flowData
 */
fun updateFlowData(
    baseFlowData: BaseFlowData?,
    spType: String?,
    spId: String?,
    sId: String?,
    nsid: String? = null
) {
    baseFlowData?.also {
        it.spType = spType ?: "0"
        it.spId = spId ?: ""
        it.sId = sId ?: ""
        it.nsid = nsid
    }
}

/**
 * 生成默认FlowData
 */
fun generateDefaultBaseFlowData(): BaseFlowData = BaseFlowData("0", "", "", null, "")

package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ConsultantBean
import com.ybmmarket20.bean.OrderDetailProductListData
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/6/20 14:48
 *    desc   :
 */
interface IConsultantService {

    @FormUrlEncoded
    @POST("getSalesInfo")
    suspend fun getSalesInfo(@Field("merchantId") merchantId: String = SpUtil.getMerchantid()): BaseBean<ConsultantBean>
}

class ConsultantServiceRequest {
    suspend fun getSalesInfo(): BaseBean<ConsultantBean> = try {
        NetworkService.instance.mRetrofit.create(IConsultantService::class.java)
            .getSalesInfo()
    } catch (e: Exception) {
        BaseBean<ConsultantBean>().initWithException(e)
    }
}
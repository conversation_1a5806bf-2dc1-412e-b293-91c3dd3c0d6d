package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.DownloadRecordBean
import com.ybmmarket20.bean.RelatedAptitudeTag
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface IDownloadRelatedAptitudeService {

    @FormUrlEncoded
    @POST("order/downQualificationRecord/getAllQualificationType")
    suspend fun getRelatedAptitudeList(@Field("orderNo")orderNo: String, @Field("orgId")orgId: String, @Field("merchantId")merchantId: String): BaseBean<List<RelatedAptitudeTag>>

    @FormUrlEncoded
    @POST("order/downQualificationRecord/sendQualificationToEmail")
    suspend fun submitDownloadAptitude(@FieldMap params: Map<String, String>): BaseBean<Any>

    @FormUrlEncoded
    @POST("order/downQualificationRecord/getList")
    suspend fun getAptitudeDownloadRecordList(@Field("orderNo")orderNo: String): BaseBean<List<DownloadRecordBean>>
}

class DownloadRelatedAptitudeRequest {

    suspend fun getRelatedAptitudeList(orderNo: String, orgId: String, merchantId: String): BaseBean<List<RelatedAptitudeTag>> = try {
        NetworkService.instance.mRetrofit.create(IDownloadRelatedAptitudeService::class.java).getRelatedAptitudeList(orderNo, orgId, merchantId)
    } catch (e: Exception) {
        BaseBean<List<RelatedAptitudeTag>>().initWithException(e)
    }

    suspend fun submitDownloadAptitude(params: Map<String, String>): BaseBean<Any> = try {
        NetworkService.instance.mRetrofit.create(IDownloadRelatedAptitudeService::class.java).submitDownloadAptitude(params)
    } catch (e: Exception) {
        BaseBean<Any>().initWithException(e)
    }

    suspend fun getAptitudeDownloadRecordList(orderNo: String): BaseBean<List<DownloadRecordBean>> = try {
        NetworkService.instance.mRetrofit.create(IDownloadRelatedAptitudeService::class.java).getAptitudeDownloadRecordList(orderNo)
    } catch (e: Exception) {
        BaseBean<List<DownloadRecordBean>>().initWithException(e)
    }

}
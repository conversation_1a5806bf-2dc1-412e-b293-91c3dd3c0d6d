package com.ybmmarket20.adapter;

import android.text.TextUtils;

import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.TransactionDetailsChildrenBean;
import com.ybmmarket20.utils.UiUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class InterestSubsidyAdapter extends YBMBaseMultiItemAdapter<TransactionDetailsChildrenBean> {

    private SimpleDateFormat mFormat;

    public InterestSubsidyAdapter(List<TransactionDetailsChildrenBean> data) {
        super(data);
        mFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        addItemType(TransactionDetailsChildrenBean.ITEMTYPE_HEAD, R.layout.medicine_ious_head);
        addItemType(TransactionDetailsChildrenBean.ITEMTYPE_CONTENT, R.layout.medicine_ious_content);
    }

    @Override
    protected void bindItemView(YBMBaseHolder baseViewHolder, TransactionDetailsChildrenBean bean) {
        switch (bean.getItemType()) {
            case TransactionDetailsChildrenBean.ITEMTYPE_HEAD:
                bindHead(baseViewHolder, bean);
                break;
            case TransactionDetailsChildrenBean.ITEMTYPE_CONTENT:
                bindContent(baseViewHolder, bean);
                break;
        }
    }

    private void bindContent(YBMBaseHolder baseViewHolder, TransactionDetailsChildrenBean bean) {

        baseViewHolder.setText(R.id.tv_content_order_no, "订单:" + bean.getOrderNo())
                .setText(R.id.tv_content_money, UiUtils.transform(bean.getActualInterest()))
                .setText(R.id.tv_content_time, "返息时间：" + mFormat.format(new Date(bean.getCreateTime())))
                .setText(R.id.tv_content_state, !TextUtils.isEmpty(bean.getTransStatusName()) ? bean.getTransStatusName() : "返息成功");
    }

    private void bindHead(YBMBaseHolder baseViewHolder, TransactionDetailsChildrenBean bean) {

        baseViewHolder.setText(R.id.tv_head_time, bean.getDate())
                .setText(R.id.tv_head_money, "共" + bean.getSize() + "笔，合计：" + UiUtils.transform(bean.getTotalAmount()));

    }
}
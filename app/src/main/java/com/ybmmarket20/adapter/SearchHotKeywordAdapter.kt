package com.ybmmarket20.adapter

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R

/**
 * <AUTHOR>
 * @date 2019-12-27
 * @description
 */
class SearchHotKeywordAdapter(
        layoutId: Int,
        data: List<String>
): YBMBaseAdapter<String>(layoutId, data) {
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: String?) {
        baseViewHolder?.run {
            getView<TextView>(R.id.rtv_hot_keyword)?.text = t
        }
    }
}
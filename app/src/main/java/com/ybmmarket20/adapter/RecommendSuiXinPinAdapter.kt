package com.ybmmarket20.adapter

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams
import com.ybmmarket20.bean.RecommendSuiXinPinBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SpellGroupGoodsItem
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.ProductEditLayoutSuiXinPin
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.xyyreport.page.payment.suixinpin.SuiXinPinSXP
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import kotlin.math.absoluteValue

//随心拼弹窗
class RecommendSuiXinPinAdapter(
    goodsList: List<RowsBean>,
    val onAddCartListener: (RecommendSuiXinPinBean) -> Unit,
    val mViewModel: SpellGroupRecommendGoodsViewModel
) : PaymentSuiXinPinAnalysisPopWindowAdapter<RowsBean>(
    R.layout.item_spell_group_recommend_goods, goodsList, SuiXinPinSXP) {
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RowsBean) {
        super.bindItemView(baseViewHolder, t)
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            if (bean.actSuiXinPin == null) return@whenAllNotNull
            ImageUtil.load(
                mContext,
                AppNetConfig.LORD_IMAGE + bean.imageUrl,
                holder.getView(R.id.iv_goods)
            )
//            ImageUtil.loadNoPlace(
//                mContext,
//                AppNetConfig.LORD_TAG + bean.goodsTagUrl,
//                holder.getView(R.id.iv_goods_tag)
//            )
//            holder.setText(R.id.tv_goods_title, bean.showName)
            val goodsTitle = holder.getView<TextView>(R.id.tv_goods_title)
            val tagList = ArrayList<TagBean>()
            bean.tagList?.forEach {
                val tagBean = TagBean();
                tagBean.uiStyle = it.uiStyle
                tagBean.text = it.text ?: it.name ?: ""
                tagBean.bgColor = it.bgColor
                tagBean.textColor = it.textColor
                tagBean.borderColor = it.borderColor
                tagBean.description = it.description
                tagBean.appUrl = it.appURL
                tagList.add(tagBean)
            }
            goodsTitle.TextWithPrefixTag(tagList, bean.showName)
            goodsTitle.setLineSpacing(0f, 1.1f)
            if(bean.promoTag.isNullOrEmpty()){
                holder.getView<TextView>(R.id.tv_promo_tag).visibility = View.GONE
            }else{
                holder.getView<TextView>(R.id.tv_promo_tag).visibility = View.VISIBLE
                holder.getView<TextView>(R.id.tv_promo_tag).text = bean.promoTag
            }
            holder.setText(R.id.tv_effect, "有效期：" + bean.nearEffect)
            //设置价格样式
            val priceBuilder =
                UiUtils.getPriceWithFormat(
                    "¥${UiUtils.transform(bean.actSuiXinPin.suiXinPinPrice)}",
                    11
                )
            priceBuilder.setSpan(
                StyleSpan(Typeface.BOLD),
                0,
                priceBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            val unitBuilder = SpannableStringBuilder("/${bean.productUnit} ")
            unitBuilder.setSpan(
                    ForegroundColorSpan(
                            ContextCompat.getColor(
                                    mContext,
                                    R.color.color_676773
                            )
                    ), 0, unitBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            unitBuilder.setSpan(
                AbsoluteSizeSpan(11, true),
                0,
                unitBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            priceBuilder.append(unitBuilder)
            if (bean.fob.toString().isNotEmpty() && !UiUtils.transform(bean.fob)
                    .equals(UiUtils.transform(bean.actSuiXinPin.suiXinPinPrice))
            ) {
                val originalPriceBuilder =
                    SpannableStringBuilder("¥${bean.fob ?: ""}")
                originalPriceBuilder.setSpan(
                    AbsoluteSizeSpan(11, true),
                    0,
                    originalPriceBuilder.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                originalPriceBuilder.setSpan(
                    ForegroundColorSpan(
                        ContextCompat.getColor(
                            mContext,
                            R.color.color_676773
                        )
                    ), 0, originalPriceBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                originalPriceBuilder.setSpan(
                    StrikethroughSpan(),
                    0,
                    originalPriceBuilder.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                priceBuilder.append(originalPriceBuilder)
            }
            holder.setText(R.id.tv_price, priceBuilder)
            //加购
            val pel = holder.getView<ProductEditLayoutSuiXinPin>(R.id.pel)
            pel.bindData(
                "${bean.id}",
                1,
                true,
                true,
                bean.mediumPackageNum ?: 1,
                bean.isSplit == 1,
                "${mViewModel.spellGroupRecommendGoodsLiveData.value?.goodsIdMapping?.get(bean.productId) ?: 0}"
            )
            pel.setOnAddCartListener(object : ProductEditLayoutSuiXinPin.AddCartListener {
                override fun onPreAddCart(params: RequestParams?): RequestParams =
                    RequestParams()

                override fun onAddCartSuccess(params: ProductEditLayoutSuccessParams?) {
//                    mAddChargeLocalCallback?.invoke(pel)

                    val spellGroupGoodsItem = SpellGroupGoodsItem(
                        bean.imageUrl,
                        bean.markerUrl,
                        bean.showName,
                        bean.actSuiXinPin?.suiXinPinPrice ?: "0",
                        bean.productUnit,
                        "${bean.fob}",
                        0,
                        bean.productId,
                        bean.stepNum,
                        bean.isSplit,
                        nearEffect = bean.nearEffect,
                        promoTag = bean.promoTag
                    )
                    spellGroupGoodsItem.qtData = bean.qtData
                    val goodsAmount =
                        ((params?.amount?.toIntOrNull() ?: 0) - (params?.preAmount ?: 0))
                    mViewModel.addSpellGroupRecommendCart(
                        spellGroupGoodsItem, goodsAmount>0, goodsAmount.absoluteValue
                    )

                    if (baseViewHolder?.itemView?.context is BaseActivity) {
                        (baseViewHolder.itemView.context as BaseActivity).hideSoftInput()
                    }
                    val isAdd =
                        ((params?.amount?.toIntOrNull() ?: 0) - (params?.preAmount ?: 0)) > 0

                    onAddCartListener.invoke(
                        RecommendSuiXinPinBean(
                            bean,
                            params?.amount?.toInt() ?: 0,
                            isAdd
                        )
                    )
//                    XyyIoUtil.track(
//                        "page_ListPage_Purchase", hashMapOf(
//                            "commodityId" to mViewModel.mainGoodsSkuId,
//                            "commodityName" to mMainGoods?.goodsTitle,
//                            "sptype" to bean.sptype,
//                            "spid" to bean.spid,
//                            "sid" to bean.sid,
//                            "direct" to "5",
//                            "source" to bean.source,
//                            "index" to "${holder.bindingAdapterPosition}"
//                        )
//                    )
                }
            })
        }
    }
}
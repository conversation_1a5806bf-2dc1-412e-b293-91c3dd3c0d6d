package com.ybmmarket20.adapter;


import static com.ybmmarket20.activity.PaymentActivity.REQUESTCODE_TO_FREIGHT_ADD_ON_ITEM;
import static com.ybmmarket20.bean.RefundProductListBean.ITEMTYPE_CONTENT;
import static com.ybmmarket20.bean.RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT;
import static com.ybmmarketkotlin.activity.FreightAddOnItemActivityKt.ACTIVITY_TYPE_FROM_PAYMENT;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.OrderProductListActivity;
import com.ybmmarket20.activity.PaymentActivity;
import com.ybmmarket20.activity.VoucherAvailableActivity;
import com.ybmmarket20.bean.AgentOrderListRowBean;
import com.ybmmarket20.bean.NextDayDeliveryDtoBean;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.bean.payment.PaymentImageBean;
import com.ybmmarket20.bean.payment.PaymentItemBean;
import com.ybmmarket20.bean.payment.PaymentShoppingGroupBean;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.MathUtils;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.FreightTipDialog;
import com.ybmmarket20.viewmodel.PaymentGoodsViewModel;

import java.util.List;
import java.util.Map;

public class PaymentNewAdapter extends YBMGroupListAdapter<PaymentItemBean> {

    private int getLayoutPosition = -1;
    private AgentOrderListRowBean agentOrderBean;
    private boolean isKaUser = SpUtil.isKa();

    private PaymentGoodsViewModel mPaymentGoodsViewModel;

    public PaymentNewAdapter(List<PaymentItemBean> data, PaymentGoodsViewModel paymentGoodsViewModel) {
        super(R.layout.payment_item_group, R.layout.payment_item_content, data);
        mPaymentGoodsViewModel = paymentGoodsViewModel;
    }

    private GiftSelectClickListener giftSelect ;

    public GiftSelectClickListener getGiftSelect() {
        return giftSelect;
    }

    public void setGiftSelect(GiftSelectClickListener giftSelect) {
        this.giftSelect = giftSelect;
    }

    public interface GiftSelectClickListener{
        void onClick(PaymentShoppingGroupBean group);
    }

    @Override
    public void bindGroupView(YBMBaseHolder ybmBaseHolder, PaymentItemBean paymentItemBean) {
        ((TextView) ybmBaseHolder.getView(R.id.icon_arrow)).setCompoundDrawablesWithIntrinsicBounds(null, null, mContext.getResources().getDrawable(paymentItemBean.isExpanded() ? R.drawable.icon_payment_up_arrow : R.drawable.icon_payment_down_arrow), null);
        ((TextView) ybmBaseHolder.getView(R.id.icon_arrow)).setText(paymentItemBean.isExpanded() ? "收起" : "展开");
        ConstraintLayout rlNextDay = ybmBaseHolder.getView(R.id.rl_next_day);
        TextView tvNextDayLabel = ybmBaseHolder.getView(R.id.tv_next_day_label);
        TextView tvNextDayDes = ybmBaseHolder.getView(R.id.tv_next_day);
        setNextDayData(tvNextDayDes, tvNextDayLabel, rlNextDay, paymentItemBean.nextDayDeliveryDto);
        //自营或非自营
        boolean notThirdCompany = paymentItemBean.IsNotThirdCompany();
        boolean isExpanded_group = (paymentItemBean.getSubItems() != null && paymentItemBean.getSubItems().size() > 1);
        ybmBaseHolder.setGone(R.id.icon_arrow, notThirdCompany && isExpanded_group);
        ybmBaseHolder.setOnClickListener(R.id.icon_arrow, v -> {
            //是否一条信息下有多条数据
            if (isExpanded_group) {
                //展开状态
                if (paymentItemBean.isExpanded()) {
                    List<PaymentItemBean> list = ((List<PaymentItemBean>) paymentItemBean.getSubItems());
                    if (list != null && list.size() > 0) {
                        for (PaymentItemBean subItem : list) {
                            if (subItem.isLast() && !TextUtils.isEmpty(subItem.getRemarks())) {
                                paymentItemBean.setRemarks(subItem.getRemarks());
                                subItem.setRemarks("");
                            }
                        }
                    }
                    collapse_(ybmBaseHolder.getLayoutPosition());
                } else {
                    if (!TextUtils.isEmpty(paymentItemBean.getRemarks())) {
                        List<PaymentItemBean> list = ((List<PaymentItemBean>) paymentItemBean.getSubItems());
                        if (list != null && list.size() > 0) {
                            for (PaymentItemBean subItem : list) {
                                if (subItem.isLast()) {
                                    subItem.setRemarks(paymentItemBean.getRemarks());
                                    paymentItemBean.setRemarks("");
                                }
                            }
                        }
                    }
                    expand_(ybmBaseHolder.getLayoutPosition());
                }
                getLayoutPosition = ybmBaseHolder.getLayoutPosition();
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(paymentItemBean);
                }
            }
        });

        //根据getSubItems有无一条数据isExpanded状态显示
        ybmBaseHolder.setGone(R.id.ll_content_show, !paymentItemBean.isExpanded());

        if (!notThirdCompany && paymentItemBean.getPromoTotalAmt() > 0D) {
            ybmBaseHolder.setVisible(R.id.ll_preferential_activities, true);
            ybmBaseHolder.setText(R.id.tv_preferential_activities_price, "-¥" + UiUtils.transform(paymentItemBean.getPromoTotalAmt()));
        } else {
            ybmBaseHolder.setVisible(R.id.ll_preferential_activities, false);
        }

        //是否三方药店
        TextView proprietary = ybmBaseHolder.getView(R.id.tv_cart_proprietary);
        proprietary.setText(paymentItemBean.getCompanyName());
        if (notThirdCompany) {
            proprietary.setCompoundDrawablesWithIntrinsicBounds(mContext.getResources().getDrawable(R.drawable.icon_autotrophy_new), null, null, null);
        } else {
            proprietary.setCompoundDrawablesWithIntrinsicBounds(mContext.getResources().getDrawable(R.drawable.icon_payment_pop), null, null, null);
        }

        //是否显示会员礼包
        String giftStr = "含物料心愿单礼包";
        ybmBaseHolder.setText(R.id.tv_gift, giftStr);
        ybmBaseHolder.setGone(R.id.tv_gift, paymentItemBean.getType() == 5 && paymentItemBean.isSelectStatus());

        ybmBaseHolder.setOnClickListener(R.id.ll_product, v -> {
            List<RefundProductListBean> detailList = paymentItemBean.detailListOrigin;
            if (detailList == null) detailList = paymentItemBean.getDetailList();
            if (detailList != null && detailList.size() > 0) {
                ((PaymentActivity) mContext).startActivity(OrderProductListActivity.getIntent2Me(mContext, detailList, paymentItemBean.getProductVarietyNum()));
            }
        });
        // 优惠券使用显示 7.9版本大平台券需求去掉优惠券显示
        //ybmBaseHolder.setGone(R.id.ll_order_discount_coupon, !notThirdCompany);
        ybmBaseHolder.setText(R.id.tv_discount_coupon_num, !TextUtils.isEmpty(paymentItemBean.getVoucherTip()) ? paymentItemBean.getVoucherTip() : "暂无可用优惠券");
        ybmBaseHolder.setTextColor(R.id.tv_discount_coupon_num, !TextUtils.isEmpty(paymentItemBean.getVoucherTip()) ? UiUtils.getColor(R.color.color_292933) : UiUtils.getColor(R.color.color_9494A6));

        //总优惠显示
        ybmBaseHolder.setGone(R.id.ll_the_total_discount, notThirdCompany);

        //件数
        ybmBaseHolder.setText(R.id.tv_product_number, paymentItemBean.getProductVarietyNum() <= 0 ? "共0件" : "共" + paymentItemBean.getProductVarietyNum() + "件");

        //合计
        ybmBaseHolder.setText(R.id.tv_combined_price, "¥" + UiUtils.transform(paymentItemBean.getPayAmount()));

        //邮费
        ybmBaseHolder.setText(R.id.tv_freight_num, "+¥" + paymentItemBean.getFreightTotalAmt());

        //总优惠
        ybmBaseHolder.setText(R.id.tv_the_total_discount_price, "-¥" + UiUtils.transform(paymentItemBean.getDiscountAmount()));

        handleGiftSelectTips(ybmBaseHolder, paymentItemBean);


        EditText et = ybmBaseHolder.getView(R.id.payment_message_leave_et);
        setEditText(paymentItemBean, et);
        setShowImage(ybmBaseHolder, paymentItemBean);

        if (isKaUser) {
            ybmBaseHolder.setGone(R.id.ll_order_freight, false);
        }
        TextView tipBtn = ybmBaseHolder.getView(R.id.tv_freight);
        setFreightTipBtn(tipBtn, paymentItemBean);
        //处理凑单包邮
        ybmBaseHolder.setGone(R.id.ll_order_freight_add_on_item, paymentItemBean.freightTipsShowStatus == 1);
        ybmBaseHolder.setText(R.id.tv_freight_add_on_item, paymentItemBean.freeFreightDiffTips);//包邮提示语
        ybmBaseHolder.setText(R.id.cart_new_tv_title_url, paymentItemBean.freightUrlText);//去凑单 展示
        ybmBaseHolder.setGone(R.id.cart_new_tv_title_url, !TextUtils.isEmpty(paymentItemBean.freightUrlText));
        ybmBaseHolder.setOnClickListener(R.id.cart_new_tv_title_url, v -> {//去凑单包邮
            RoutersUtils.openForResult("ybmpage://freightaddonitem/" + ACTIVITY_TYPE_FROM_PAYMENT, REQUESTCODE_TO_FREIGHT_ADD_ON_ITEM);
        });

        //是否显示随货资质
        ybmBaseHolder.setVisible(R.id.llPaymentLicense, true);
        ybmBaseHolder.setText(R.id.tvPaymentLicense, paymentItemBean.licenseStr);
        ybmBaseHolder.getView(R.id.tvPaymentLicense).setOnClickListener(view -> {
            mPaymentGoodsViewModel.getGoodsList(paymentItemBean.getShopCode(), null);
            mPaymentGoodsViewModel.setStaticShopName(paymentItemBean.getShopName());
            String shopType = "";
            if (paymentItemBean.isThirdCompany() && !paymentItemBean.isFbpShop) {
                shopType = "1";
            } else shopType = "2";
            RoutersUtils.open("ybmpage://licenserequirementwithorderEdit?shopCode=" + paymentItemBean.getShopCode() + "&shopType=" + shopType);
        });
        //7.9版本大平台券需求去掉优惠券显示
        //ybmBaseHolder.setOnClickListener(R.id.ll_order_discount_coupon, v -> updateVoucher(paymentItemBean));
    }

    private void setNextDayData(TextView tvNextDes, TextView tvNextLabel, ConstraintLayout rlNextDay, NextDayDeliveryDtoBean bean) {
        if (bean != null && null != bean.getTagDto()) {
            rlNextDay.setVisibility(View.VISIBLE);
            tvNextDes.setText(bean.getTips());
            int textColorInt = Color.parseColor("#00B377");
            try {
                textColorInt = Color.parseColor(bean.getTagDto().getTextColor());
            } catch (Exception e) {
            }
            tvNextLabel.setTextColor(textColorInt);
            int bgColorInt = Color.parseColor("#04BE88");
            try {
                Color.parseColor(bean.getTagDto().getBgColor());
            } catch (Exception e) {
            }

            int borderColorInt = Color.parseColor("#04BE88");
            try {
                borderColorInt = Color.parseColor(bean.getTagDto().getBorderColor());
            } catch (Exception e) {
            }

            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setColor(bgColorInt);
            gradientDrawable.setCornerRadius(UiUtils.dp2px(2));
            gradientDrawable.setStroke(UiUtils.dp2px(1), borderColorInt);
            tvNextLabel.setBackground(gradientDrawable);
            tvNextLabel.setText(bean.getTagDto().getName());
            if (!TextUtils.isEmpty(bean.getTagDto().getAppIcon())) {
                Glide.with(mContext)
                        .load(AppNetConfig.getCDNHost() + bean.getTagDto().getAppIcon()) // bean.imageUrl 是网络图片的 URL
                        .into(new SimpleTarget<GlideDrawable>() {
                            @Override
                            public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                                // 设置 drawableStart
                                resource.setBounds(0, 0, UiUtils.dp2px(7), UiUtils.dp2px(10));
                                tvNextLabel.setCompoundDrawablesRelative(
                                        resource,
                                        null,
                                        null,
                                        null
                                );
                            }
                        });
            }
        } else {
            rlNextDay.setVisibility(View.GONE);
        }
    }
    private void handleGiftSelectTips(YBMBaseHolder ybmBaseHolder, PaymentItemBean paymentItemBean) {
        //==============选赠品start===============
        try{
            ConstraintLayout clGiftTips = ybmBaseHolder.getView(R.id.cl_gift_tips);
            ConstraintLayout rlNextDay = ybmBaseHolder.getView(R.id.rl_next_day);
            TextView tvGiftTips = ybmBaseHolder.getView(R.id.tv_gift_tips);
            TextView tvSelectGift = ybmBaseHolder.getView(R.id.tv_select_gift);
            TextView tvNextDayLabel = ybmBaseHolder.getView(R.id.tv_next_day_label);
            TextView tvNextDayDes = ybmBaseHolder.getView(R.id.tv_next_day);
            ImageView ivArrow = ybmBaseHolder.getView(R.id.iv_arrow);
            PaymentShoppingGroupBean group = paymentItemBean.getGroup();
            if (group != null && group.isCanGoToGiftPool()){

                clGiftTips.setVisibility(View.VISIBLE);
                if (group.isGiveUpGift()){
                    tvGiftTips.setText(mContext.getResources().getString(R.string.str_gift_get_tips_3, String.valueOf(group.getGiftPoolActTotalSelectedNum())));
                }else {
                    if (group.getGiftPoolActHasSelectedNum()==0){
                        tvGiftTips.setText(mContext.getResources().getString(R.string.str_gift_get_tips, String.valueOf(group.getGiftPoolActTotalSelectedNum())));
                    }else {
                        tvGiftTips.setText(mContext.getResources().getString(R.string.str_gift_get_tips_2, String.valueOf(group.getGiftPoolActTotalSelectedNum()),String.valueOf(group.getGiftPoolActHasSelectedNum())));
                    }
                }
            }else {
                clGiftTips.setVisibility(View.GONE);
            }
            setNextDayData(tvNextDayDes, tvNextDayLabel, rlNextDay, paymentItemBean.nextDayDeliveryDto);
            tvSelectGift.setOnClickListener(v -> {
                if (giftSelect != null){
                    giftSelect.onClick(group);
                }
            });

            ivArrow.setOnClickListener(v ->{
                if (giftSelect!= null){
                    giftSelect.onClick(group);
                }
            });
        }catch (Exception e){
            e.printStackTrace();
        }


        //==============选赠品end===============
    }

    /**
     * 设置显示运费提示按钮
     *
     * @param tipView         显示按钮的view
     * @param paymentItemBean 数据对象
     */
    private void setFreightTipBtn(TextView tipView, PaymentItemBean paymentItemBean) {
        if (tipView == null || paymentItemBean == null) return;
        if (paymentItemBean.freightIconShowStatus == 1) {
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_hint_image_cart);
            if (drawable != null) {
                drawable.setBounds(0, 0, ConvertUtils.dp2px(11), ConvertUtils.dp2px(11));
                tipView.setCompoundDrawables(null, null, drawable, null);
            }
            tipView.setOnClickListener(v -> new FreightTipDialog(mContext).showTip(paymentItemBean.getShopCode()));
        } else {
            tipView.setCompoundDrawables(null, null, null, null);
            tipView.setOnClickListener(null);
        }
    }

    @Override
    public void bindContentView(YBMBaseHolder ybmBaseHolder, PaymentItemBean paymentItemBean) {
        ybmBaseHolder.setText(R.id.tv_cart_proprietary, paymentItemBean.getShopName());

        ybmBaseHolder.setGone(R.id.ll_combined, paymentItemBean.isLast());
        ybmBaseHolder.setGone(R.id.payment_message, paymentItemBean.isLast());
        ybmBaseHolder.setGone(R.id.ll_order_freight, paymentItemBean.isLast());
        //凑单包邮是最后一个item并且freightTipsShowStatus=1时才显示
        ybmBaseHolder.setGone(R.id.ll_order_freight_add_on_item, paymentItemBean.isLast() && paymentItemBean.freightTipsShowStatus == 1);
        //最后一个shop并且不止一个shop
        ybmBaseHolder.setGone(R.id.ll_more, paymentItemBean.isLast() && !paymentItemBean.isShopCount());
        //小计的显示-不止一个shop就显示
        ybmBaseHolder.setGone(R.id.ll_subtotal, !paymentItemBean.isShopCount());
        //是否显示会员礼包
        String giftStr = "含物料心愿单礼包";
        ybmBaseHolder.setText(R.id.tv_gift, giftStr);
        ybmBaseHolder.setGone(R.id.tv_gift, paymentItemBean.getType() == 5 && paymentItemBean.isSelectStatus());

        ybmBaseHolder.setOnClickListener(R.id.ll_more, v -> {
            if (!TextUtils.isEmpty(paymentItemBean.getRemarks())) {
                if (paymentItemBean.isLast()) {
                    if (mData != null && mData.size() > getLayoutPosition) {
                        ((PaymentItemBean) mData.get(getLayoutPosition)).setRemarks(paymentItemBean.getRemarks());
                        paymentItemBean.setRemarks("");
                    }
                }
            }
            collapse_(getLayoutPosition);
            if (mOnItemClickListener != null) {
                mOnItemClickListener.onItemClick(paymentItemBean);
            }
        });

        ybmBaseHolder.setOnClickListener(R.id.ll_product, v -> {
            List<RefundProductListBean> detailList = paymentItemBean.getDetailList();
            if (detailList != null && detailList.size() > 0) {
                ((PaymentActivity) mContext).startActivityForResult(OrderProductListActivity.getIntent2Me(mContext, detailList, paymentItemBean.getProductVarietyNum()), 11);
            }
        });
        //7.9版本大平台券需求去掉优惠券显示
        //ybmBaseHolder.setOnClickListener(R.id.ll_order_discount_coupon, v -> updateVoucher(paymentItemBean));

        //件数
        ybmBaseHolder.setText(R.id.tv_product_number, paymentItemBean.getProductVarietyNum() <= 0 ? "共0件" : "共" + paymentItemBean.getProductVarietyNum() + "件");

        //活动优惠
        ybmBaseHolder.setGone(R.id.ll_preferential_activities, paymentItemBean.getPromoTotalAmt() > 0);
        if (ybmBaseHolder.getView(R.id.ll_preferential_activities).getVisibility() == View.VISIBLE) {
            ybmBaseHolder.setText(R.id.tv_preferential_activities_price, "-¥" + UiUtils.transform(paymentItemBean.getPromoTotalAmt()));
        }

        //优惠券
        ybmBaseHolder.setText(R.id.tv_discount_coupon_num, !TextUtils.isEmpty(paymentItemBean.getVoucherTip()) ? paymentItemBean.getVoucherTip() : "暂无可用优惠券");
        ybmBaseHolder.setTextColor(R.id.tv_discount_coupon_num, !TextUtils.isEmpty(paymentItemBean.getVoucherTip()) ? UiUtils.getColor(R.color.color_292933) : UiUtils.getColor(R.color.color_9494A6));

        //小计
        ybmBaseHolder.setText(R.id.tv_subtotal_price, "¥" + UiUtils.transform(paymentItemBean.getPayAmount()));

        //合计
//        ybmBaseHolder.setText(R.id.tv_combined_price, "¥" + UiUtils.transform(paymentItemBean.getAllPayAmount()));
        ybmBaseHolder.setText(R.id.tv_combined_price, "¥" + UiUtils.transform(paymentItemBean.getPayAmount()));

        //邮费
        ybmBaseHolder.setText(R.id.tv_freight_num, "+¥" + paymentItemBean.getFreightTotalAmt());

        handleGiftSelectTips(ybmBaseHolder, paymentItemBean);

        EditText et = ybmBaseHolder.getView(R.id.payment_message_leave_et);
        setEditText(paymentItemBean, et);
        setShowImage(ybmBaseHolder, paymentItemBean);

        if (isKaUser) {
            ybmBaseHolder.setGone(R.id.ll_order_freight, false);
            //7.9版本大平台券需求去掉优惠券显示
            //ybmBaseHolder.setGone(R.id.ll_order_discount_coupon, false);
        }
        TextView tipBtn = ybmBaseHolder.getView(R.id.tv_freight);
        setFreightTipBtn(tipBtn, paymentItemBean);
        //处理凑单包邮
        ybmBaseHolder.setText(R.id.tv_freight_add_on_item, paymentItemBean.freeFreightDiffTips);//包邮提示语
        ybmBaseHolder.setText(R.id.cart_new_tv_title_url, paymentItemBean.freightUrlText);//去凑单 展示
        ybmBaseHolder.setGone(R.id.cart_new_tv_title_url, !TextUtils.isEmpty(paymentItemBean.freightUrlText));
        ybmBaseHolder.setOnClickListener(R.id.cart_new_tv_title_url, v -> {//去凑单包邮
            RoutersUtils.openForResult("ybmpage://freightaddonitem/" + ACTIVITY_TYPE_FROM_PAYMENT, REQUESTCODE_TO_FREIGHT_ADD_ON_ITEM);
        });
        //是否显示随货资质
        ybmBaseHolder.setVisible(R.id.llPaymentLicense, true);
        ybmBaseHolder.setText(R.id.tvPaymentLicense, paymentItemBean.licenseStr);
        ybmBaseHolder.getView(R.id.tvPaymentLicense).setOnClickListener(view -> {
            mPaymentGoodsViewModel.getGoodsList(paymentItemBean.getShopCode(), null);
            mPaymentGoodsViewModel.setStaticShopName(paymentItemBean.getShopName());
            String shopType = "";
            if (paymentItemBean.isThirdCompany() && !paymentItemBean.isFbpShop) {
                shopType = "1";
            } else shopType = "2";
            RoutersUtils.open("ybmpage://licenserequirementwithorderEdit?shopCode=" + paymentItemBean.getShopCode() + "&shopType=" + shopType);
        });
    }

    private void setEditText(PaymentItemBean paymentItemBean, EditText et) {
        //通过tag判断当前editText是否已经设置监听，有监听的话，移除监听再给editText赋值
        if (et.getTag() instanceof TextWatcher) {
            et.removeTextChangedListener((TextWatcher) et.getTag());
        }
        //必须在判断tag后给editText赋值，否则会数据错乱
        et.setText(paymentItemBean.getRemarks());
//        et.setFilters(new InputFilter[]{new EmojiFilter(EmojiFilter.Type.OLD)});
        TextWatcher watcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                paymentItemBean.setRemarks(editable.toString());
            }
        };
        //给item中的editText设置监听
        et.addTextChangedListener(watcher);
        //给editText设置tag，以便于判断当前editText是否已经设置监听
        et.setTag(watcher);
    }

    /**
     * 更改优惠券
     */
    private void updateVoucher(PaymentItemBean paymentItemBean) {
        if (paymentItemBean == null) {
            return;
        }

        //重新设置优惠id
        String mVoucherIds = getVoucherId(paymentItemBean);

        String commodityId = getCommodityId(paymentItemBean.getDetailList());
        String skuIds = getSkuIds(paymentItemBean.getDetailList());
        double price = paymentItemBean.getTotalAmount();
        if (paymentItemBean.getGiftTotalAmount() >= 0.0001) {
            price = MathUtils.sub(paymentItemBean.getTotalAmount(), paymentItemBean.getGiftTotalAmount());
        }
        if (paymentItemBean.getFreightTotalAmt() >= 0.0001) {
            price = MathUtils.sub(price, paymentItemBean.getFreightTotalAmt());
        }

        String priceStr = UiUtils.transform(price);
        String id_price;
        if (!TextUtils.isEmpty(commodityId)) {
            id_price = commodityId + "=" + priceStr;
        } else {
            ToastUtils.showShort("无可用优惠券信息");
            return;
        }
        Intent intent = new Intent(((PaymentActivity) mContext), VoucherAvailableActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString(IntentCanst.VOUCHERINFO, id_price);
        bundle.putString("shopCode", paymentItemBean.getShopCode());
        bundle.putString("shopPatternCode", paymentItemBean.getShopPatternCode());
        bundle.putString("skuIds", skuIds);
        bundle.putString("ids", mVoucherIds);
        if (agentOrderBean != null) {//代下单过来的
            bundle.putBoolean("isFromAgentOrder", true);
            bundle.putString("purchaseNo", agentOrderBean.getPurchaseNo());
        }
        intent.putExtras(bundle);
        //跳转请求码
        int VOUVHERCODE = 2;
        ((PaymentActivity) mContext).startActivityForResult(intent, VOUVHERCODE);
    }

    private String getVoucherId(PaymentItemBean paymentItemBean) {
        //遍历selectVoucherIds获取优惠券id集合
        String selectVoucherIds = paymentItemBean.getSelectVoucherIds();
        Gson gson = new Gson();
        Map<String, String> selectVoucherIdsMap;
        String newVoucherIds = "";
        try {
            selectVoucherIdsMap = gson.fromJson(selectVoucherIds, new TypeToken<Map<String, String>>() {
            }.getType());
            if (selectVoucherIdsMap == null || selectVoucherIdsMap.isEmpty()) {
                return newVoucherIds;
            }
            StringBuilder sb = new StringBuilder();
            for (String key : selectVoucherIdsMap.keySet()) {
                sb.append(key);
                sb.append(",");
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
                newVoucherIds = sb.toString();
                return newVoucherIds;
            }
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }
        return newVoucherIds;
    }

    /**
     * productId
     */
    private String getCommodityId(List<RefundProductListBean> detailList) {
        StringBuilder sb = new StringBuilder();
        if (detailList == null) {
            return null;
        }
        RefundProductListBean bean;
        for (int i = 0; i < detailList.size(); i++) {
            bean = detailList.get(i);
            if (bean.getItemType() == ITEMTYPE_CONTENT || bean.getItemType() == ITEMTYPE_PACKAGE_CONTENT) {//正常商品
                sb.append(detailList.get(i).productId);
                sb.append(":");
                sb.append(detailList.get(i).subtotal);
                sb.append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
            return sb.toString();
        }
        return null;
    }

    /**
     * productId
     */
    private String getSkuIds(List<RefundProductListBean> detailList) {
        StringBuilder sb = new StringBuilder();
        if (detailList == null) {
            return null;
        }
        RefundProductListBean bean;
        for (int i = 0; i < detailList.size(); i++) {
            bean = detailList.get(i);
            if (bean.getItemType() == ITEMTYPE_CONTENT || bean.getItemType() == ITEMTYPE_PACKAGE_CONTENT) {//正常商品
                sb.append(detailList.get(i).productId);
                sb.append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
            return sb.toString();
        }
        return null;
    }

    /*
     * 设置图片
     * */
    private void setShowImage(YBMBaseHolder ybmBaseHolder, PaymentItemBean paymentItemBean) {
        ImageView iv1 = ybmBaseHolder.getView(R.id.iv_product_1);
        ImageView iv2 = ybmBaseHolder.getView(R.id.iv_product_2);
        ImageView iv3 = ybmBaseHolder.getView(R.id.iv_product_3);

        if (paymentItemBean.getDefaultShowProducts() != null && paymentItemBean.getDefaultShowProducts().size() > 0) {
            setProductImgs(iv1, iv2, iv3, paymentItemBean.getDefaultShowProducts());
        }
    }

    /*
     * 设置图片
     * */
    private void setProductImgs(ImageView ivProduct1, ImageView ivProduct2, ImageView ivProduct3, List<PaymentImageBean> list) {
        if (list == null || list.isEmpty()) {
            ivProduct1.setImageDrawable(null);
            ivProduct2.setImageDrawable(null);
            ivProduct3.setImageDrawable(null);
            return;
        }
        //设置商品图片
        if (list.size() > 0 && !TextUtils.isEmpty(list.get(0).getImgUrl())) {
            ivProduct1.setVisibility(View.VISIBLE);
            ivProduct2.setVisibility(View.INVISIBLE);
            ivProduct3.setVisibility(View.INVISIBLE);
            ImageHelper.with(mContext).load(UiUtils.getProductImageUrl(list.get(0).getImgUrl())).dontAnimate()
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE).error(R.drawable.transparent)
                    .placeholder(R.drawable.transparent).into(ivProduct1);
        }
        if (list.size() > 1 && !TextUtils.isEmpty(list.get(1).getImgUrl())) {
            ivProduct2.setVisibility(View.VISIBLE);
            ivProduct3.setVisibility(View.INVISIBLE);
            ImageHelper.with(mContext).load(UiUtils.getProductImageUrl(list.get(1).getImgUrl())).dontAnimate()
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE).error(R.drawable.transparent)
                    .placeholder(R.drawable.transparent).into(ivProduct2);
        }
        if (list.size() > 2 && !TextUtils.isEmpty(list.get(2).getImgUrl())) {
            ivProduct3.setVisibility(View.VISIBLE);
            ImageHelper.with(mContext).load(UiUtils.getProductImageUrl(list.get(2).getImgUrl())).dontAnimate()
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE).error(R.drawable.transparent)
                    .placeholder(R.drawable.transparent).into(ivProduct3);
        }
    }

    public interface OnCartItemClickListener {
        void onItemClick(PaymentItemBean rows);
    }

    private OnCartItemClickListener mOnItemClickListener = null;

    public void setOnItemClickListener(OnCartItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }

    //把代下单的bean从activity传递过来取里面的采购单编号purchaseNo带到选择优惠券页面
    public void setAgentOrderBean(AgentOrderListRowBean agentOrderBean) {
        this.agentOrderBean = agentOrderBean;
    }
}

package com.ybmmarket20.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.VoucherListBean;
import com.ybmmarket20.utils.DateTimeUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.SurroundTagView;

import java.util.List;

/**
 * 优惠券适配器
 * Created by asus on 2016/5/30.
 */
public class CouponMemberAdapter extends YBMBaseAdapter<VoucherListBean> {

    private boolean mIsShowUse;

    public CouponMemberAdapter(int layoutResId, boolean showUse, List<VoucherListBean> couponData) {
        super(layoutResId, couponData);
        mIsShowUse = showUse;
    }

    @Override
    protected void bindItemView(YBMBaseHolder baseViewHolder, final VoucherListBean voucherListBean) {

        LinearLayout llLeft = baseViewHolder.getView(R.id.ll_left);
        TextView tvCouponCondition = baseViewHolder.getView(R.id.tv_coupon_condition);
        TextView tvStartUse = baseViewHolder.getView(R.id.tv_start_use);
        TextView tvPriceUnit = baseViewHolder.getView(R.id.tv_price_unit);
        TextView tvDiscountUnit = baseViewHolder.getView(R.id.tv_discount_unit);
        ImageView ivStatus = baseViewHolder.getView(R.id.iv_coupon_status);
        SurroundTagView tvTypeAndCondition = baseViewHolder.getView(R.id.tv_tag_and_condition);
        if ("1".equalsIgnoreCase(voucherListBean.voucherUsageWay) && !TextUtils.isEmpty(voucherListBean.maxMoneyInVoucherDesc)) {
            baseViewHolder.setGone(R.id.tv_coupon_max, true);
            baseViewHolder.setText(R.id.tv_coupon_max, voucherListBean.maxMoneyInVoucherDesc);
        } else {
            baseViewHolder.setGone(R.id.tv_coupon_max, false);
        }
        //左边显示打折或者价格
//        if (voucherListBean.voucherType == 3) {
//            tvPriceUnit.setVisibility(View.GONE);
//            tvDiscountUnit.setVisibility(View.VISIBLE);
//            int discount = (int) voucherListBean.discount;
//            baseViewHolder.setText(R.id.tv_coupon_price, discount % 10 == 0 ? ("" + discount / 10) : ("" + discount / 10.0));
//        } else {
//            tvPriceUnit.setVisibility(View.VISIBLE);
//            tvDiscountUnit.setVisibility(View.GONE);
//            baseViewHolder.setText(R.id.tv_coupon_price, UiUtils.transformInt(voucherListBean.getMoneyInVoucher()));
//        }


        if (voucherListBean.voucherState == 1) {
            // 折扣券
            tvPriceUnit.setVisibility(View.GONE);
            tvDiscountUnit.setVisibility(View.VISIBLE);
            baseViewHolder.setText(R.id.tv_coupon_price, voucherListBean.discount);
        } else {
            tvPriceUnit.setVisibility(View.VISIBLE);
            tvDiscountUnit.setVisibility(View.GONE);
            baseViewHolder.setText(R.id.tv_coupon_price, UiUtils.transformInt(voucherListBean.moneyInVoucher));
        }


        //使用门槛
        baseViewHolder.setText(R.id.tv_coupon_condition, voucherListBean.getMinMoneyToEnableDesc());
        //券类型和券文案
        tvTypeAndCondition.setTagContent(voucherListBean.getVoucherTypeDesc(), voucherListBean.voucherTitle);
        //券有效期
        String time = DateTimeUtil.getCouponDateTime(voucherListBean.validDate)
                + "-" + DateTimeUtil.getCouponDateTime(voucherListBean.expireDate);
        baseViewHolder.setText(R.id.tv_coupon_use_time, time);

        boolean isMinMoney = !TextUtils.isEmpty(voucherListBean.getMinMoneyToEnableDesc());
        tvCouponCondition.setVisibility(isMinMoney ? View.VISIBLE : View.GONE);
        //是否显示“按钮”
        tvStartUse.setVisibility(mIsShowUse ? View.VISIBLE : View.GONE);
        //根据服务器传递数据tag判断
        int state = voucherListBean.state;
        switch (state) {
            case 2:
                //是否显示立即领取按钮
                mIsShowUse = (voucherListBean.isUse == 0);
                tvStartUse.setVisibility(mIsShowUse ? View.VISIBLE : View.GONE);
                ivStatus.setVisibility(View.GONE);
                llLeft.setBackgroundResource(R.drawable.bg_coupon_left);
                break;
            case 3:
                tvStartUse.setVisibility(View.GONE);
                ivStatus.setVisibility(View.VISIBLE);
                ivStatus.setImageResource(R.drawable.icon_used);
                llLeft.setBackgroundResource(R.drawable.bg_coupon_left_invliad);
                break;
            case 4:
                tvStartUse.setVisibility(View.GONE);
                ivStatus.setVisibility(View.VISIBLE);
                ivStatus.setImageResource(R.drawable.icon_expired);
                llLeft.setBackgroundResource(R.drawable.bg_coupon_left_invliad);
                break;
        }

        tvStartUse.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnAvailableClickListener != null) {
                    mOnAvailableClickListener.onAvailableClick(voucherListBean);
                }
            }
        });
    }

    public interface OnAvailableClickListener {
        void onAvailableClick(VoucherListBean coupon);
    }

    private OnAvailableClickListener mOnAvailableClickListener = null;

    public void setOnItemClickListener(OnAvailableClickListener listener) {
        this.mOnAvailableClickListener = listener;
    }

}

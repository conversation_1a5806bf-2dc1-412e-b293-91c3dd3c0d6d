package com.ybmmarket20.adapter;

import android.animation.Animator;
import android.content.Context;
import android.content.Intent;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.os.Handler;

import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.text.InputType;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chad.library.adapter.base.BaseViewHolder;
import com.google.gson.Gson;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JgOperationPositionInfo;
import com.ybmmarket20.bean.JgRequestParams;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.reportBean.JGPageListCommonBean;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CartDataBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.ProductDetailBean;
import com.ybmmarket20.bean.cart.CartItemBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.db.info.HandlerGoodsDao;
import com.ybmmarket20.utils.ClickDelayUtil;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.AnalysisConst;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.view.BaseBottomPopWindow;
import com.ybmmarket20.view.FreightTipDialog;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.ShowBottomCartCouponDialog;
import com.ybmmarket20.view.SwipeMenuLayout;
import com.ybmmarket20.view.TagView;
import com.ydmmarket.report.manager.TrackManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;

import static com.ybmmarket20.bean.RefundProductListBean.ITEMTYPE_CONTENT;
import static com.ybmmarket20.bean.RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT;
import static com.ybmmarket20.common.util.Abase.getResources;

/**
 * 购物车
 */
@Deprecated
public class ShopCheckAdapter extends YBMBaseMultiItemAdapter<CartItemBean> {

    private Handler mHandler;
    private HandlerGoodsDao goodsDao;
    private int isVisibility = View.VISIBLE;
    private static List<Integer> isSelected;
    private static List<Integer> isCheckSelected;
    private static boolean finishOrEdit = false;
    private int mTotal, mTotal_normal;
    public int number = 0;//记录对话框中的数量

    private static final int CART_SELL_OUT = 2;
    private static final int CART_SOLD_OUT = 4;
    private static final int CART_JURISDICTION_OUT = 91;
    private static final int CART_RESTRICTION_OUT = 92;
    private static final int CART_BUSINESS_SCOPE_OUT = 95;
    private static final int CART_OEM = 105;

    private static final int MEDIUM_PACKAGE_NUMBER = 10;
    private static final int IS_SPLIT = 1;
    private BaseFlowData flowData;
    private ShowBottomCartCouponDialog bottomCartCouponDialog;
    private View token;
    private BaseBottomPopWindow.OnSelectListener dismissListener;
    private ClickDelayUtil clickDelayUtil;
    private Function1<Boolean, Unit> addCartCallback;
    public JgTrackBean jgTrackBean = null;

    public ShopCheckAdapter(List<CartItemBean> data, Handler mHandler) {
        super(data);
        addItemType(CartItemBean.content_normal, R.layout.cart_section_content);
        addItemType(CartItemBean.content_combo, R.layout.cart_section_content01);
        addItemType(CartItemBean.content_end, R.layout.cart_section_content02);
        addItemType(CartItemBean.content_commodity, R.layout.cart_section_content03);
        addItemType(CartItemBean.content_invalid_commodity_end, R.layout.cart_section_content04);
        addItemType(CartItemBean.content_preferential_combo, R.layout.cart_section_content07);
        addItemType(CartItemBean.content_preferential_end, R.layout.cart_section_content09);
        addItemType(CartItemBean.content_preferential_combo_end, R.layout.cart_section_content08);

        addItemType(CartItemBean.header_lose_efficacy, R.layout.cart_section_head);
        addItemType(CartItemBean.header_money_off, R.layout.cart_section_head01);
        addItemType(CartItemBean.header_combo_head, R.layout.cart_section_head02);
        addItemType(CartItemBean.header_combo_end, R.layout.cart_section_head03);
        addItemType(CartItemBean.header_combo_lose_end, R.layout.cart_section_head09);
        addItemType(CartItemBean.header_lose_combo_head, R.layout.cart_section_head04);
        addItemType(CartItemBean.header_proprietary_head, R.layout.cart_section_head05);
        addItemType(CartItemBean.header_shop_head, R.layout.cart_section_head08);
        addItemType(CartItemBean.header_return_ticket_head, R.layout.cart_section_head11);
        clickDelayUtil = new ClickDelayUtil();
        this.mHandler = mHandler;
        isSelected = new ArrayList<>();
        isCheckSelected = new ArrayList();
        goodsDao = HandlerGoodsDao.getInstance();
        initData();
    }

    @Override
    public void setNewData(List data) {
        super.setNewData(data);
        initData();
    }

    /**
     * 设置埋点数据
     */
    public void setFlowData(BaseFlowData flowData) {
        this.flowData = flowData;
    }

    @Override
    protected void startAnim(Animator anim, int index) {
        super.startAnim(anim, index);
        if (index < 5) anim.setStartDelay(index * 150);
    }

    // 初始化isSelected的数据
    private void initData() {
        mTotal = 0;
        mTotal_normal = 0;
        if (isSelected == null) {
            isSelected = new ArrayList<>();
        }
        if (isCheckSelected == null) {
            isCheckSelected = new ArrayList<>();
        }
        isSelected.clear();
        isCheckSelected.clear();
        if (mData != null && mData.size() > 0) {
            for (int i = 0; i < mData.size(); i++) {
                CartItemBean groupBean = (CartItemBean) mData.get(i);
                if (groupBean.getItemType() == CartItemBean.header_combo_head
                        || groupBean.getItemType() == CartItemBean.content_normal
                        || groupBean.getItemType() == CartItemBean.content_end
                        || groupBean.getItemType() == CartItemBean.content_preferential_end
                        || groupBean.getItemType() == CartItemBean.content_preferential_combo_end
                        || groupBean.getItemType() == CartItemBean.header_shop_head
                        || groupBean.getItemType() == CartItemBean.content_commodity
                        || groupBean.getItemType() == CartItemBean.header_proprietary_head
                        || groupBean.getItemType() == CartItemBean.header_lose_combo_head
                        || groupBean.getItemType() == CartItemBean.content_preferential_combo
                        || groupBean.getItemType() == CartItemBean.content_invalid_commodity_end) {
                    mTotal++;

                    if (groupBean.getSkuStatus() != 2 && groupBean.getSkuStatus() != 4 && groupBean.getSkuStatus() != 91 && groupBean.getSkuStatus() != 92 && groupBean.getValid() != 0) {
                        mTotal_normal++;
                        if (groupBean.getStatus() == 1) {
                            isCheckSelected.add(i);
                        }
                    }
                }
            }
        }
    }

    @Override
    protected void bindItemView(YBMBaseHolder baseViewHolder, CartItemBean cartItemBean) {
        switch (baseViewHolder.getItemViewType()) {
            case CartItemBean.header_lose_efficacy:
                bindHeadInLoseEfficacy(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.header_money_off:
                bindHeadInMoneyOff(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.header_combo_head:
                bindHeadComboHead(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.header_combo_end:
            case CartItemBean.header_combo_lose_end:
                bindHeadComboEnd(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.header_lose_combo_head:
                bindHeaderLoseComboHead(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.header_proprietary_head:
                bindHeaderProprietaryHeadHead(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.header_shop_head:
                bindHeadShop(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.content_normal:
            case CartItemBean.content_end:
            case CartItemBean.content_preferential_end:
            case CartItemBean.content_preferential_combo_end:
            case CartItemBean.content_commodity:
            case CartItemBean.content_invalid_commodity_end:
            case CartItemBean.content_preferential_combo:
                bindContent(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.content_combo:
                bindContentCombo(baseViewHolder, cartItemBean);
                break;
            case CartItemBean.header_return_ticket_head:
                bindReturnVoucherInfo(baseViewHolder, cartItemBean);
                break;
        }
    }

    /*
     * 头部样式1-失效商品头部
     * */
    private void bindHeadInLoseEfficacy(final BaseViewHolder baseViewHolder, final CartItemBean CartItemBean) {
        baseViewHolder.getConvertView().setOnClickListener(v -> {
            if (CartItemBean != null) {
                if (null != mOnSwipeListener) {
                    mOnSwipeListener.onAllDel(baseViewHolder.getAdapterPosition());
                }
            }
        });
        baseViewHolder.setText(R.id.cart_new_rl_tv01, CartItemBean.getTitle());
    }

    /*
     * 头部样式2-满减头部样式
     * */
    private void bindHeadInMoneyOff(final YBMBaseHolder baseViewHolder, final CartItemBean cartItemBean) {
        baseViewHolder.setText(R.id.cart_new_rl_iv, setTitle(cartItemBean.getType()))
                .setBackgroundRes(R.id.cart_new_rl_iv, setBgIcon(cartItemBean.getType()))
                .setTextColor(R.id.cart_new_rl_iv, mContext.getResources().getColor(setColor(cartItemBean.getType())))
                .setText(R.id.cart_new_rl_tv01, cartItemBean.getTitle());

        String title = !TextUtils.isEmpty(cartItemBean.getTitleUrlText()) ? cartItemBean.getTitleUrlText() : "去凑单";
        String titleUrl = cartItemBean.getTitleUrl();
        boolean isShowUrl = !TextUtils.isEmpty(titleUrl);

        baseViewHolder.setText(R.id.cart_new_tv_title_url, title);
        baseViewHolder.setGone(R.id.cart_new_tv_title_url, isShowUrl);
        baseViewHolder.getConvertView().setOnClickListener(v -> {
            if (!TextUtils.isEmpty(titleUrl)) {
                RoutersUtils.open(titleUrl);
            }
        });
    }

    /*
     * 头部样式3-套餐头部
     * */
    private void bindHeadComboHead(final YBMBaseHolder baseViewHolder, final CartItemBean cartItemBean) {

        baseViewHolder.setText(R.id.cart_section_head02_price, "搭配套餐:¥" + UiUtils.transform(cartItemBean.getPrice()));
        baseViewHolder.setTextColor(R.id.cart_section_head02_price, cartItemBean.getValid() == 0 ? UiUtils.getColor(R.color.cart_tv01) : UiUtils.getColor(R.color.cart_head_tv01));
        baseViewHolder.setTextColor(R.id.tv_cart_section_head03_price, cartItemBean.getValid() == 0 ? UiUtils.getColor(R.color.cart_tv01) : UiUtils.getColor(R.color.color_9494A6));
        ((TextView) baseViewHolder.getView(R.id.cart_section_head03_price)).getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
        baseViewHolder.setText(R.id.cart_section_head03_price, "¥" + UiUtils.transform(cartItemBean.getOrigPrice()));
        baseViewHolder.setTextColor(R.id.cart_section_head03_price, cartItemBean.getValid() == 0 ? UiUtils.getColor(R.color.cart_tv01) : UiUtils.getColor(R.color.color_9494A6));
        bindCheckBox(baseViewHolder, cartItemBean);
    }

    /*
     * 头部样式4-套餐尾部
     * */
    private void bindHeadComboEnd(YBMBaseHolder baseViewHolder, CartItemBean cartItemBean) {

        baseViewHolder.setText(R.id.cart_head03_tv_subtotal, "小计:¥" + cartItemBean.getSubtotal() + "元");
        baseViewHolder.setTextColor(R.id.cart_head03_tv_subtotal, cartItemBean.getValid() == 0 ? UiUtils.getColor(R.color.cart_tv01) : UiUtils.getColor(R.color.cart_head_tv02));
        baseViewHolder.setText(R.id.tv_number, String.valueOf(cartItemBean.getAmount()));
        baseViewHolder.setGone(R.id.shop_no_tv01, cartItemBean.getValid() == 0);
        baseViewHolder.setGone(R.id.rl_layout, cartItemBean.getValid() != 0);
        bindNumAddOrSub(baseViewHolder, cartItemBean);
    }

    /*
     * 头部样式5-自营和非自营头部
     * */
    private void bindHeaderProprietaryHeadHead(final YBMBaseHolder baseViewHolder, final CartItemBean cartItemBean) {

        // 运费+包邮展示控制逻辑
        baseViewHolder.setGone(R.id.rl_freight_add_on_item_head, cartItemBean.freightTipsShowStatus == 1);//1是展示 0不展示
        // 运费+包邮提示如果展示才走里面的逻辑
        if (cartItemBean.freightTipsShowStatus == 1) {
            baseViewHolder.setText(R.id.tv_freight_add_on_item_tips, cartItemBean.getFreightTips());//包邮提示语
            baseViewHolder.setOnClickListener(R.id.tv_freight_add_on_item_tips, v -> {//包邮感叹号点击
                new FreightTipDialog(mContext).showTip(cartItemBean.getMainShopCode());
            });
            baseViewHolder.setGone(R.id.cart_new_tv_title_url, !TextUtils.isEmpty(cartItemBean.getFreightUrlText()));
            baseViewHolder.setText(R.id.cart_new_tv_title_url, cartItemBean.getFreightUrlText());//去凑单 展示
            baseViewHolder.setOnClickListener(R.id.cart_new_tv_title_url, (View.OnClickListener) v -> {//去凑单 点击去凑单包邮
                // RoutersUtils.open("ybmpage://freightaddonitem/" + ACTIVITY_TYPE_FROM_CART);
                RoutersUtils.open(cartItemBean.getFreightJumpUrl());
            });
            baseViewHolder.getView(R.id.tv_freight_add_on_item_tips).setEnabled(cartItemBean.getFreightIconShowStatus() == 1);//包邮感叹号是否可点击
            //包邮感叹号是否显示
            TextView tvFreightAddOnItemTips = baseViewHolder.getView(R.id.tv_freight_add_on_item_tips);
            tvFreightAddOnItemTips.setCompoundDrawablesWithIntrinsicBounds(null, null, cartItemBean.getFreightIconShowStatus() == 1 ? getResources().getDrawable(R.drawable.icon_cart_freight_add_on_item_normal) : null, null);
        }

        // 设置一级头部，小药药自营和第三方店铺名，并分别设置logo和跳转的点击事件
        TextView orderName = baseViewHolder.getView(R.id.tv_cart_proprietary);
        orderName.setText(cartItemBean.getCompanyName());


        if (cartItemBean.getIsThirdCompany() == 1) {
            orderName.setCompoundDrawablesWithIntrinsicBounds(mContext.getResources().getDrawable(R.drawable.icon_payment_pop), null, mContext.getResources().getDrawable(R.drawable.right_new), null);
            //  增加一个 店铺优惠券 的展示
            if (cartItemBean.getIsHaveVoucher() == 1) {
                baseViewHolder.getView(R.id.ll_coupon_wrapper).setVisibility(View.VISIBLE);
            } else {
                baseViewHolder.getView(R.id.ll_coupon_wrapper).setVisibility(View.GONE);
            }
            baseViewHolder.setOnClickListener(R.id.iv_coupon, v -> {
                bottomCartCouponDialog = new ShowBottomCartCouponDialog();
                bottomCartCouponDialog.setShopCode(cartItemBean.getShopCode());
                bottomCartCouponDialog.setShopName(cartItemBean.getShopName());
                bottomCartCouponDialog.setSkuIds(getSkuIds(cartItemBean.getShopItemList()));
                bottomCartCouponDialog.show(v);
                token = v;
                bottomCartCouponDialog.setOnSelectListener(dismissListener);
            });
        } else {
            // 自营店铺区域级 不展示优惠券图标
            baseViewHolder.getView(R.id.ll_coupon_wrapper).setVisibility(View.GONE);
            orderName.setCompoundDrawablesWithIntrinsicBounds(mContext.getResources().getDrawable(R.drawable.icon_autotrophy_new), null, mContext.getResources().getDrawable(R.drawable.right_new), null);
        }
        orderName.setOnClickListener(v -> RoutersUtils.open(cartItemBean.getShopJumpUrl()));
        bindCheckBox(baseViewHolder, cartItemBean);
    }

    /*
     * 头部样式6-商城头部样式
     * */
    private void bindHeadShop(final YBMBaseHolder baseViewHolder, final CartItemBean cartItemBean) {

        baseViewHolder.setText(R.id.tv_cart_proprietary, cartItemBean.getShopName());
        TextView orderName = baseViewHolder.getView(R.id.tv_cart_proprietary);
        //第三方药店
        if (!TextUtils.isEmpty(cartItemBean.getAppLinkUrl())) {
            orderName.setCompoundDrawablesWithIntrinsicBounds(null, null, mContext.getResources().getDrawable(R.drawable.right_new), null);
        } else {
            orderName.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        }

        baseViewHolder.setOnClickListener(R.id.tv_cart_proprietary, v -> {
            String url = cartItemBean.getAppLinkUrl();
            if (!TextUtils.isEmpty(url)) {
                if (!url.startsWith("ybmpage")) {
                    url = "ybmpage://commonh5activity?url=" + url;
                }
                RoutersUtils.open(url);
            }
        });

        baseViewHolder.setGone(R.id.icon_cart_proprietary, cartItemBean.getIsHaveVoucher() == 1);
        baseViewHolder.setOnClickListener(R.id.icon_cart_proprietary, v -> {//店铺 优惠券
            bottomCartCouponDialog = new ShowBottomCartCouponDialog();
            bottomCartCouponDialog.setShopCode(cartItemBean.getShopCode());
            bottomCartCouponDialog.setShopName(cartItemBean.getShopName());
            bottomCartCouponDialog.setSkuIds(getSkuIds(cartItemBean.getShopItemList()));
            bottomCartCouponDialog.show(v);
            token = v;
            bottomCartCouponDialog.setOnSelectListener(dismissListener);
        });
        bindCheckBox(baseViewHolder, cartItemBean);
        //运费提示
        TextView tipView = baseViewHolder.getView(R.id.tv_head_tip);
        if (cartItemBean.freightTipsShowStatus == 1) {
            tipView.setVisibility(View.VISIBLE);
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_hint_image_cart);
            if (drawable != null) {
                drawable.setBounds(0, 0, ConvertUtils.dp2px(13), ConvertUtils.dp2px(13));
                tipView.setCompoundDrawables(drawable, null, null, null);
                tipView.setText(cartItemBean.freightTips);
                tipView.setOnClickListener(v -> new FreightTipDialog(mContext).showTip(cartItemBean.getMainShopCode()));
            }
        } else {
            tipView.setVisibility(View.GONE);
        }
    }

    /*
     * 头部样式5-失效列表套餐头部
     * */
    private void bindHeaderLoseComboHead(final YBMBaseHolder baseViewHolder, final CartItemBean CartItemBean) {

        bindCheckBox(baseViewHolder, CartItemBean);
    }

    /*
     * 头部样式-显示返券头部样式
     * */
    private void bindReturnVoucherInfo(final YBMBaseHolder baseViewHolder, final CartItemBean cartItemBean) {

        String title = !TextUtils.isEmpty(cartItemBean.getTitleUrlText()) ? cartItemBean.getTitleUrlText() : "去凑单";
        final String titleUrl = cartItemBean.getTitleUrl();
        boolean isShowUrl = cartItemBean.getIsMatch() != 0;

        baseViewHolder.setText(R.id.cart_new_rl_tv01, cartItemBean.getTitle());
        baseViewHolder.setText(R.id.cart_new_tv_title_url, title);
        baseViewHolder.setGone(R.id.cart_new_tv_title_url, isShowUrl);

        baseViewHolder.getConvertView().setOnClickListener(v -> {
            if (!TextUtils.isEmpty(titleUrl) && isShowUrl) {
                RoutersUtils.open(titleUrl);
            }
        });
    }

    /*
     * 内容样式1-正常条目布局
     * */
    private void bindContent(final BaseViewHolder baseViewHolder, final CartItemBean cartItemBean) {


        //是否失效商品,0=失效商品，1=正常商品
        boolean isValid = cartItemBean.getValid() != 0;

        //图片上遮罩层是否显示，2是已售罄4是已下架95是超经营范围
        boolean isLoseEfficacy = !isValid && (!TextUtils.isEmpty(cartItemBean.getLoseTagText()) || cartItemBean.getSkuStatus() == 2 || cartItemBean.getSkuStatus() == 4 || cartItemBean.getSkuStatus() == 95 || cartItemBean.getSkuStatus() == 105);
        baseViewHolder.setGone(R.id.shop_no_limit_tv01, isLoseEfficacy);

        //中包装数量,不可拆零才显示,4.2.0以后所有的商品都显示
        boolean isShow = !TextUtils.isEmpty(cartItemBean.getMediumPackageTitle()) && isValid;
        baseViewHolder.setGone(R.id.shop_tv_limit, isShow);
        baseViewHolder.setText(R.id.shop_tv_limit, cartItemBean.getMediumPackageTitle());

        baseViewHolder.setGone(R.id.shop_price1, isValid);
        baseViewHolder.setGone(R.id.rl_layout, isValid);
        baseViewHolder.setGone(R.id.shop_no_tv01, !isValid);

        baseViewHolder.setTextColor(R.id.shop_name, !isValid ? UiUtils.getColor(R.color.cart_tv01) : UiUtils.getColor(R.color.black));
        //baseViewHolder.setTextColor(R.id.shop_description, !isValid ? UiUtils.getColor(R.color.cart_tv01) : UiUtils.getColor(R.color.cart_description));
        baseViewHolder.setTextColor(R.id.shop_price, !isValid ? UiUtils.getColor(R.color.cart_tv01) : UiUtils.getColor(R.color.cart_tv_shop_price));

        //包邮凑单提示 满80盒包邮，还差30盒
        baseViewHolder.setGone(R.id.tv_freight_tips, isValid);
        baseViewHolder.setText(R.id.tv_freight_tips, cartItemBean.getFreightTips());

        //控销专区
        baseViewHolder.setTextColor(R.id.shop_no_tv01, cartItemBean.getSkuStatus() == 91 ? UiUtils.getColor(R.color.tv_control) : UiUtils.getColor(R.color.cart_name));

        String sellOutStr = mContext.getResources().getString(R.string.text_sell_out);
        String soldOutStr = mContext.getResources().getString(R.string.text_sold_out);
        String loseEfficacyStr = !TextUtils.isEmpty(cartItemBean.getLoseTagText()) ? cartItemBean.getLoseTagText() : cartItemBean.getSkuStatus() == 2 ? sellOutStr : (cartItemBean.getSkuStatus() == 4 ? soldOutStr : cartItemBean.getSkuStatus() == 95 ? "超经\n营范围" : (cartItemBean.getSkuStatus() == 105 ? "战略合作" : ""));
        //小计价格
        String subtotalStr = "小计:¥" + UiUtils.transform(cartItemBean.getSubtotal());
        int subtotalStrLength = TextUtils.isEmpty(subtotalStr) ? 0 : subtotalStr.indexOf(".");
        SpannableStringBuilder subtotalNameStr = getShopName(subtotalStr, 4, subtotalStrLength, 16, 13);
        baseViewHolder.setText(R.id.shop_price1, subtotalNameStr);

        //单价和单位
        String priceStr = "¥" + UiUtils.transform(cartItemBean.getPrice());
        int nameLength = TextUtils.isEmpty(priceStr) ? 0 : priceStr.length();
        if (cartItemBean.getSku() != null) {
            ProductDetailBean product = cartItemBean.getSku();
            String productUnit = product.getProductUnit();
            priceStr = priceStr + "/" + productUnit;
        }
        SpannableStringBuilder priceNameStr = getShopName(priceStr, 0, nameLength, 15, 12);
        baseViewHolder.setText(R.id.shop_price, priceNameStr);

        baseViewHolder.setText(R.id.shop_no_limit_tv01, loseEfficacyStr);
        if (!TextUtils.isEmpty(cartItemBean.showPriceAfterDiscount)) {
            //LogUtils.tag("discount").e("折后价 = " + cartItemBean.showPriceAfterDiscount);
            baseViewHolder.setGone(R.id.shop_description, true);
            baseViewHolder.setText(R.id.shop_description, "预估到手价 ¥" + cartItemBean.showPriceAfterDiscount);
        } else {
            baseViewHolder.setGone(R.id.shop_description, false);
        }

        baseViewHolder.setText(R.id.tv_number, String.valueOf(cartItemBean.getAmount()));
        baseViewHolder.setTag(R.id.tv_number, cartItemBean.getSkuId());
        baseViewHolder.setText(R.id.shop_name, cartItemBean.getName());

        String cartSellOut = "该商品已售罄";
        String cartSoldOut = "该商品已下架";
        String cartJurisdictionOut = "暂无购买权限";
        String cartRestrictionOut = "该商品已限购";
        String cartBusinessScopeOut = "该商品超出经营范围";
        String cartOem = "价格签署协议可见";
        String cartPriceNull = "¥--";
        TextView shopNoTv = baseViewHolder.getView(R.id.shop_no_tv01);
        switch (cartItemBean.getSkuStatus()) {
            case CART_SELL_OUT:
                shopNoTv.setText(cartSellOut);
                break;
            case CART_SOLD_OUT:
                shopNoTv.setText(cartSoldOut);
                break;
            case CART_JURISDICTION_OUT:
                shopNoTv.setText(cartJurisdictionOut);
                baseViewHolder.setText(R.id.shop_price, cartPriceNull);
                break;
            case CART_RESTRICTION_OUT:
                shopNoTv.setText(cartRestrictionOut);
                break;
            case CART_BUSINESS_SCOPE_OUT:
                shopNoTv.setText(cartBusinessScopeOut);
                break;
            case CART_OEM:
                shopNoTv.setText(cartOem);
                baseViewHolder.setGone(R.id.shop_price, false);
                baseViewHolder.setGone(R.id.shop_price1, false);
                break;
            default:
                shopNoTv.setText("");
        }

        bindBtnClick(baseViewHolder, cartItemBean);
        bindCheckBox(baseViewHolder, cartItemBean);
        bindNumAddOrSub(baseViewHolder, cartItemBean);
        bindImageData(baseViewHolder, cartItemBean);
        bindStockAndBlackProduct(baseViewHolder, cartItemBean);
        TextView tv = baseViewHolder.getView(R.id.preferential_combo_tip);
        if (tv != null) {
            setPreferentialComboTip(tv, cartItemBean);
        }
        //比加入时降多少
        TagView dataTagListView = baseViewHolder.getView(R.id.data_tag_list_view);
        if (dataTagListView != null && cartItemBean.dataTagList != null && cartItemBean.dataTagList.size() > 0) {
            dataTagListView.setVisibility(View.VISIBLE);
            dataTagListView.bindData(cartItemBean.dataTagList);
        } else {
            dataTagListView.setVisibility(View.GONE);
        }
    }

    /**
     * 满减商品设置提示
     *
     * @param tv
     * @param cartItemBean
     */
    private void setPreferentialComboTip(TextView tv, CartItemBean cartItemBean) {
        if (tv != null) {
            if (cartItemBean.getPromoQty() > 0 && cartItemBean.getNormalQty() > 0 && cartItemBean.getSku() != null) {
                tv.setVisibility(View.VISIBLE);
            } else {
                tv.setVisibility(View.GONE);
                return;
            }
        }
        SpannableStringBuilder builder = new SpannableStringBuilder("限购");
        ForegroundColorSpan tagSpan = new ForegroundColorSpan(ContextCompat.getColor(mContext, R.color.color_ff982c));
        int tagLength = builder.length();
        builder.setSpan(tagSpan, 0, tagLength, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        ForegroundColorSpan contentSpan = new ForegroundColorSpan(ContextCompat.getColor(mContext, R.color.color_696975));
        builder.append(" 此商品为限购商品，超出")
                .append(String.valueOf(cartItemBean.getPromoQty()))
                .append(cartItemBean.getSku().productUnit);
        if (TextUtils.isEmpty(cartItemBean.showPriceAfterDiscount)) {
            builder.append("的部分将按原价购买");
        } else {
            builder.append("的部分将按照原价购买，超出部分也参与折后价计算");
        }
        builder.setSpan(contentSpan, tagLength, builder.length() - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        tv.setText(builder);
    }

    /*
     * 内容样式2-套餐条目布局
     * */
    private void bindContentCombo(final BaseViewHolder baseViewHolder, final CartItemBean cartItemBean) {

        baseViewHolder.setTextColor(R.id.shop_name, cartItemBean.getValid() == 0 ? UiUtils.getColor(R.color.cart_tv01) : UiUtils.getColor(R.color.cart_tv_shop_name));
        baseViewHolder.setText(R.id.shop_name, cartItemBean.getName());

        String mediumPackageNum = cartItemBean.getMediumPackageNum() + "";
        //单价和单位
        String priceStr = "¥" + UiUtils.transform(cartItemBean.getPrice());
        int nameLength = TextUtils.isEmpty(priceStr) ? 0 : priceStr.length();
        if (cartItemBean.getSku() != null) {
            ProductDetailBean product = cartItemBean.getSku();
            String productUnit = product.getProductUnit();
            priceStr = priceStr + "/" + productUnit;
            mediumPackageNum = mediumPackageNum + productUnit;
        }
        SpannableStringBuilder priceNameStr = getShopName(priceStr, 0, nameLength, 15, 12);
        baseViewHolder.setText(R.id.shop_price, priceNameStr);

        // 套餐中该商品的数量
        baseViewHolder.setText(R.id.shop_tv_number, "X" + cartItemBean.packageProductQty);

        //小计价格
        String subtotalStr = "小计:¥" + UiUtils.transform(cartItemBean.getSubtotal());
        int subtotalStrLength = TextUtils.isEmpty(subtotalStr) ? 0 : subtotalStr.indexOf(".");
        SpannableStringBuilder subtotalNameStr = getShopName(subtotalStr, 4, subtotalStrLength, 16, 13);
        baseViewHolder.setText(R.id.shop_price1, subtotalNameStr);

        if (!TextUtils.isEmpty(cartItemBean.showPriceAfterDiscount)) {
            baseViewHolder.setGone(R.id.shop_description, true);
            baseViewHolder.setText(R.id.shop_description, "预估到手价 ¥" + cartItemBean.showPriceAfterDiscount);
        } else {
            baseViewHolder.setGone(R.id.shop_description, false);
        }

        bindStockAndBlackProduct(baseViewHolder, cartItemBean);
        bindBtnClick(baseViewHolder, cartItemBean);
        bindImageData(baseViewHolder, cartItemBean);
    }

    private SpannableStringBuilder getShopName(String shopName, int nameLength, int LastNameLength, int size, int LastSize) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
        try {
            spannableString.setSpan(new AbsoluteSizeSpan(size, true), nameLength, LastNameLength + 1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
            spannableString.setSpan(new AbsoluteSizeSpan(LastSize, true), LastNameLength + 1, shopName.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }
        return spannableString;
    }

    /**
     * skuIds
     */
    private String getSkuIds(List<CartItemBean> list) {
        StringBuilder sb = new StringBuilder();
        if (list == null) {
            return null;
        }
        CartItemBean bean;
        for (int i = 0; i < list.size(); i++) {
            bean = list.get(i);
            if (bean.getItemType() == ITEMTYPE_CONTENT || bean.getItemType() == ITEMTYPE_PACKAGE_CONTENT) {//正常商品
                sb.append(list.get(i).getSkuId());
                sb.append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
            return sb.toString();
        }
        return null;
    }

    private String setTitle(int type) {
        switch (type) {
            default:
            case 1:
                return "满减";
            case 2:
                return "满折";
            case 3:
                return "满赠";
            case 4:
                return "满减赠";
            case 6:
                return "一口价";
            case 11:
                return "满返";
            case 12:
                return "满减返券";
            case 21:
                return "返券";
        }
    }

    private int setBgIcon(int type) {
        switch (type) {
            default:
            case 1:
            case 2:
            case 3:
            case 4:
            case 6:
                return R.drawable.bg_cart_money_off;
            case 21:
                return R.drawable.bg_cart_money_return_ticket;
        }
    }

    private int setColor(int type) {
        switch (type) {
            default:
            case 1:
            case 2:
            case 3:
            case 4:
            case 6:
                return R.color.color_FF2121;
            case 21:
                return R.color.color_ff982c;
        }
    }

    /*
     * 商品剩余多少件文案，是否显示==参与返点文案，是否显示
     * */
    private void bindStockAndBlackProduct(final BaseViewHolder baseViewHolder, final CartItemBean cartItemBean) {

        boolean isValid = cartItemBean.getValid() != 0;

        //商品剩余多少件文案，是否显示
        boolean isStockTitle = !TextUtils.isEmpty(cartItemBean.getStockTitle());

        baseViewHolder.setGone(R.id.shop_no_limit_tv02, isStockTitle && isValid);
        baseViewHolder.setText(R.id.shop_no_limit_tv02, cartItemBean.getStockTitle());
        // 满减icon
        ((TagView) baseViewHolder.getView(R.id.rl_icon_type)).bindData(cartItemBean.getTagList(), 3, true);

    }

    /*
     * 新惠减-图片-角标,3月6号，去掉新惠减高毛标签
     * */
    private void bindImageData(final BaseViewHolder baseViewHolder, final CartItemBean cartItemBean) {

        List<Integer> list = new ArrayList<>();
        if (cartItemBean.getValid() != 0) {

            if (cartItemBean.isGift()) {
                list.add(R.drawable.icon_procurement_festival);
            }

            if (cartItemBean.getAgent() == 1) {
                list.add(R.drawable.icon_exclusive);
            }

        }

        if (cartItemBean.getIsUsableMedicalStr() == 1) {
            list.add(R.drawable.icon_health_insurance);
        }

        SpannableStringBuilder shopName = getShopNameIcon(cartItemBean.getName(), list);
        if (!TextUtils.isEmpty(shopName)) baseViewHolder.setText(R.id.shop_name, shopName);

        //加载图片
        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + cartItemBean.getImageUrl())
                .diskCacheStrategy(DiskCacheStrategy.SOURCE).placeholder(R.drawable.jiazaitu_min)
                .into(((ImageView) baseViewHolder.getView(R.id.shop_photo)));
        //左上角-角标图片
        if (cartItemBean.getSku() != null) {
            ProductDetailBean product = cartItemBean.getSku();
            if (product.markerUrl != null && product.markerUrl.startsWith("http")) {
                ImageHelper.with(mContext).load(product.markerUrl).placeholder(R.drawable.transparent)
                        .error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate().dontTransform().into(((ImageView) baseViewHolder.getView(R.id.iv_shop_mark)));
            } else {
                if (TextUtils.isEmpty(product.markerUrl)) {
                    ImageHelper.with(mContext).load(R.drawable.transparent).into(((ImageView) baseViewHolder.getView(R.id.iv_shop_mark)));
                } else {
                    ImageHelper.with(mContext).load(AppNetConfig.LORD_TAG + product.markerUrl)
                            .placeholder(R.drawable.transparent).error(R.drawable.transparent)
                            .diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
                            .into(((ImageView) baseViewHolder.getView(R.id.iv_shop_mark)));
                }
            }
        }
    }

    /*
     * 勾选状态
     * */
    private void bindCheckBox(final BaseViewHolder baseViewHolder, final CartItemBean cartItemBean) {
        baseViewHolder.setChecked(R.id.shop_check, cartItemBean.getStatus() == 1);

        boolean isGroup;
        CheckBox shopCheck = baseViewHolder.getView(R.id.shop_check);
        if (cartItemBean.getItemType() == CartItemBean.header_combo_head
                || cartItemBean.getItemType() == CartItemBean.header_lose_combo_head) {
            isGroup = true;
            baseViewHolder.setTag(R.id.shop_check, cartItemBean.getPackageId() + "");
        } else {

            isGroup = false;
            if (cartItemBean.getItemType() == CartItemBean.header_proprietary_head) {
                //公司
                baseViewHolder.setTag(R.id.shop_check, cartItemBean.getOrgId());
            } else if (cartItemBean.getItemType() == CartItemBean.header_shop_head) {
                //店铺
                baseViewHolder.setTag(R.id.shop_check, cartItemBean.getShopCode());
            } else {
                baseViewHolder.setTag(R.id.shop_check, cartItemBean.getSkuId() + "");
            }

        }

        UiUtils.expandViewTouchDelegate(shopCheck, ConvertUtils.dp2px(100), ConvertUtils.dp2px(100), ConvertUtils.dp2px(100), ConvertUtils.dp2px(100));
        baseViewHolder.setOnClickListener(R.id.shop_check, new onClickListener(shopCheck, isGroup, cartItemBean.getItemType(), cartItemBean.getIsThirdCompany(), baseViewHolder.getAdapterPosition()));
        baseViewHolder.setOnCheckedChangeListener(R.id.shop_check, (buttonView, isChecked) -> {

        });

    }

    /*
     * 数量加减
     * */
    private void bindNumAddOrSub(final BaseViewHolder baseViewHolder, final CartItemBean cartItemBean) {

        int packageNum = MEDIUM_PACKAGE_NUMBER;//中包装数量 默认为1
        int split = IS_SPLIT;//是否可拆零 0:不可拆零；1:可拆零 默认1
        boolean isGroup = cartItemBean.getItemType() == CartItemBean.header_combo_end || cartItemBean.getItemType() == CartItemBean.header_combo_lose_end
                || cartItemBean.getItemType() == CartItemBean.header_lose_combo_head;

        //是否可拆零
        split = cartItemBean.getIsSplit();

        //中包装数量
        packageNum = cartItemBean.getMediumPackageNum();
        if (packageNum <= 1) {
            packageNum = 1;
        }

        final boolean isSplit = (split == IS_SPLIT);

        cartItemBean.setMediumPackageNum(packageNum);
        //加减数量
        baseViewHolder.setTag(R.id.iv_numSub, cartItemBean);
        baseViewHolder.setTag(R.id.iv_numAdd, cartItemBean);
        baseViewHolder.getView(R.id.iv_numSub).setOnClickListener(v -> {
            int amount = cartItemBean.getAmount();
            int skuId = cartItemBean.getSkuId();
            int packageId = cartItemBean.getPackageId();
            int packageNum1 = cartItemBean.getMediumPackageNum();
            number = amount;
            clickDelayUtil.checkClick(true, new Function2<Integer, Boolean, Unit>() {
                @Override
                public Unit invoke(Integer count, Boolean aBoolean) {
                    if (aBoolean) {
                        clickDelayUtil.pushTask(new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                if (isSplit) {
                                    number--;
                                } else {
                                    if (number >= packageNum1) {
                                        number -= packageNum1;
                                    } else {
                                        number = 0;
                                    }
                                }

                                if (number > 0) {
                                    if (isGroup) {
                                        getCartNum(number, packageId, true);
                                    } else {
                                        getCartNum(number, skuId, false);
                                    }
                                } else {
                                    if (mOnSwipeListener != null)
                                        mOnSwipeListener.onRemoveProduct(baseViewHolder.getAdapterPosition());
                                }
                                return null;
                            }
                        });
                    } else {
                        int num = isGroup? number - 1: number - packageNum1;
                        if (num > 0) {
                            cartItemBean.setAmount(num);
                            notifyDataSetChanged();
                        }
                    }
                    return null;
                }
            });
        });
        baseViewHolder.getView(R.id.iv_numAdd).setOnClickListener(v -> {
            int amount = cartItemBean.getAmount();
            int skuId = cartItemBean.getSkuId();
            int packageId = cartItemBean.getPackageId();
            int packageNum12 = cartItemBean.getMediumPackageNum();
            number = amount;
            clickDelayUtil.checkClick(true, new Function2<Integer, Boolean, Unit>() {
                @Override
                public Unit invoke(Integer count, Boolean aBoolean) {
                    if (aBoolean) {
                        clickDelayUtil.pushTask(new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                if (isGroup) {
                                    number++;
                                    getCartNum(number, packageId, true);
                                } else {
                                    number += packageNum12;
                                    getCartNum(number, skuId, false);
                                }
                                return null;
                            }
                        });
                    } else {
                        cartItemBean.setAmount(isGroup? number + 1: number + packageNum12);
                        notifyDataSetChanged();
                    }
                    return null;
                }
            });
//            if (isGroup) {
//                number++;
//                getCartNum(number, packageId, true);
//            } else {
//                number += packageNum12;
//                getCartNum(number, skuId, false);
//            }
        });
        //编辑弹出对话框加减数量
        baseViewHolder.getView(R.id.tv_number).setOnClickListener(v -> {
            int amount = cartItemBean.getAmount();
            int packageNum13 = cartItemBean.getMediumPackageNum();
            number = amount;

            if (mContext instanceof BaseActivity) {
                ((BaseActivity) mContext).hideSoftInput();
            }
            DialogUtil.addOrSubDialog(((BaseActivity) v.getContext()), InputType.TYPE_CLASS_NUMBER, number + "", packageNum13, isSplit, true, new DialogUtil.DialogClickListener() {

                private InputMethodManager mImm;

                @Override
                public void confirm(String content) {

                    sendShopNum(baseViewHolder.getAdapterPosition(), cartItemBean, content);
                }

                @Override
                public void cancel() {

                }

                @Override
                public void showSoftInput(final View view) {
                    try {
                        if (mImm == null) {
                            mImm = (InputMethodManager) (view.getContext()).getSystemService(Context.INPUT_METHOD_SERVICE);
                        }
                        if (mImm != null) {
                            mImm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
                        }
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }
            });
        });
    }

    /*
     * item点击和侧滑删除
     * */
    private void bindBtnClick(final BaseViewHolder baseViewHolder, final CartItemBean cartItemBean) {
        baseViewHolder.getView(R.id.btnDelete).setOnClickListener(v -> {
            if (null != mOnSwipeListener) {
                //且如果想让侧滑菜单同时关闭，需要同时调用 ((CstSwipeDelMenu) holder.itemView).quickClose();
                ((SwipeMenuLayout) baseViewHolder.itemView).quickClose();
                mOnSwipeListener.onDel(baseViewHolder.getAdapterPosition());
            }
        });
        //收藏
        baseViewHolder.setGone(R.id.btnCollect, !SpUtil.isKa());
        baseViewHolder.getView(R.id.btnCollect).setOnClickListener(v -> {
            if (null != mOnSwipeListener) {
                ((SwipeMenuLayout) baseViewHolder.itemView).quickClose();
                mOnSwipeListener.onCollect(cartItemBean.getSkuId());
            }
        });

        //注意事项，设置item点击，不能对整个holder.itemView设置咯，只能对第一个子View，即原来的content设置，这算是局限性吧。
        baseViewHolder.getView(R.id.fg).setOnClickListener(v -> {
            if (mOnItemClickListener != null) {
                mOnItemClickListener.onItemClick(cartItemBean);
            }
        });
    }

    /*
     * 设置新-惠-icon在药品名称之前
     * */
    private SpannableStringBuilder getShopNameIcon(String shopName, List<Integer> icons) {
        if (icons != null && icons.size() > 0) {
            SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = mContext.getResources().getDrawable(icons.get(i));
                //适配图标大小问题
                if (icons.get(i) == R.drawable.icon_exclusive) {//82 34  78  42
                    drawable.setBounds(0, 0, ConvertUtils.dp2px(24), ConvertUtils.dp2px(13));
                } else {
                    drawable.setBounds(0, 0, ConvertUtils.dp2px(15), ConvertUtils.dp2px(15));
                }

                MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                //占个位置
                spannableString.insert(0, "-");
                spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
            return spannableString;
        }
        return null;
    }

    /*
     * 勾选商品
     * */
    public class onClickListener implements View.OnClickListener {

        private boolean isGroup;
        private int position;
        private CheckBox cb;
        private int itemType;
        private int isThirdCompany;

        private onClickListener(CheckBox cb, boolean isGroup, int itemType, int isThirdCompany, int position) {
            this.cb = cb;
            this.isGroup = isGroup;
            this.position = position;
            this.itemType = itemType;
            this.isThirdCompany = isThirdCompany;
        }

        @Override
        public void onClick(View v) {

            String skuId = (String) v.getTag();
            if (v.getId() == R.id.shop_check) {
                getIsCheckSelected().remove(Integer.valueOf(position));
                if (cb.isChecked()) {
                    getIsCheckSelected().add(position);
                }
                //如果所有的物品全部被选中，则全选按钮也默认被选中
//                    mHandler.sendMessage(mHandler.obtainMessage(11, isAllCheckSelected()));
                if (itemType == CartItemBean.header_proprietary_head
                        || itemType == CartItemBean.header_shop_head) {
                    selectOrCancelAllItem(cb.isChecked(), skuId, isThirdCompany, itemType);
                } else {
                    selectOrCancelItem(cb.isChecked(), skuId, isGroup);
                }

            }
        }

    }

    /**
     * 购买商品
     * url =>  "changeCart"
     *
     * @param amount  数量
     * @param id      套餐id或者商品id
     * @param isGroup 是否是套餐或者普通商品
     */
    private void getCartNum(final int amount, final int id, final boolean isGroup) {
        if (id < 0) {
            return;
        }

        //服务器同步
        ((BaseActivity) mContext).showProgress();
        RequestParams params = new RequestParams();
        JgRequestParams jgRequestParams = new JgRequestParams();
        String merchantid = SpUtil.getMerchantid();
        params.put("merchantId", merchantid);
        if (isGroup) {
            params.put("packageId", String.valueOf(id));
        } else {
            params.put("skuId", String.valueOf(id));
        }
        params.put("amount", String.valueOf(amount));
        try {
            if (JGTrackManager.Companion.getSuperProperty(mContext,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) != null){
                String searchSortStrategyCode = (String)JGTrackManager.Companion.getSuperProperty(mContext,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) ;
//                params.put("searchSortStrategyCode",searchSortStrategyCode);
                jgRequestParams.setSearch_sort_strategy_id(searchSortStrategyCode);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        if (jgTrackBean!=null ){
            if (jgTrackBean.getEntrance() != null && !jgTrackBean.getEntrance().isEmpty()){
//                params.put("entrance",jgTrackBean.getEntrance());
                jgRequestParams.setEntrance(jgTrackBean.getEntrance());
            }
            if (jgTrackBean.getActivityEntrance() != null && !jgTrackBean.getActivityEntrance().isEmpty()){
//                params.put("activityEntrance",jgTrackBean.getActivityEntrance());
                jgRequestParams.setActivity_entrance(jgTrackBean.getActivityEntrance());
            }
        }
        if(JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo() != null){
            JgOperationPositionInfo mJgOperationInfo = JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo();
            if (mJgOperationInfo.getProductId()!= null && !mJgOperationInfo.getProductId().isEmpty() && Objects.equals(mJgOperationInfo.getProductId(), String.valueOf(id))){
                if (mJgOperationInfo.getOperationId()!=null){
//                    params.put("operationId", mJgOperationInfo.getOperationId());
                    jgRequestParams.setOperation_id(mJgOperationInfo.getOperationId());
                }
                if (mJgOperationInfo.getOperationRank() != null){
//                    params.put("operationRank", mJgOperationInfo.getOperationRank().toString());
                    jgRequestParams.setOperation_rank(mJgOperationInfo.getOperationRank());
                }

                if (mJgOperationInfo.getRank() != null){
//                    params.put("rank", mJgOperationInfo.getRank().toString());
                    jgRequestParams.setRank(mJgOperationInfo.getRank());
                }
            }
        }
        if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean() != null){
            RowsBean mJgSearchRowsBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean();
            if (mJgSearchRowsBean.getProductId()!=null && !mJgSearchRowsBean.getProductId().isEmpty() && Objects.equals(mJgSearchRowsBean.getProductId(), String.valueOf(id))){
                jgRequestParams.setList_position_type(mJgSearchRowsBean.positionType+"");
                if (mJgSearchRowsBean.positionTypeName != null){
                    jgRequestParams.setList_position_typename(mJgSearchRowsBean.positionTypeName);
                }
                if (mJgSearchRowsBean.searchKeyword != null){
                    jgRequestParams.setKey_word(mJgSearchRowsBean.searchKeyword);
                }
                jgRequestParams.setProduct_id(mJgSearchRowsBean.getProductId());
                jgRequestParams.setProduct_name(mJgSearchRowsBean.getProductName());
                jgRequestParams.setProduct_first(mJgSearchRowsBean.categoryFirstId);
                jgRequestParams.setProduct_number(mJgSearchRowsBean.getProductNumber());
                jgRequestParams.setProduct_price(mJgSearchRowsBean.getJgProductPrice());
                jgRequestParams.setProduct_type(String.valueOf(mJgSearchRowsBean.productType));
                jgRequestParams.setProduct_activity_type(mJgSearchRowsBean.productActivityType);
                jgRequestParams.setProduct_shop_code(mJgSearchRowsBean.shopCode);
                jgRequestParams.setProduct_shop_name(mJgSearchRowsBean.shopName);

                if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField() != null){
                    jgRequestParams.setRank(JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getRank());
                    JGPageListCommonBean mJgPageListCommonBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getMJgPageListCommonBean();
                    if (mJgPageListCommonBean != null){
                        jgRequestParams.setSptype(mJgPageListCommonBean.getSptype());
                        jgRequestParams.setJgspid(mJgPageListCommonBean.getJgspid());
                        jgRequestParams.setSid(mJgPageListCommonBean.getSid());
                        jgRequestParams.setPage_no(mJgPageListCommonBean.getPage_no());
                        jgRequestParams.setResult_cnt(mJgPageListCommonBean.getResult_cnt());
                        jgRequestParams.setPage_size(mJgPageListCommonBean.getPage_size());
                        jgRequestParams.setTotal_page(mJgPageListCommonBean.getTotal_page());
                    }

                }
            }
        }
        jgRequestParams.setProduct_number(amount);
        jgRequestParams.setDirect("3");
        jgRequestParams.setSession_id(TrackManager.getSessionId(YBMAppLike.getAppContext()));
        params.put("mddata",new Gson().toJson(jgRequestParams));
        if (jgTrackBean != null && jgTrackBean.getEntrance() != null && jgTrackBean.getEntrance().contains(JGTrackManager.TrackShoppingCart.TITLE)){ //购物车只传个direct = "3"
            params.getParamsMap().remove("mddata");
        }

        FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, null, AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_CART);
        addCartCallback.invoke(false);
        HttpManager.getInstance().post(AppNetConfig.BUY_COMMODITY, params, new BaseResponse<CartDataBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CartDataBean> obj, CartDataBean cartDataBean) {
                addCartCallback.invoke(true);
                if (null != obj) {
                    if (obj.isSuccess()) {
                        mHandler.sendMessage(mHandler.obtainMessage(14, true));
                        //数据库更新
                        int num = 0;
                        if (cartDataBean != null) {
                            num = cartDataBean.qty;
                        }
                        if (isGroup) {
                            goodsDao.updateItem(id, num, true);
                        } else {
                            goodsDao.updateItem(id, num, false);
                        }
                        //广播通知购物车刷新
                        //LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SHOPNUMBER));
                        LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_BUY_PRODUCT));

                    } else {
                        ((BaseActivity) mContext).dismissProgress();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                ((BaseActivity) mContext).dismissProgress();
                addCartCallback.invoke(true);
            }

        });
    }

    /**
     * 购买商品
     * url => "changeCart"
     *
     * @param pos          商品下标
     * @param cartItemBean 购物车bean
     * @param str          购买数量
     */
    private void sendShopNum(int pos, final CartItemBean cartItemBean, String str) {
        //获取商品的数量
        int num;
        try {
            num = Integer.parseInt(str);
        } catch (Exception e) {
            num = 0;
        }
        if (num <= 0) {
            if (mOnSwipeListener != null) mOnSwipeListener.onRemoveProduct(pos);
            return;
        }
        number = num;
        //将用户更改的商品数量更新到服务器
        final int skuId = cartItemBean.getSkuId();
        final int packageId = cartItemBean.getPackageId();
        String merchantid = SpUtil.getMerchantid();
        //服务器同步
        ((BaseActivity) mContext).showProgress();
        RequestParams params = new RequestParams();
        JgRequestParams jgRequestParams = new JgRequestParams();
        params.put("merchantId", merchantid);
        if (cartItemBean.getItemType() == CartItemBean.header_combo_end || cartItemBean.getItemType() == CartItemBean.header_combo_lose_end
                || cartItemBean.getItemType() == CartItemBean.header_lose_combo_head) {
            params.put("packageId", String.valueOf(packageId));
        } else {
            params.put("skuId", String.valueOf(skuId));
        }
        params.put("amount", String.valueOf(number));
        FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, flowData, AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_CART);
        try {
            if (JGTrackManager.Companion.getSuperProperty(mContext,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) != null){
                String searchSortStrategyCode = (String)JGTrackManager.Companion.getSuperProperty(mContext,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) ;
//                params.put("searchSortStrategyCode",searchSortStrategyCode);
                jgRequestParams.setSearch_sort_strategy_id(searchSortStrategyCode);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        if (jgTrackBean!=null ){
            if (jgTrackBean.getEntrance() != null && !jgTrackBean.getEntrance().isEmpty()){
//                params.put("entrance",jgTrackBean.getEntrance());
                jgRequestParams.setEntrance(jgTrackBean.getEntrance());
            }
            if (jgTrackBean.getActivityEntrance() != null && !jgTrackBean.getActivityEntrance().isEmpty()){
//                params.put("activityEntrance",jgTrackBean.getActivityEntrance());
                jgRequestParams.setActivity_entrance(jgTrackBean.getActivityEntrance());
            }
        }
        if(JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo() != null){
            JgOperationPositionInfo mJgOperationInfo = JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo();
            if (mJgOperationInfo.getProductId()!= null && !mJgOperationInfo.getProductId().isEmpty() && Objects.equals(mJgOperationInfo.getProductId(), String.valueOf(skuId))){
                if (mJgOperationInfo.getOperationId()!=null){
//                    params.put("operationId", mJgOperationInfo.getOperationId());
                    jgRequestParams.setOperation_id(mJgOperationInfo.getOperationId());
                }
                if (mJgOperationInfo.getOperationRank() != null){
//                    params.put("operationRank", mJgOperationInfo.getOperationRank().toString());
                    jgRequestParams.setOperation_rank(mJgOperationInfo.getOperationRank());
                }

                if (mJgOperationInfo.getRank() != null){
//                    params.put("rank", mJgOperationInfo.getRank().toString());
                    jgRequestParams.setRank(mJgOperationInfo.getRank());
                }
            }
        }
        if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean() != null){
            RowsBean mJgSearchRowsBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean();
            if (mJgSearchRowsBean.getProductId()!=null && !mJgSearchRowsBean.getProductId().isEmpty() && Objects.equals(mJgSearchRowsBean.getProductId(), String.valueOf(skuId))){
                jgRequestParams.setList_position_type(mJgSearchRowsBean.positionType+"");
                if (mJgSearchRowsBean.positionTypeName != null){
                    jgRequestParams.setList_position_typename(mJgSearchRowsBean.positionTypeName);
                }
                if (mJgSearchRowsBean.searchKeyword != null){
                    jgRequestParams.setKey_word(mJgSearchRowsBean.searchKeyword);
                }
                jgRequestParams.setProduct_id(mJgSearchRowsBean.getProductId());
                jgRequestParams.setProduct_name(mJgSearchRowsBean.getProductName());
                jgRequestParams.setProduct_first(mJgSearchRowsBean.categoryFirstId);
                jgRequestParams.setProduct_number(mJgSearchRowsBean.getProductNumber());
                jgRequestParams.setProduct_price(mJgSearchRowsBean.getJgProductPrice());
                jgRequestParams.setProduct_type(String.valueOf(mJgSearchRowsBean.productType));
                jgRequestParams.setProduct_activity_type(mJgSearchRowsBean.productActivityType);
                jgRequestParams.setProduct_shop_code(mJgSearchRowsBean.shopCode);
                jgRequestParams.setProduct_shop_name(mJgSearchRowsBean.shopName);

                if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField() != null){
                    jgRequestParams.setRank(JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getRank());
                    JGPageListCommonBean mJgPageListCommonBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getMJgPageListCommonBean();
                    if (mJgPageListCommonBean != null){
                        jgRequestParams.setSptype(mJgPageListCommonBean.getSptype());
                        jgRequestParams.setJgspid(mJgPageListCommonBean.getJgspid());
                        jgRequestParams.setSid(mJgPageListCommonBean.getSid());
                        jgRequestParams.setPage_no(mJgPageListCommonBean.getPage_no());
                        jgRequestParams.setResult_cnt(mJgPageListCommonBean.getResult_cnt());
                        jgRequestParams.setPage_size(mJgPageListCommonBean.getPage_size());
                        jgRequestParams.setTotal_page(mJgPageListCommonBean.getTotal_page());
                    }

                }
            }
        }

        jgRequestParams.setProduct_number(number);
        jgRequestParams.setDirect("3");
        jgRequestParams.setSession_id(TrackManager.getSessionId(YBMAppLike.getAppContext()));
        params.put("mddata",new Gson().toJson(jgRequestParams));
        if (jgTrackBean != null && jgTrackBean.getEntrance() != null && jgTrackBean.getEntrance().contains(JGTrackManager.TrackShoppingCart.TITLE)){ //购物车只传个direct = "3"
            params.getParamsMap().remove("mddata");
        }

        HttpManager.getInstance().post(AppNetConfig.BUY_COMMODITY, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onFailure(NetError error) {
                ((BaseActivity) mContext).dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                if (null != obj) {
                    if (obj.isSuccess()) {
                        mHandler.sendMessage(mHandler.obtainMessage(14, true));
                        //数据库更新
                        if (cartItemBean.getItemType() == CartItemBean.header_combo_end || cartItemBean.getItemType() == CartItemBean.header_combo_lose_end
                                || cartItemBean.getItemType() == CartItemBean.header_lose_combo_head) {
                            goodsDao.updateItem(packageId, number, true);
                        } else {
                            goodsDao.updateItem(skuId, number, false);
                        }
                        notifyDataSetChanged();
                        LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SHOPNUMBER));
                        LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_BUY_PRODUCT));

                    } else {
                        notifyDataSetChanged();
                    }
                }

            }

        });
    }

    /**
     * 选择或者取消单个商品
     * 购物车选择单个商品url => "selectItem"
     * 购物车取消选择单个商品 => "cancelItem"
     *
     * @param isCheck 选中状态判断是否是取消或者选择
     * @param id      商品或者套餐id
     * @param isGroup 是否是商品或者套餐
     */
    private void selectOrCancelItem(boolean isCheck, String id, boolean isGroup) {
        ((BaseActivity) mContext).showProgress();
        String netUrl;
        RequestParams params = new RequestParams();
        String merchantid = SpUtil.getMerchantid();
        params.put("merchantId", merchantid);

        if (isGroup) {
            params.put("packageId", String.valueOf(id));
        } else {
            params.put("skuId", String.valueOf(id));
        }

        if (isCheck) {
            netUrl = AppNetConfig.SELECT_ITEM;
        } else {
            netUrl = AppNetConfig.CANCEL_ITEM;
        }
        HttpManager.getInstance().post(netUrl, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                mHandler.sendMessage(mHandler.obtainMessage(14, true));

            }

        });
    }

    /**
     * 全选or全取消
     *
     * @param isChecked 是否全部选中
     *                  购物车取消选择所有商品 url => "cancelAllItem"
     *                  购物车选择所有商品 url => "selectAllItem"
     */
    private void selectOrCancelAllItem(final boolean isChecked, String id, int isThirdCompany, int itemType) {
        ((BaseActivity) mContext).showProgress();


        String netUrl;
        //不要在初始化的时候取值，因切换账户，所以id要随用随取
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        if (!TextUtils.isEmpty(id)) {
            if (itemType == CartItemBean.header_proprietary_head || itemType == CartItemBean.header_shop_head) {
                params.put("orgId", id);
            }
        }
        params.put("isThirdCompany", isThirdCompany + "");

        if (isChecked) {
            netUrl = AppNetConfig.SELECT_ALL_ITEM;
        } else {
            netUrl = AppNetConfig.CANCEL_ALL_ITEM;
        }
        HttpManager.getInstance().post(netUrl, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                if (obj != null && obj.isSuccess()) {
                    mHandler.sendMessage(mHandler.obtainMessage(14, true));
                }
            }

            @Override
            public void onFailure(NetError error) {
                ((BaseActivity) mContext).dismissProgress();
//                if (!getFinishOrEdit()) {
//                    checkSelectedNo(isChecked);
//                }
            }
        });
    }

    /**
     * 判断是否购物车中所有的商品全部被选中
     *
     * @return true所有条目全部被选中 false还有条目没有被选中
     */
    public boolean isAllSelected() {
        return isSelected.size() == mTotal;
    }

    public boolean isAllCheckSelected() {
        return isCheckSelected.size() == mTotal_normal;
    }

    /*
     * 是否有被选中的商品
     * */
    public boolean isCheckSelected() {
        return isCheckSelected.size() > 0;
    }

    /*
     * 勾选状态
     * */
    public void setCheckBoxVisibility(int visibility) {
        if (isVisibility == visibility) {
            return;
        } else {
            if (visibility == View.GONE || visibility == View.VISIBLE) {
                isVisibility = visibility;
            } else {
                isVisibility = View.GONE;
            }
            notifyDataSetChanged();
        }
    }

    public boolean isCheck(int position) {
        return isSelected.contains(position);
    }

    public static List<Integer> getIsSelected() {
        return isSelected;
    }

    public static void setIsSelected(List<Integer> isSelected) {
        ShopCheckAdapter.isSelected = isSelected;
    }

    public static List<Integer> getIsCheckSelected() {
        return isCheckSelected;
    }

    public static void setIsCheckSelected(List<Integer> isCheckSelected) {
        ShopCheckAdapter.isCheckSelected = isCheckSelected;
    }

    public boolean getFinishOrEdit() {
        return finishOrEdit;
    }

    public void setFinishOrEdit(boolean finishOrEdit) {
        ShopCheckAdapter.finishOrEdit = finishOrEdit;
    }

    /**
     * 和Activity通信的接口
     */
    public interface onSwipeListener {
        void onDel(int pos);

        void onCollect(int pos);

        void onAllDel(int pos);

        void onRemoveProduct(int pos);
    }

    private onSwipeListener mOnSwipeListener;

    public onSwipeListener getOnDelListener() {
        return mOnSwipeListener;
    }

    public void setOnDelListener(onSwipeListener mOnDelListener) {
        this.mOnSwipeListener = mOnDelListener;
    }

    public interface OnCartItemClickListener {
        void onItemClick(CartItemBean rows);
    }

    private OnCartItemClickListener mOnItemClickListener = null;

    public void setOnItemClickListener(OnCartItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }

    /**
     * 展示上次展示的底部弹框
     */
    public void showBottomCouponDialog() {
        if (bottomCartCouponDialog == null || token == null) return;
        bottomCartCouponDialog.getData();
        bottomCartCouponDialog.show(token);
    }

    /**
     * 监听弹框消失
     *
     * @param dismissListener
     */
    public void setOnSelectListener(BaseBottomPopWindow.OnSelectListener dismissListener) {
        this.dismissListener = dismissListener;
    }

    public void setAddCartCompletedListener(Function1<Boolean, Unit> callback) {
        this.addCartCallback = callback;
    }
}


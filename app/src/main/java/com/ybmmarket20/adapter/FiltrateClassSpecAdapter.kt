package com.ybmmarket20.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.widget.CheckBox
import androidx.core.text.isDigitsOnly
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

class FiltrateClassSpecAdapter(layoutResId: Int, val list: MutableList<SearchFilterBean>?)
    : YBMBaseAdapter<SearchFilterBean>(layoutResId, list) {
    var mCallback: ((selectedSpecCount: String, isSelect: Boolean)-> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchFilterBean?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            val cbItem = baseViewHolder.getView<CheckBox>(R.id.cb_item)
            val specCount = if (!bean.specCount.isNullOrEmpty()) " (${bean.specCount})" else ""
            val specCountSpannable = SpannableStringBuilder(specCount).also {
                it.setSpan(ForegroundColorSpan(Color.parseColor(if(bean.isSelected) "#00B377" else "#9090A1")), 0, it.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            cbItem.text = SpannableStringBuilder(bean.key ?: "").append(specCountSpannable)
            cbItem.isChecked = bean.isSelected
            holder.setOnClickListener(R.id.ll_item) {
                bean.apply {
                    isSelected = !isSelected
                    notifyDataSetChanged()
                    if (mCallback != null) {
                        mCallback!!.invoke(bean.key, isSelected)
                    }
                }
            }
        }
    }

    fun setOnClickListener(callback: ((selectedSpecCount: String, isSelect: Boolean)-> Unit)?) {
        mCallback = callback
    }
}
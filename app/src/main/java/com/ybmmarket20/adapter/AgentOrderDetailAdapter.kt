package com.ybmmarket20.adapter

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.widget.ImageView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.OrderDetailDto
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils

/**
 * 订单详情适配器
 */
class AgentOrderDetailAdapter(
        layoutId: Int,
        data: List<OrderDetailDto>
): YBMBaseAdapter<OrderDetailDto>(layoutId, data) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: OrderDetailDto?) {
        if(baseViewHolder == null) return
        t?.let {
            baseViewHolder.setText(R.id.iv_agent_order_goods_name, it.productName)
            baseViewHolder.setText(R.id.iv_agent_order_goods_spec, it.spec)
            baseViewHolder.setText(R.id.iv_agent_order_goods_price, "${mContext.resources.getString(R.string.price_with_symbol)}¥${it.productPrice}")
            baseViewHolder.setText(R.id.iv_agent_order_goods_rate, "${mContext.resources.getString(R.string.goods_rate_with_symbol)}${it.grossMargin?: ""}")
            baseViewHolder.setText(R.id.tv_agent_order_goods_count ,"x${it.productAmount}")
            val builder = SpannableStringBuilder("小计:")
            builder.setSpan(AbsoluteSizeSpan(ConvertUtils.dp2px(11f)), 0, builder.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            builder.append(UiUtils.getPriceWithFormat("¥${it.subTotal}", 11))
            baseViewHolder.setText(R.id.tv_agent_order_goods_amount, builder)

            val ivAgentOrderGoods = baseViewHolder.getView<ImageView>(R.id.iv_agent_order_goods)
            ImageHelper.with(mContext).load("${AppNetConfig.LORD_IMAGE}${t.imageUrl}")
                    .placeholder(R.drawable.jiazaitu_min)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .dontAnimate()
                    .into(ivAgentOrderGoods)
            baseViewHolder.convertView.setOnClickListener{RoutersUtils.open("ybmpage://productdetail/${t.skuId}")}
        }
    }

}
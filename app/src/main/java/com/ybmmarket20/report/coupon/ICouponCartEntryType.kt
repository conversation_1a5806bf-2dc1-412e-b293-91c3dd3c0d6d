package com.ybmmarket20.report.coupon

/**
 * 购物车优惠券入口
 */
interface ICouponCartEntryType {
    /**
     * 购物车顶部
     */
    fun getCouponCartTopEntryType(): ICouponEntryType

    /**
     * 购物车店铺右侧
     */
    fun getCouponCartShopEntryType(): ICouponEntryType

    /**
     * 购物车凑单
     */
    fun getCouponCartAddGoodsType(): ICouponEntryType
}

/**
 * 购物车顶部
 */
class CartTopCouponCartEntryType: ICouponEntryType {
    override fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_CART_TOP
}

/**
 * 购物车店铺右侧
 */
class CartShopCouponEntryType: ICouponEntryType {
    override fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_CART_SHOP
}

/**
 * 购物车凑单
 */
class CartAddGoodCouponEntryType: ICouponEntryType {
    override fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_CART_ADD_GOODS
}

class DefaultCouponCartEntryType: ICouponCartEntryType {
    override fun getCouponCartTopEntryType(): ICouponEntryType = CartTopCouponCartEntryType()

    override fun getCouponCartShopEntryType(): ICouponEntryType = CartShopCouponEntryType()

    override fun getCouponCartAddGoodsType(): ICouponEntryType = CartAddGoodCouponEntryType()

}
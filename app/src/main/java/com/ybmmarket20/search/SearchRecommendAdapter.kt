package com.ybmmarket20.search

import android.graphics.Bitmap
import android.media.Image
import android.text.TextUtils
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.RecommendKeyWord
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.utils.*

class SearchRecommendAdapter : YBMBaseAdapter<RecommendKeyWord> {

    constructor(layoutResId: Int, data: MutableList<RecommendKeyWord>?) : super(layoutResId, data)


    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: RecommendKeyWord?) {

        val clRecommendWord = baseViewHolder.getView<ConstraintLayout>(R.id.cl_recommend_word)
        val ivRecommendWord = baseViewHolder.getView<ImageView>(R.id.iv_recommend_word)
        val tvRecommendWord = baseViewHolder.getView<TextView>(R.id.tv_recommend_word)
        val ivIcon = baseViewHolder.getView<ImageView>(R.id.iv_icon)
        val ivRight = baseViewHolder.getView<ImageView>(R.id.iv_right)
        if (!TextUtils.isEmpty(t?.picUrl)) {
            ImageUtil.load(mContext, t?.picUrl, ivRecommendWord)
            ivRecommendWord.visibility = View.VISIBLE
            clRecommendWord.visibility = View.GONE
        } else {
            ivRecommendWord.visibility = View.GONE
            clRecommendWord.visibility = View.VISIBLE
            var bgresId = -1
            val postion = baseViewHolder.layoutPosition % 4
            when (postion) {
                0 -> bgresId = R.drawable.bg_search_hot_01
                1 -> bgresId = R.drawable.bg_search_hot_02
                2 -> bgresId = R.drawable.bg_search_hot_03
                3 -> bgresId = R.drawable.bg_search_hot_04
            }
            bgresId?.takeIf { it > 0 }?.let {
                clRecommendWord.setBackgroundResource(it)
            }
            tvRecommendWord.text = t?.keyword ?: ""
            if (TextUtils.isEmpty(t?.icon)) {
                ivIcon?.visibility = View.GONE
            } else {
                ivIcon?.visibility = View.VISIBLE
                ImageUtil.load(mContext, t?.icon, ivIcon)
            }
            if (TextUtils.isEmpty(t?.androidUrl)) {
                ivRight?.visibility = View.GONE
            } else {
                ivRight?.visibility = View.VISIBLE
            }
        }
    }
}
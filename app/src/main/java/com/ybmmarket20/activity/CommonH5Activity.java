package com.ybmmarket20.activity;

import static android.view.View.GONE;
import static com.ybmmarket20.activity.jdpay.AddBankCardActivityKt.BIND_RESULT_FROM_PAYMENT;
import static com.ybmmarket20.bean.SelectFileBeanKt.ENTRY_TYPE_CAMAERA;
import static com.ybmmarket20.bean.SelectFileBeanKt.FILE_TYPE_MIX;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentValues;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ValueCallback;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.github.mzule.activityrouter.annotation.Router;
import com.github.mzule.activityrouter.router.Routers;
import com.google.gson.Gson;
import com.luck.picture.lib.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.tools.PictureFileUtils;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.tencent.smtt.export.external.interfaces.PermissionRequest;
import com.tencent.smtt.sdk.WebChromeClient;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.tencent.tddiag.logger.TDLog;
import com.xyy.app.rsa.Base64;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.NetUtil;
import com.ybm.app.utils.PermissionDialogUtil;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AdBagListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.SelectFileBean;
import com.ybmmarket20.bean.SelectFileBeanKt;
import com.ybmmarket20.bean.ShowH5PopBean;
import com.ybmmarket20.common.AdDialog2;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.AppUpdateManagerV2;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.ViewOnClickListener;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.Abase;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.search.SearchProductActivity;
import com.ybmmarket20.utils.BitmapUtil;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SettingProxy;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StatusBarUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.YBMPayUtil;
import com.ybmmarket20.utils.YBMWxUtil;
import com.ybmmarket20.utils.YbmCommand;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.BitmapView;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.view.CommonH5SharePopWindow;
import com.ybmmarket20.view.SharePopWindow;
import com.ybmmarket20.view.X5WebView;
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

import butterknife.Bind;

/**
 * 通用h5页面，可以使用原生标题栏
 */
@Router({"commonh5activity", "commonproductactivity", "commonproductactivity/:url", "commonh5activity/:url", "commonh5activity/:url/:ybm_title/:cache/:no_head/:head_menu"})
public class CommonH5Activity extends CommonH5AnalysisActivity {
    // 广发白条传递过来的url
    public static String ext_url = "ext_url_gfbt";
    public static String needDecode = "needDecode";
    //唤起系统图册的request
    private static final int CAMERA_CODE = 100;
    private static final int PICTURE_CODE = 200;
    private static final int VIDEO_RECORD_CODE = 300;
    private static final int AUDIO_RECORD_CODE = 400;
    public static final int H5_REQUESTCODE = 500;
    @Bind(R.id.fl_container)
    FrameLayout flContainer;
    @Bind(R.id.ll_root)
    LinearLayout llRoot;
    @Bind(R.id.btnReload)
    Button btnReload;
    X5WebView wbX5;

    private String url;
    private boolean isNeedDecode = true;        //   广发白条的url不需要解码
    private boolean pageTitle = true;
    private String cache;
    private String header;
    private String headerMenu;
    private String title;
    private String rawUrl;
    private String showSearchBtn;//是否显示搜索按钮1显示 0不显示
    private String tagList;//搜索参数  商品标签列表(多个标签用逗号分隔) 例：pg_yx商品组优选商品
    private String jgspid;
    //web上传文件用的回调
    private ValueCallback<Uri> mFileCallback16;
    private ValueCallback<Uri[]> mFileCallback21;
    //qq x5版本文件回掉
    private com.tencent.smtt.sdk.ValueCallback<Uri> mFileCallbackLow;
    private com.tencent.smtt.sdk.ValueCallback<Uri[]> mFileCallbackHei;
    private File cameraFile;
    private String PHOTO_DIR;
    private String photo;   // 0 让用户选择拍照或图片 1 相册 2 相机 21 前置相机 3视频 4音频
    private String htmlStr;
    //传入html字符串
    private String commonHtmlStr;
    private AlertDialogEx dialogEx;
    private int dismissType;
    private String searchAction = "ybmpage://searchproduct";
    private SharePopWindow mPopWindowShare;
    private String shareImg;
    Bitmap bitmap;

    //分享文案
    private String share_type;
    private String share_desc_pyq;
    private String share_desc;
    private String share_url;
    private String share_title;

    private String mVoicePath;             //用于存储录音的最终目录，即根目录 / 录音的文件夹 / 录音
    private String mVoiceName;             //保存的录音的名字
    private File mVoiceFile;               //录音文件
    private LinkedList<String> uploadUrlList;
    private List<String> uploadUrl;

    private static YBMPayUtil.PaySDKCallBack mPayCallBack;
    // 图片来源
    private String photoGetWay;
    // 后置摄像头或者图库
    private static String IMAGE_FROM_BOTH_CAMERA_AND_PICTURES = "image/type1";
    // 前置摄像头
    private static String IMAGE_FROM_CAMERA_BEFORE = "image/type2";

    private String bindResultFrom;


    private XyyIoUtil.XyyJS xyyJS;
    private AppUpdateManagerV2 updateManagerV2;
    public static void setPaySDKCallBack(YBMPayUtil.PaySDKCallBack payCall) {
        mPayCallBack = payCall;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            bindResultFrom = getIntent().getStringExtra("bindResultFrom");
        } catch (Exception e) {
            e.printStackTrace();
        }
        getView(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                trackClose();
                if (!TextUtils.isEmpty(htmlStr)) {
                    getView(R.id.iv_close).setOnClickListener(v1 -> {
                        if (TextUtils.isEmpty(bindResultFrom)) {
                            RoutersUtils.open("ybmpage://addbankcard?status=4");
                        } else {
                            if (TextUtils.equals(BIND_RESULT_FROM_PAYMENT, bindResultFrom)){
                                RoutersUtils.open("ybmpage://payment");
                            } else {
                                RoutersUtils.open("ybmpage://paywayactivity");
                            }
                        }
                    });
                }
                    onBackPressed();
            }
        });

        setLeft(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                trackBack();
                if (!TextUtils.isEmpty(htmlStr)) {
                    if (TextUtils.isEmpty(bindResultFrom)) {
                        RoutersUtils.open("ybmpage://addbankcard?status=4");
                    } else {
                        if (TextUtils.equals(BIND_RESULT_FROM_PAYMENT, bindResultFrom)){
                            RoutersUtils.open("ybmpage://payment");
                        } else {
                            RoutersUtils.open("ybmpage://paywayactivity");
                        }
                    }
                }
                onBackPressed();
            }
        });

        findViewById(R.id.iv_ad_suspension).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getCoupon();
            }
        });

        findViewById(R.id.iv_share).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                trackShare();
                if (!TextUtils.isEmpty(shareImg)) {
                    initShowShare();
                    mPopWindowShare.show(flContainer);
                }
            }
        });

        YBMWxUtil.getInstance().ybmCall(new YBMWxUtil.SDKCallBack() {
            @Override
            public void sdkCallBack(int code, String res_msg) {
                if (wbX5 != null) {
                    //UiUtils.toast(res_msg);
                    wbX5.callJs("window._self_fn.isShareSuccess", "");
                }
            }

            @Override
            public void sdkCallBackTwo() {
                isSharing = true;
            }
        });
        if (BuildConfig.DEBUG) {
            btnReload.setVisibility(View.VISIBLE);
        }
        btnReload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                wbX5.loadUrl(url);
            }
        });
        btnReload.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                btnReload.setVisibility(GONE);
                return true;
            }
        });
        if (!TextUtils.isEmpty(htmlStr)) {
            findViewById(R.id.iv_close).setVisibility(GONE);
        }
    }
    private void setCallH5Method(int type){
        // 调用H5方法获取数据
        callH5Method("window.clickOnTheTopOfTheApp()", new CommonH5Activity.JsCallback() {
            @Override
            public void onResult(String value) {
                if (TextUtils.isEmpty(value)) {
                    if (type==1){
                        finish();
                    }else {
                        if (wbX5 != null && !wbX5.back()) {
                           goBack();
                        }
                    }
                }else {
                    Gson gson = new Gson();
                    ShowH5PopBean bean = gson.fromJson(value, ShowH5PopBean.class);
                    if (bean != null&&bean.getAlertStatus().equals("1")){
                        showDialog(bean.getMsg());
                    }else {
                        if (type==1){
                            finish();
                        }else {
                            if (wbX5 != null && !wbX5.back()) {
                                goBack();
                            }
                        }
                    }
                }
            }
        });
    }
    private void showDialog(String msg){
    AlertDialogEx dialogEx = new AlertDialogEx(this);
    dialogEx.setMessage(msg)
            .setCancelButton("取消", (AlertDialogEx.OnClickListener) (dialog, button) -> {
    })
            .setConfirmButton("确定", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                if (wbX5 != null && !wbX5.back()) {
                   goBack();
                }
            })
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(null)
            .show();
}
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
                onBackPressed();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void initData() {
        super.initData();

        rawUrl = getIntent().getStringExtra(Routers.KEY_RAW_URL);
        title = getIntent().getStringExtra("ybm_title");
        cache = getIntent().getStringExtra("cache");
        header = getIntent().getStringExtra("no_head");
        headerMenu = getIntent().getStringExtra("head_menu");
        photo = getIntent().getStringExtra("photo");
        photo = getIntent().getStringExtra("photo");
        jgspid = getIntent().getStringExtra("jgspid");
        try {
            htmlStr = getIntent().getStringExtra("htmlStr");
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            commonHtmlStr = getIntent().getStringExtra("commonHtmlStr");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (rawUrl != null && (rawUrl.contains("http") || rawUrl.contains("commonproductactivity"))) {//没有base64,
            url = rawUrl.substring(rawUrl.indexOf("url=") + 4);
        } else {//base64 了或者是相对路径
            url = getIntent().getStringExtra("url");
            if (!TextUtils.isEmpty(url) && !url.startsWith("#!")) {
                try {
                    url = new String(Base64.decode(url));
                } catch (Throwable e) {

                }
            }
        }
        // 白条的跳转链接，一定要确定不影响其他跳转url，其他url都是null才获取
        if (rawUrl == null && url == null) {
            url = getIntent().getStringExtra(ext_url);
            isNeedDecode = getIntent().getBooleanExtra(needDecode, true);
        }
        if (url == null) {
            finish();
            ToastUtils.showShort("请求参数异常");
            return;
        }
        if (!url.startsWith("http")) {
            url = AppNetConfig.getStaticHost2Https() + url;
        }



        //上报打开的web页面PV/UV
        HashMap<String, String> param = new HashMap<>();
        String urlPath = url;
        int index = url.indexOf('?');
        if (index > 0) {
            urlPath = url.substring(0, index);
        }
        param.put("pageUrl", urlPath);
        XyyIoUtil.track(XyyIoUtil.WEB_PAGE_OPEN, param);

        //是否登录
        if (!isLogin()) {
            if (!TextUtils.isEmpty(url) && (url.contains("xyyvue/dist/#/service") || url.contains("ybmPrivacy"))) {

            } else {
                gotoAtivity(LoginActivity.class, null);
                return;
            }
        }
        try {
            if (isNeedDecode) {
                url = URLDecoder.decode(url);
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }

        if (TextUtils.isEmpty(cache)) {
            cache = RoutersUtils.getParameter(url, "cache");
        }
        if (TextUtils.isEmpty(header)) {
            header = RoutersUtils.getParameter(url, "no_head");
        }
        if (TextUtils.isEmpty(headerMenu)) {
            headerMenu = RoutersUtils.getParameter(url, "head_menu");
        }
        if (TextUtils.isEmpty(photo)) {
            photo = RoutersUtils.getParameter(url, "photo");
        }
        if (TextUtils.isEmpty(showSearchBtn)) {
            showSearchBtn = RoutersUtils.getParameter(url, "showSearchBtn");
        }
        if (TextUtils.isEmpty(tagList)) {
            tagList = RoutersUtils.getParameter(url, "tagList");
        }
        if (TextUtils.isEmpty(title)) {
            title = RoutersUtils.getParameter(url, "ybm_title");
        }
        if (TextUtils.isEmpty(jgspid)) {
            jgspid = RoutersUtils.getParameter(url, "jgspid");
        }
        if (!TextUtils.isEmpty(title)) {
            setTitle(title);
            pageTitle = false;
        }
        String isShowCart = RoutersUtils.getParameter(url, "isShowCart");
        if (TextUtils.isEmpty(isShowCart)) {
            isShowCart = "1";
        }

        findViewById(R.id.iv_cart).setVisibility(TextUtils.equals("1", isShowCart)? View.VISIBLE: View.GONE);
        findViewById(R.id.tv_num).setVisibility(TextUtils.equals("1", isShowCart)? View.VISIBLE: View.GONE);

        if (!TextUtils.isEmpty(showSearchBtn) && "1".equals(showSearchBtn)) {//显示搜索按钮
            getView(R.id.iv_search).setVisibility(View.VISIBLE);
        } else {
            getView(R.id.iv_search).setVisibility(View.GONE);
        }
        getView(R.id.iv_search).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(tagList) || "优选品种专区".equals(title)) {
                    RoutersUtils.open("ybmpage://searchproduct?tagList=" + tagList);
                    XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH_WEBACTIVITY);
                } else {
                    RoutersUtils.open(searchAction);
                }

            }
        });
        getView(R.id.cl_pingan_gold).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RoutersUtils.open("ybmpage://commonh5activity?url="+AppNetConfig.SHOPPING_GOLD_USER_INFO_FROM_MY);
            }
        });

        if (header != null && header.trim().equals("0")) {//不使用原生标题
            findViewById(R.id.ll_title).setVisibility(GONE);
        }
        if (TextUtils.isEmpty(headerMenu)) {
            if (!TextUtils.isEmpty(url) && (url.endsWith("htm") || url.endsWith("html"))) {
                headerMenu = "1";//默认样式 不显示
            } else {
                headerMenu = "1";//右边有购物车图标
            }
        }
        initWebSetting();
        setNestedScroll(false);//禁止webview自己滑动
        setHeaderMenu(headerMenu);
    }

    @Override
    public void setTitle(String title) {
        super.setTitle(title);
        if (title.equals("我的平安商户")){
            findViewById(R.id.cl_pingan_gold).setVisibility(View.VISIBLE);
            findViewById(R.id.iv_cart).setVisibility(View.GONE);
            findViewById(R.id.tv_num).setVisibility(View.GONE);
        } else{
            findViewById(R.id.cl_pingan_gold).setVisibility(GONE);
        }
    }

    private void initShowShare() {
        mPopWindowShare = new SharePopWindow(new SharePopWindow.DialogSingleClick() {
            @Override
            public void click(String content, ImageView view) {
                sharePosters(content, view);
            }
        }, this, shareImg);
    }

    private void sharePosters(String platform, ImageView sv) {
        if (!ShareUtil.isWeChatOrQQInstall(platform)) {
            return;
        }

        if ("wx".equals(platform)) {
            bitmap = BitmapView.viewSave2ToImage(sv);
            if (bitmap != null) {
                ShareUtil.shareWXWithImage(0, bitmap, UiUtils.getScreenWidth(), UiUtils.getScreenHeight());
            }
        } else if ("wxpyq".equals(platform)) {
            bitmap = BitmapView.viewSave2ToImage(sv);
            if (bitmap != null) {
                ShareUtil.shareWXWithImage(1, bitmap, UiUtils.getScreenWidth(), UiUtils.getScreenHeight());
            }
        } else if ("bctp".equals(platform)) {

            if (FileUtil.isOpenVerifyStoragePermissions(CommonH5Activity.this)) {

                SmartExecutorManager.getInstance().execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            BitmapView.viewSaveToImage(sv, CommonH5Activity.this);

                        } catch (Exception e) {
                            e.printStackTrace();
                            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                                @Override
                                public void run() {
                                    ToastUtils.showShort("图片保存出错");
                                }
                            });
                        }
                    }
                });
            }
        }

    }

    //获取首页天降礼包
    private void getCoupon() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOME_COUPON).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<AdBagListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<AdBagListBean> obj, AdBagListBean adDataBeans) {
                if (wbX5 == null) {
                    return;
                }

                if (obj != null && obj.isSuccess()) {

                    if (adDataBeans != null && adDataBeans.bagList != null && adDataBeans.bagList.size() > 0) {
                        AdDialog2.showDialog(adDataBeans);
                    }
                }
            }
        });
    }

    private void initWebSetting() {
        flContainer.removeAllViews();
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        wbX5 = new X5WebView(getMySelf(), jgspid);
        WebSettings settings = wbX5.getSettings();
        settings.setJavaScriptEnabled(true);
        // https://docs.zhugeio.com/dev/embedded.html
        // app内嵌H5的数据关联
        xyyJS = new XyyIoUtil.XyyJS();
        wbX5.addJavascriptInterface(xyyJS, "sdkTracker");
        flContainer.addView(wbX5, params);
//        XyyReportManager.attachX5WebView(wbX5);
        //设置QQ x5监听
        if (wbX5 != null) {
            wbX5.setCacheMode(cache);
            wbX5.setWebViewListener(new X5WebView.WebViewListener() {

                @Override
                public void onReceivedTitle(com.tencent.smtt.sdk.WebView webView, String s) {
                    if (pageTitle) {
                        setTitle(s);
                        CommonH5Activity.this.title = s;
                    }
                }

                @Override
                public void setStatusColor(com.tencent.smtt.sdk.WebView webView, int color) {
                    findViewById(R.id.ll_title).setBackgroundColor(color);
                    findViewById(R.id.ll_root).setBackgroundColor(color);
                }

                @Override
                public void onReceivedError(com.tencent.smtt.sdk.WebView view, int errorCode, String description, String failingUrl) {
                    showError(errorCode, failingUrl);
                }

                @Override
                public void onScrollChanged(int x, int y, int oldx, int oldy) {

                }

                @Override
                public void setToolBar(String type, String content) {
                    LogUtils.tag("xyd").d("type = " + type + "; content = " + content);
                    if ("liveShare".equalsIgnoreCase(type)) {
                        try {
                            setLiveShare(content);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    } else if ("showTitle".equalsIgnoreCase(type)) {
                        findViewById(R.id.ll_title).setVisibility("1".equals(content) ? View.VISIBLE : GONE);
                    }
                }

                @Override
                public boolean setRightMenu(String title, final String action) {
                    if (TextUtils.isEmpty(title)) {
                        setShowCart(false, title);
                        return false;
                    }
                    if ("1".equals(title)) {
                        setShowCart(true, title);
                    } else {
                        setShowCart(false, title);
                        setRigthText(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                RoutersUtils.open(action);
                            }
                        }, title);
                    }
                    return true;
                }

                @Override
                public boolean setTitleStyle(String startColor, String endColor) {
//                    StatusBarUtils.setDarkMode(CommonH5Activity.this);
                    int[] colors = {Color.parseColor(startColor), Color.parseColor(endColor)};
                    GradientDrawable gd = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, colors);
                    StatusBarUtils.setStatusColor(CommonH5Activity.this, gd);
                    getView(R.id.ll_title).setBackgroundDrawable(gd);
                    ((ImageView) getView(R.id.iv_cart)).setImageDrawable(getResources().getDrawable(R.drawable.icon_seckill_cart));
                    ((ImageView) getView(R.id.iv_close)).setImageDrawable(getResources().getDrawable(R.drawable.icon_seckill_close));
                    ((ImageView) getView(R.id.iv_back)).setImageDrawable(getResources().getDrawable(R.drawable.icon_seckill_left));
                    ((TextView) getView(R.id.tv_title)).setTextColor(getResources().getColor(R.color.white));
                    return true;
                }

                @Override
                public boolean setControlTitle(String type, String startColor, String endColor) {
//                    StatusBarUtils.setDarkMode(CommonH5Activity.this);
                    int[] colors = {Color.parseColor(startColor), Color.parseColor(endColor)};
                    GradientDrawable gd = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, colors);
                    StatusBarUtils.setStatusColor(CommonH5Activity.this, gd);
                    getView(R.id.ll_title).setBackground(gd);
                    getView(R.id.ll_search).setBackgroundColor(getColor(startColor));

                    getView(R.id.iv_close).setVisibility(View.GONE);
                    ((ImageView) getView(R.id.iv_cart)).setImageDrawable(getResources().getDrawable(R.drawable.icon_seckill_cart));
                    ((ImageView) getView(R.id.iv_back)).setImageDrawable(getResources().getDrawable(R.drawable.icon_seckill_left));
                    ((TextView) getView(R.id.tv_title)).setTextColor(getResources().getColor(R.color.white));

                    getView(R.id.iv_voice).setOnClickListener(v -> RoutersUtils.open("ybmpage://searchvoiceactivity"));
                    getView(R.id.iv_voice_two).setOnClickListener(v -> RoutersUtils.open("ybmpage://searchvoiceactivity"));
                    getView(R.id.ll_search).setOnClickListener(v -> {
                        gotoAtivity(SearchProductActivity.class, null);
                        XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS_SEARCH);
                    });
                    getView(R.id.ll_search_two).setOnClickListener(v -> {
                        gotoAtivity(SearchProductActivity.class, null);
                        XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS_SEARCH);
                    });
                    getView(R.id.tv_share).setOnClickListener(v -> DialogUtil.shareTheInvitationDialog(CommonH5Activity.this, false, platform -> {
                        if (TextUtils.isEmpty(platform)) {
                            return;
                        }
                        if (!ShareUtil.isWeChatOrQQInstall(platform)) {
                            return;
                        }
                        if (TextUtils.isEmpty(CommonH5Activity.this.share_url)) {
                            return;
                        }
                        //type：1是双十二分享，2是控销店铺分享
                        int icon = R.drawable.logo;
                        if ("1".equals(CommonH5Activity.this.share_type)) {
                            icon = R.drawable.icon_logo_double_twelve;
                        } else if ("2".equals(CommonH5Activity.this.share_type)) {
//                            icon = R.drawable.icon_logo_control_pin;
                            icon = R.drawable.icon_huomaimai_share;
                        }
                        if ("wx".equals(platform)) {
                            Bitmap bmp = BitmapFactory.decodeResource(getResources(), icon);
                            ShareUtil.shareWXPage(0, CommonH5Activity.this.share_title, CommonH5Activity.this.share_url, CommonH5Activity.this.share_desc, bmp);
                        } else if ("wxpyq".equals(platform)) {
                            Bitmap bmp = BitmapFactory.decodeResource(getResources(), icon);
                            ShareUtil.shareWXPage(1, CommonH5Activity.this.share_desc_pyq, CommonH5Activity.this.share_url, CommonH5Activity.this.share_desc_pyq, bmp);
                        }
                    }));
                    if ("1".equals(type)) {
                        getView(R.id.tv_title).setVisibility(View.GONE);
                        getView(R.id.ll_control_pin_mall).setVisibility(View.VISIBLE);
                        getView(R.id.ll_search).setVisibility(View.VISIBLE);
                        getView(R.id.ll_search_two).setVisibility(GONE);
                    } else if ("2".equals(type)) {
                        getView(R.id.tv_title).setVisibility(View.VISIBLE);
                        getView(R.id.ll_control_pin_mall).setVisibility(View.GONE);
                        getView(R.id.ll_search).setVisibility(GONE);
                        getView(R.id.ll_search_two).setVisibility(View.VISIBLE);
                    }
                    return true;
                }

                @Override
                public boolean setShowAdPop(boolean isShow) {

                    if (SpUtil.readInt("show_ad_collect_pop", 0) == 1) {
                        findViewById(R.id.iv_ad_suspension).setVisibility(View.GONE);
                        return false;
                    }
                    findViewById(R.id.iv_ad_suspension).setVisibility(View.VISIBLE);
                    return true;
                }

                @Override
                public boolean setShareMenu(String title, String action) {
                    return true;
                }

                @Override
                public boolean controlMallShare(String title, String url, String desc, String desc_pyq, String type) {
                    CommonH5Activity.this.share_title = title;
                    CommonH5Activity.this.share_url = url;
                    CommonH5Activity.this.share_desc = desc;
                    CommonH5Activity.this.share_desc_pyq = desc_pyq;
                    CommonH5Activity.this.share_type = type;
                    return true;
                }

                @Override
                public void getImageVideo(SelectFileBean selectFileBean) {
                    SmartExecutorManager.getInstance().executeUI(() -> {
                        RxPermissions rxPermissions = new RxPermissions(CommonH5Activity.this);
                        if (rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE)
                                && rxPermissions.isGranted(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                                && rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
                            requestPermission(selectFileBean);
                        } else {
                            PermissionDialogUtil.showPermissionInfoDialog(CommonH5Activity.this,
                                    "药帮忙App需要申请存储权限和相机权限，用于拍照并存储照片",
                                    () -> requestPermission(selectFileBean));
                        }

                    });
                }

                @Override
                public void closeWebView() {
                    setCallH5Method(1);
                }

                @Override
                public void goBack() {
                    wbX5.back();
                }

                @Override
                public void setNavigationBar(String bgColor, String fontColor) {
                    wbX5.postDelayed(() -> {
                        try {
                            if (TextUtils.isEmpty(bgColor) || TextUtils.isEmpty(fontColor)) return;
                            findViewById(R.id.ll_title).setBackgroundColor(Color.parseColor(bgColor));
                            TextView title = findViewById(R.id.tv_title);
                            title.setTextColor(Color.parseColor(fontColor));
                            ImageView ivCart = findViewById(R.id.iv_cart);
                            ImageView ivShare = findViewById(R.id.iv_share);
                            ImageView ivBack = findViewById(R.id.iv_back);
                            ImageView ivClose = findViewById(R.id.iv_close);
                            if (TextUtils.equals(fontColor, "#ffffff")) {
                                ivCart.setImageResource(R.drawable.icon_seckill_cart);
                                ivShare.setImageResource(R.drawable.icon_share_title_h5);
                                ivBack.setImageResource(R.drawable.icon_navigateion_back_white);
                                ivClose.setImageResource(R.drawable.close);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }, 200);
                }

                @Override
                public void downLoadFile(String url) {
                    // 提取文件后缀并转换为小写
                    String extension = url.substring(url.lastIndexOf(".") + 1).toLowerCase();

                    // 判断文件类型
                    boolean isVideo = extension.matches("mp4|webm|ogg");
                    boolean isPdf = extension.matches("pdf");
                    boolean isImage = extension.matches("jpeg|jpg|png|gif|tiff|svg|psd|webp|heif");
                   if (isVideo||isImage){
                       if (rxPermissions.isGranted(Manifest.permission.READ_EXTERNAL_STORAGE)) {
                           requestDownLoadPermission(url, extension, isVideo);
                       } else {
                           PermissionDialogUtil.showPermissionInfoDialog(CommonH5Activity.this, "药帮忙App需要申请存储权限，用于存储文件/图片", () ->requestDownLoadPermission(url, extension, isVideo));
                       }
                   }else {
                       openUrlInBrowser(url);
                   }
                }
            });

            wbX5.setFileChooserListener(new X5WebView.FileChooserListener() {

                @Override
                public void onFileChooserApiLow(com.tencent.smtt.sdk.ValueCallback<Uri> uploadFile, String acceptType, String capture) {
                    mFileCallbackLow = uploadFile;

                    if (!isNeedDecode) {
                        // 白条的单独处理
                        if ("image/*".equals(acceptType)) {
                            photo = "2";
                        } else if ("video/*".equals(acceptType)) {
                            photo = "3";
                        } else if ("audio/*".equals(acceptType)) {
                            photo = "4";
                        }
                    }
                    if ("*/*".equals(acceptType)) {
                        photo = "5";
                    }
                    openMediaTool();
                }

                @Override
                public void onFileChooserApiHei(com.tencent.smtt.sdk.ValueCallback<Uri[]> uploadFile, WebChromeClient.FileChooserParams fileChooserParams) {
                    mFileCallbackHei = uploadFile;
                    if (!isNeedDecode) {
                        // 白条的单独处理
                        if ("image/*".equals(fileChooserParams.getAcceptTypes()[0])) {
                            try {
                                photoGetWay = fileChooserParams.getAcceptTypes()[1];
                            } catch (Exception e) {
                                BugUtil.sendBug(new Throwable("广发获取照片方式没有传"));
                            }
                            if (IMAGE_FROM_BOTH_CAMERA_AND_PICTURES.equals(photoGetWay)) {
                                photo = "0";
                            } else if (IMAGE_FROM_CAMERA_BEFORE.equals(photoGetWay)) {
                                photo = "21";
                            }
                        } else if ("video/*".equals(fileChooserParams.getAcceptTypes()[0])) {
                            photo = "3";
                        } else if ("audio/*".equals(fileChooserParams.getAcceptTypes()[0])) {
                            photo = "4";
                        }
                    }
                    if ("*/*".equals(fileChooserParams.getAcceptTypes()[0])) {
                        photo = "5";
                    }
                    openMediaTool();
                }

                @Override
                public void onRequestPermission(PermissionRequest permissionRequest) {
                    if (rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
                        requestFrontCameraPermission1(permissionRequest);
                    } else {
                        PermissionDialogUtil.showPermissionInfoDialog(CommonH5Activity.this,
                                "药帮忙App需要申请相机权限，用于拍摄照片", () ->
                                        requestFrontCameraPermission1(permissionRequest));
                    }
                }
            });

            // 设置广发白条的回调
            wbX5.setGFBTCallBack(url -> {
                if (url != null && url.contains("http://ybm100.com.gfbtpay")) {
                    Uri parse = Uri.parse(url);
                    String resultCode = parse.getQueryParameter("resultCode");
                    if ("success".equalsIgnoreCase(resultCode)) {
                        if (mPayCallBack != null) {
                            mPayCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_SUCCESS, getStr(R.string.payway_result_succ), "");
                        }
                    } else if ("failed".equalsIgnoreCase(resultCode)) {
                        if (mPayCallBack != null) {
                            mPayCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_FAIL_SDK, getStr(R.string.payway_result_error_sdk), "");
                        }

                    } else if ("cancelled".equalsIgnoreCase(resultCode)) {
                        if (mPayCallBack != null) {
                            mPayCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_USER_CANCEL, getStr(R.string.payway_result_cancel), "");
                        }
                    }
                    finish();
                }
            });

            //京东支付监听
            wbX5.setBindBankCardCallback((status, msg) -> {
                if (status == 2 || status == 4) {
                    //2成功 或 4失败
//                    RoutersUtils.open("ybmpage://bankcard");
                    ToastUtils.showShort(msg);
                    if (TextUtils.isEmpty(bindResultFrom)) {
                        RoutersUtils.open("ybmpage://addbankcard?status=" + status);
                    } else {
                        if (TextUtils.equals(BIND_RESULT_FROM_PAYMENT, bindResultFrom)){
                            RoutersUtils.open("ybmpage://payment?status=" + status);
                        } else {
                            RoutersUtils.open("ybmpage://paywayactivity");
                        }
                    }
                }
            });

//            if (BaseYBMApp.getApp().isDebug()) {//测试版本不缓存
//                wbX5.setCacheMode("0");
//            }
            if (BuildConfig.DEBUG) {
                wbX5.setWebContentsDebuggingEnabled(true);
            }

            if (!TextUtils.isEmpty(url)) {
                if (url.contains("?")) url += "&terminal=1";
                else url += "?terminal=1";
            }
            if (!TextUtils.isEmpty(htmlStr)) {
                try {
                    wbX5.loadData(htmlStr, "text/html", "utf-8");
                } catch (Exception e) {
                    e.printStackTrace();
                }

            } else if(!TextUtils.isEmpty(commonHtmlStr)) {
                try {
                    setTitle("");
                    wbX5.loadData(commonHtmlStr, "text/html", "utf-8");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                wbX5.loadUrl(url);
            }
        }
    }
    // Java 示例
    @SuppressLint("UnsafeImplicitIntentLaunch")
    public void openUrlInBrowser(String url) {
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.setData(Uri.parse(url));
        startActivity(intent);

    }
    private void setLiveShare(String content) throws JSONException {

        if (TextUtils.isEmpty(content)) {
            return;
        }
        JSONObject liveShareObject = new JSONObject(content);
        /*{
            "shareTitle" : "直播间名称",
                "shareDesc" : "直播说明",
                "shareUrl" : "落地页地址",
                "shareImageUrl" : "分享图标",
        }*/
        String shareTitle = liveShareObject.optString("shareTitle");
        String shareDesc = liveShareObject.optString("shareDesc");
        String shareUrl = liveShareObject.optString("shareUrl");
        String shareImageUrl = liveShareObject.optString("shareImageUrl");


        CommonH5SharePopWindow popWindow = new CommonH5SharePopWindow(platform -> {

            if ("wx".equals(platform)) {
                SmartExecutorManager.getInstance().execute(() -> {
                    try {
                        Bitmap bitmap = ImageHelper.with(CommonH5Activity.this).load(shareImageUrl).asBitmap().placeholder(R.color.white).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(100, 100).get();
                        ShareUtil.shareWXPage(0, shareTitle, shareUrl, shareDesc, bitmap);
                    } catch (Exception e) {
                        e.printStackTrace();
                        Bitmap bmp = BitmapFactory.decodeResource(Abase.getResources(), R.drawable.logo);
                        ShareUtil.shareWXPage(0, shareTitle, shareUrl, shareDesc, bmp);
                    }
                });

            } else if ("wxpyq".equals(platform)) {
                SmartExecutorManager.getInstance().execute(() -> {
                    try {
                        Bitmap bitmap = ImageHelper.with(CommonH5Activity.this).load(shareImageUrl).asBitmap().placeholder(R.color.white).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(100, 100).get();
                        ShareUtil.shareWXPage(1, shareTitle, shareUrl, shareDesc, bitmap);
                    } catch (Exception e) {
                        e.printStackTrace();
                        Bitmap bmp = BitmapFactory.decodeResource(Abase.getResources(), R.drawable.logo);
                        ShareUtil.shareWXPage(1, shareTitle, shareUrl, shareDesc, bmp);
                    }
                });
            } else if ("linkurl".equals(platform)) {
                YbmCommand.setClipboardMsg(shareUrl);
                ToastUtils.showShort("复制成功");
            }
        });

        getView(R.id.iv_share).setVisibility(View.VISIBLE);
        ImageView shareImageView = getView(R.id.iv_share);
        shareImageView.setImageResource(R.drawable.icon_h5_share);
        getView(R.id.iv_share).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                trackShare();
                popWindow.show(llRoot);
                String shareName = liveShareObject.optString("pageName");
                String pageId = liveShareObject.optString("pageId");
                String url = liveShareObject.optString("url");
//
                HashMap<String, String> params = new HashMap<>();
                params.put("pageName", shareName);
                params.put("pageId", pageId);
                params.put("url", url);

                XyyIoUtil.track("h5_action_activity_share", params);
                JGTrackTopLevelKt.jgTrackCommonBtnClick(v.getContext(), "分享", url);
            }
        });

    }

    @SuppressLint("CheckResult")
    private void requestPermission(SelectFileBean selectFileBean) {
        RxPermissions rxPermissions = new RxPermissions(this); // where this is an Activity instance
        rxPermissions.request(
                android.Manifest.permission.READ_EXTERNAL_STORAGE,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
                android.Manifest.permission.CAMERA
        ).subscribe(granted -> {
            if (granted) { // 在android 6.0之前会默认返回true
                selectImageVideo(selectFileBean);
            } else {
                // 未获取权限
                Toast.makeText(getMySelf(), "您没有授权该权限，请在设置中打开授权", Toast.LENGTH_LONG).show();
            }
        }, throwable -> {
        });
    }

    private void selectImageVideo(SelectFileBean selectFileBean) {
        if (selectFileBean == null) return;
        if (selectFileBean.getEntry() == SelectFileBeanKt.ENTRY_TYPE_CAMAERA) {
            selectFromCamera(selectFileBean);
        } else {
            selectFromAlbum(selectFileBean);
        }
    }

    /**
     * 打开照相机
     *
     * @param selectFileBean
     */
    private void selectFromCamera(SelectFileBean selectFileBean) {
        if (selectFileBean == null) return;
        int mimeType = selectFileBean.getFileType();
        if (mimeType == SelectFileBeanKt.FILE_TYPE_MIX) {
            mimeType = PictureMimeType.ofAll();
        } else if (mimeType == SelectFileBeanKt.FILE_TYPE_VIDEO) {
            mimeType = PictureMimeType.ofVideo();
        } else if (mimeType == SelectFileBeanKt.FILE_TYPE_IMAGE) {
            mimeType = PictureMimeType.ofImage();
        }
        PictureSelector.create(this).openCamera(mimeType).forResult(PictureConfig.CHOOSE_REQUEST);
    }

    /**
     * 从相册打开
     *
     * @param selectFileBean
     */
    private void selectFromAlbum(SelectFileBean selectFileBean) {
        if (selectFileBean == null) return;
        int mimeType = selectFileBean.getFileType();
        if (mimeType == SelectFileBeanKt.FILE_TYPE_MIX) {
            mimeType = PictureMimeType.ofAll();
        } else if (mimeType == SelectFileBeanKt.FILE_TYPE_VIDEO) {
            mimeType = PictureMimeType.ofVideo();
        } else if (mimeType == SelectFileBeanKt.FILE_TYPE_IMAGE) {
            mimeType = PictureMimeType.ofImage();
        }
        PictureSelector.create(getMySelf())
                .openGallery(mimeType)
                .maxSelectNum(selectFileBean.getCount())
                .minSelectNum(1)
                .imageSpanCount(4)
                .setSelectVideoMaxSize(20)
                .compress(true)
                .isCamera(true)
                .setVideoType("video/mp4")
                .canSelectedAudio(false)
                .selectionMode(PictureConfig.MULTIPLE)
                .forResult(PictureConfig.CHOOSE_REQUEST);
    }

    protected int getColor(String color) {
        try {
            return Color.parseColor(color);
        } catch (Exception e) {
            return Color.parseColor("#ffffff");
        }
    }

    private String getStr(int id) {
        return YBMAppLike.getAppContext().getString(id);
    }

    //打开相机或图库
    private void openMediaTool() {//选择图片的来源 0 没有 用户选择  1 相册 2拍照 3录像 4录音
        if (TextUtils.isEmpty(photo)) {
            photo = "0";
        }
        RxPermissions rxPermissions = new RxPermissions(this);
        switch (photo) {
            case "1"://1 相册
                if (rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE)) {
                    requestPhotoPermission();
                } else {
                    PermissionDialogUtil.showPermissionInfoDialog(this,
                            "药帮忙App需要申请存储权限，用于获取相册照片",
                            () -> requestPhotoPermission());
                }
                break;
            case "2":// 2相机，后置
                //调用系统相机程序
                if (rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
                    requestCameraPermission();
                } else {
                    PermissionDialogUtil.showPermissionInfoDialog(this,
                            "药帮忙App需要申请相机权限，用于拍摄照片",
                            () -> requestCameraPermission());
                }


                break;
            case "21":// 2相机，前置
                //调用系统相机程序
                if (rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
                    requestFrontCameraPermission();
                } else {
                    PermissionDialogUtil.showPermissionInfoDialog(this,
                            "药帮忙App需要申请相机权限，用于拍摄照片",
                            () -> requestFrontCameraPermission());
                }


                break;
            case "3":
                //录像功能，和普通标签选择功能
                if (rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
                    requestVideoPermission();
                } else {
                    PermissionDialogUtil.showPermissionInfoDialog(this,
                            "药帮忙App需要申请相机权限，用于录像",
                            () -> requestVideoPermission());
                }

                break;
            case "4":
                //录音功能，和普通标签选择功能
                if (rxPermissions.isGranted(android.Manifest.permission.RECORD_AUDIO)) {
                    requestRecordAudioPermission();
                } else {
                    PermissionDialogUtil.showPermissionInfoDialog(this, "药帮忙App需要申请录音权限，用于录音", () -> requestRecordAudioPermission());
                }
                break;
            case "5":
                if (rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE) ) {
                    requestCameraWithVideoPermission();
                } else {
                    PermissionDialogUtil.showPermissionInfoDialog(CommonH5Activity.this, "药帮忙App需要申请存储权限，用于获取相册照片", () -> {
                        requestCameraWithVideoPermission();
                    });
                }
                break;
            case "0":
            default:
                if (dialogEx == null) {
                    dismissType = 0;
                    dialogEx = new AlertDialogEx(this);
                    dialogEx.setAutoDismiss(false);
                    dialogEx.setTitle("图片上传").setMessage("从相机还是相册选择图片?").setCancelButton("相机", new ViewOnClickListener() {
                        @Override
                        public void onClick(AlertDialogEx dialog, int button) {
                            //调用系统相机程序
                            if (rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
                                requestCameraPermissionV2(dialog);
                            } else {
                                PermissionDialogUtil.showPermissionInfoDialog(CommonH5Activity.this,
                                        "药帮忙App需要申请相机权限，用于拍照照片",
                                        () -> requestCameraPermissionV2(dialog));
                            }

                        }
                    }).setConfirmButton("相册", new ViewOnClickListener() {
                        @Override
                        public void onClick(AlertDialogEx dialog, int button) {
                            if (rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE)) {
                                requestPhotoPermissionV2(dialog);
                            } else {
                                PermissionDialogUtil.showPermissionInfoDialog(CommonH5Activity.this,
                                        "药帮忙App需要申请存储权限，用于获取相册照片",
                                        () -> requestPhotoPermissionV2(dialog));
                            }

                        }
                    });
                    dialogEx.setDismissListener(new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            if (dismissType != 1) {
                                setImgResutl(null);
                            }
                            dismissType = 0;
                        }
                    });
                }
                dismissType = 0;
                dialogEx.show();
                break;
        }

    }

    private void requestPhotoPermissionV2(AlertDialogEx dialog) {
        requestEachPermissions(new PermissionCallBack("访问相册需要申请存储权限") {
            @Override
            public void granted(Permission permission) {
                Intent picture = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                picture.setType("image/*");
                startActivityForResult(picture, PICTURE_CODE);
                dialog.dismiss();
                dismissType = 1;
            }
        }, Manifest.permission.READ_EXTERNAL_STORAGE);
    }

    private void requestCameraPermissionV2(AlertDialogEx dialog) {
        requestEachPermissions(new PermissionCallBack("拍照需要申请相机权限") {
            @Override
            public void granted(Permission permission) {
                Intent camera = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                cameraFile = getPhotoFile();
                camera.putExtra(MediaStore.EXTRA_OUTPUT, FileUtil.getFileUri(cameraFile));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    camera.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                startActivityForResult(camera, CAMERA_CODE);

                dialog.dismiss();
                dismissType = 1;
            }
        }, Manifest.permission.CAMERA);
    }

    private void requestCameraWithVideoPermission() {
        requestEachPermissions(new PermissionCallBack("拍照需要申请相机权限") {
            @Override
            public void granted(Permission permission) {
                Intent picture = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                picture.setType("*/*"); // 允许所有文件类型
                picture.putExtra(Intent.EXTRA_MIME_TYPES, new String[]{"image/*", "video/*"}); // 明确指定图片和视频
                startActivityForResult(picture, PICTURE_CODE);
            }
        },Manifest.permission.READ_EXTERNAL_STORAGE);
    }

    private void requestRecordAudioPermission() {
        requestEachPermissions(new PermissionCallBack("录像需要申请录音权限", false) {
            @Override
            public void denied(Permission permission) {
                super.denied(permission);
                clearFileCallBack();
            }

            @Override
            public void showRequestPermissionRationale(Permission permission) {
                super.showRequestPermissionRationale(permission);
                clearFileCallBack();
            }

            @Override
            public void granted(Permission permission) {
                startRecord();
            }
        }, Manifest.permission.RECORD_AUDIO);
    }

    private void requestVideoPermission() {
        requestEachPermissions(new PermissionCallBack("录像需要申请相机权限", false) {
            @Override
            public void denied(Permission permission) {
                super.denied(permission);
                clearFileCallBack();
            }

            @Override
            public void showRequestPermissionRationale(Permission permission) {
                super.showRequestPermissionRationale(permission);
                clearFileCallBack();
            }

            @Override
            public void granted(Permission permission) {
                Intent cameraIntent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
                cameraIntent.putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 0);      // MediaStore.EXTRA_VIDEO_QUALITY 表示录制视频的质量，从 0-1，越大表示质量越好，同时视频也越大
                cameraIntent.putExtra(MediaStore.EXTRA_DURATION_LIMIT, 30);    // 设置视频录制的最长时间
                cameraIntent.putExtra("camerasensortype", 2);           // 调用前置摄像头
                cameraFile = getVideoFile();
                cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, FileUtil.getFileUri(cameraFile));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    cameraIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                startActivityForResult(cameraIntent, VIDEO_RECORD_CODE);
            }
        }, Manifest.permission.CAMERA);
    }

    private void requestFrontCameraPermission() {
        requestEachPermissions(new PermissionCallBack("拍照需要申请相机权限") {
            @Override
            public void granted(Permission permission) {
                Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                cameraIntent.putExtra("android.intent.extras.CAMERA_FACING", 1);     // 调用前置摄像头
                cameraFile = getPhotoFile();
                cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, FileUtil.getFileUri(cameraFile));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    cameraIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                startActivityForResult(cameraIntent, CAMERA_CODE);
            }
        }, Manifest.permission.CAMERA);
    }

    private void requestFrontCameraPermission1(PermissionRequest request) {
        requestEachPermissions(new PermissionCallBack("拍照需要申请相机权限") {
            @Override
            public void granted(Permission permission) {
                request.grant(request.getResources());
            }
        }, Manifest.permission.CAMERA);
    }
    private void requestDownLoadPermission(String url, String extension, boolean isVideo) {
        requestEachPermissions(new PermissionCallBack("访问相册需要申请存储权限") {
            @Override
            public void granted(Permission permission) {
                showProgress();
                downloadFile(url, extension, isVideo);
            }
        }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    private void requestCameraPermission() {
        requestEachPermissions(new PermissionCallBack("拍照需要申请相机权限") {
            @Override
            public void granted(Permission permission) {
                Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                cameraFile = getPhotoFile();
                cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, FileUtil.getFileUri(cameraFile));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    cameraIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                startActivityForResult(cameraIntent, CAMERA_CODE);
            }
        }, Manifest.permission.CAMERA);
    }

    private void requestPhotoPermission() {
        requestEachPermissions(new PermissionCallBack("访问相册需要申请存储权限") {
            @Override
            public void granted(Permission permission) {
                Intent pictureIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                pictureIntent.setType("image/*");
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    pictureIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                startActivityForResult(pictureIntent, PICTURE_CODE);
            }
        }, Manifest.permission.READ_EXTERNAL_STORAGE);
    }

    // 启动录音
    private void startRecord() {
        Intent intent = new Intent();
        intent.setAction(MediaStore.Audio.Media.RECORD_SOUND_ACTION);
        createVoiceFile();
        Log.d("xyd", "创建录音文件");
        //添加权限
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        Log.d("xyd", "启动系统录音机，开始录音...");
        startActivityForResult(intent, AUDIO_RECORD_CODE);
    }

    /**
     * 创建音频目录
     */
    private void createVoiceFile() {
        mVoiceName = System.currentTimeMillis() + ".amr";
        Log.d("xyd", "录音文件名称：" + mVoiceName);
        mVoiceFile = getAudioFile();
        mVoicePath = mVoiceFile.getAbsolutePath();
        Log.d("xyd", "按设置的目录层级创建音频文件，路径：" + mVoicePath);
        mVoiceFile.setWritable(true);
    }

    private void clearFileCallBack() {
        if (mFileCallbackLow != null) {
            mFileCallbackLow.onReceiveValue(null);
            mFileCallbackLow = null;
        } else if (mFileCallbackHei != null) {
            mFileCallbackHei.onReceiveValue(null);
            mFileCallbackHei = null;
        }
    }

    // 获取录音目录
    private File getAudioFile() {
        if (TextUtils.isEmpty(PHOTO_DIR)) {
            createImgFolders();
        }
        File file = new File(PHOTO_DIR, mVoiceName);
        if (file.exists()) {
            try {
                file.delete();
                file.createNewFile();
            } catch (Exception e) {
                LogUtils.d(e);
            }
        }
        return file;
    }

    //生成相机来的图片
    public File getPhotoFile() {
        if (TextUtils.isEmpty(PHOTO_DIR)) {
            createImgFolders();
        }
        File file = new File(PHOTO_DIR, "xyy.jpg");
        if (file.exists()) {
            try {
                file.delete();
                file.createNewFile();
            } catch (Exception e) {
                LogUtils.d(e);
            }
        }
        return file;
    }

    //生成相机来的录像
    public File getVideoFile() {
        if (TextUtils.isEmpty(PHOTO_DIR)) {
            createImgFolders();
        }
        File file = new File(PHOTO_DIR, "xyy_bt_record.mp4");
        if (file.exists()) {
            try {
                file.delete();
                file.createNewFile();
            } catch (Exception e) {
                LogUtils.d(e);
            }
        }
        return file;
    }

    private void createImgFolders() {
        PHOTO_DIR = getExternalCacheDir().getAbsolutePath();
        File photoDir = new File(PHOTO_DIR);
        if (!photoDir.exists()) {
            photoDir.mkdirs();
            photoDir = null;
        }
    }

    private void setHeaderMenu(String headerMenu) {
        if (TextUtils.isEmpty(headerMenu)) {
            headerMenu = "0";
        }
        switch (headerMenu) {
            case "0":
                setShowCart(false);
                break;
            case "1":
                setShowCart(true);
                break;
            default:
                setShowCart(false);
                break;
        }
    }

    private void setNestedScroll(boolean nestedScroll) {
        if (wbX5 != null) {
            wbX5.setNestedScroll(nestedScroll);
        }
    }

    private void showError(int errorCode, final String errorUrl) {
        findViewById(R.id.rl_network_tip).setVisibility(View.VISIBLE);
        findViewById(R.id.tv_fresh).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NetUtil.getNetworkState(getMySelf()) == NetUtil.NETWORN_NONE) {
                    DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.noNet, "网络无法连接");
                    return;
                }
                flContainer.setVisibility(View.VISIBLE);
                findViewById(R.id.tv_error).setVisibility(GONE);
                if (wbX5 != null) {
                    wbX5.reload();
                }
            }
        });
        flContainer.setVisibility(GONE);
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_common_h5;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Uri result = data == null || resultCode != RESULT_OK ? null : data.getData();
        Uri fileUri = null;
        if (requestCode == PICTURE_CODE && result != null) {
//            String path = GalleryUtil.getFilePathByUri(this, result);
//            if (!TextUtils.isEmpty(path)) {
                fileUri = result;
//            }
        } else if (requestCode == CAMERA_CODE && resultCode == Activity.RESULT_OK) {
            try {
                String path = getExternalCacheDir().getAbsolutePath() + "/" + System.currentTimeMillis() + "_tx.png";
                //来自于相机返回的结果
                if (cameraFile != null && cameraFile.exists()) {
                    if (BitmapUtil.compressFile(cameraFile.getAbsolutePath(), path)) {
                        cameraFile = new File(path);
                    }
                    LogUtils.d(cameraFile.getAbsolutePath());
                    fileUri = Uri.fromFile(cameraFile);
                }
            } catch (Exception e) {
                BugUtil.sendBug(new Throwable("getExternalCacheDir().getAbsolutePath()==null"));
            }
        } else if (requestCode == VIDEO_RECORD_CODE && resultCode == Activity.RESULT_OK) {
            fileUri = result;

        } else if (requestCode == AUDIO_RECORD_CODE && resultCode == Activity.RESULT_OK) {
            fileUri = result;
        } else if (resultCode == RESULT_OK && requestCode == PictureConfig.CHOOSE_REQUEST) {
            // 选择图片或视频
            List<LocalMedia> images = PictureSelector.obtainMultipleResult(data);
            if (images == null || images.isEmpty()) {
                ToastUtils.showShort("未找到图片");
                return;
            }
            if (uploadUrlList == null) {
                uploadUrlList = new LinkedList<>();
            }
            for (LocalMedia localMedia : images) {
                if (TextUtils.isEmpty(localMedia.getCompressPath())) {
                    uploadUrlList.add(localMedia.getPath());
                } else {
                    uploadUrlList.add(localMedia.getCompressPath());
                }
            }
            showProgress(false);
            uploadCircle(uploadUrlList);
        } else {
            //权限回来的,请求code不同，data也为空
            if (requestCode == CAMERA_CODE || requestCode == PICTURE_CODE || requestCode == VIDEO_RECORD_CODE || requestCode == AUDIO_RECORD_CODE) {
                if (mFileCallbackHei != null) {
                    mFileCallbackHei.onReceiveValue(null);//选择取消
                }
            }
        }

        if (resultCode != RESULT_OK || requestCode != PictureConfig.CHOOSE_REQUEST) {
            if (fileUri != null) {
                setImgResutl(fileUri);
            }
        }

        if (requestCode == H5_REQUESTCODE && resultCode == PaywayForDrugSchollActivity.PAY_RESULT_SUCESS_CODE) {
            // 药学院支付成功回调
            wbX5.callJs("window._self_fn.successCallBack", "");
        } else if (requestCode == H5_REQUESTCODE && resultCode == PaywayForDrugSchollActivity.PAY_RESULT_FAILURE_CODE) {
            // 药学院支付失败回调
            wbX5.callJs("window._self_fn.errorCallBack", "");
        }

    }

    private void setImgResutl(Uri fileUri) {
        //使用QQ X5内核上传文件的回掉
        if (wbX5 != null) {
            if (mFileCallbackLow != null) {
                mFileCallbackLow.onReceiveValue(fileUri);
                mFileCallbackLow = null;
            } else if (mFileCallbackHei != null) {
                if (fileUri != null) {
                    mFileCallbackHei.onReceiveValue(new Uri[]{fileUri});
                } else {
                    mFileCallbackHei.onReceiveValue(null);
                }
                mFileCallbackHei = null;
            }
        }
    }

    @Override
    protected void onPause() {
        if (!BaseYBMApp.getApp().isDebug()) {
            SettingProxy.setWebViewProxyBg(true);
        }
        super.onPause();
        if (wbX5 != null) {
            wbX5.onPause();
        }
        isResume = false;
        if (xyyJS != null) {
            xyyJS.onPause();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        GlobalViewModelStore.Companion.get().clearGlobalViewModelStore();
        if (wbX5 != null) {
            wbX5.onResume();
        }
        isSharing = false;
        isResume = true;
        if (xyyJS != null) {
            xyyJS.onResume();
        }
    }

    @Override
    protected String getRawAction() {
        return rawUrl;
    }

    @Override
    protected String getTitleName() {
        return title;
    }

    @Override
    protected void onDestroy() {
        if (wbX5 != null) {
            wbX5.destroy();
        }
        if (updateManagerV2!= null){
            updateManagerV2=null;
        }
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(IntentCanst.ACTION_MY_WEALTH_REFRESH));
//        XyyReportManager.detach();
    }

    @Override
    protected void refreshPosition() {
        if (wbX5 != null) {
            wbX5.callJs("window._self_fn.change_page", "");
        }
        if (wbX5 != null) {
            wbX5.callJs("window._self_fn.isTimeCalibration", "");
        }
    }

    private boolean isSharing;  //是否调起了分享。如果调起分享，这个值为true。
    private boolean isResume;  //Activity是否处于前台。

    @Override
    protected void onRestart() {
        super.onRestart();
        if (isSharing) {
            isSharing = false;
            //这里要延时0.2秒在判断是否回调了onResume，因为onRestart在onResume之前执行。
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    // 如果0.2秒后没有调用onResume，则认为是分享成功并且留着微信。
                    if (!isResume) {
                        if (wbX5 != null) {
                            wbX5.callJs("window._self_fn.isShareSuccess", "");
                        }
                    }
                }
            }, 200);
        }
    }

    /**
     * 上传图片
     *
     * @param pathList
     */
    public void uploadCircle(final LinkedList<String> pathList) {
        if (isFinishing()) return;
        if (pathList.isEmpty()) {
            dismissProgress();
            JSONObject obj = new JSONObject();
            try {
                obj.put("code", "200");
                // uploadUrl
                // 此处result返回格式有错误，但线上已有调用需要一起改动，因此暂时注掉，需要时候放开就可以
                /*JSONArray jsonArray = new JSONArray();
                if (uploadUrl == null) {
                    uploadUrl = new ArrayList<>();
                }
                int urlsize = uploadUrl.size();
                for (String url : uploadUrl) {
                    jsonArray.put(url);
                }
                obj.put("result", jsonArray);*/

                obj.put("result", uploadUrl.toString());
                obj.put("msg", "");
                wbX5.loadUrl("javascript:getImageVideoCallBack('" + obj.toString() + "')");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (uploadUrl != null) uploadUrl.clear();
            if (uploadUrlList != null) uploadUrlList.clear();
            return;
        }
        final String path = pathList.removeFirst();
        if (path == null) {
            ToastUtils.showShort("上传文件不存在");
            return;
        }
        File file = new File(path);
        if (file == null || !file.exists()) {
            ToastUtils.showShort("上传文件不存在");
            return;
        }
        RequestParams params = new RequestParams();
        if (!TextUtils.isEmpty(SpUtil.getMerchantid())) {
            params.put("merchantId", SpUtil.getMerchantid());
        }
        params.put("file", file);
        HttpManager.getInstance().post(AppNetConfig.TASK_CENTER_UPLOAD_FILE, params, new BaseResponse<List<String>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<String>> data, List<String> bean) {
                // dismissProgress();
                if (bean != null && bean.size() > 0) {
                    if (uploadUrl == null) {
                        uploadUrl = new ArrayList<>();
                    }
                    uploadUrl.add(bean.get(0));
                    uploadCircle(pathList);
                    LogUtils.e("单张上传成功  " + path + " - " + bean.get(0));
                    //需要保持上传成功服务器返回的地址
                } else {
                    dismissProgress();
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                JSONObject obj = new JSONObject();
                try {
                    obj.put("code", "500");
                    obj.put("result", null);
                    obj.put("msg", error.message);
                    wbX5.loadUrl("javascript:getImageVideoCallBack('" + obj.toString() + "')");
                    if (uploadUrl != null) uploadUrl.clear();
                } catch (JSONException e) {
                    e.printStackTrace();
                    if (uploadUrl != null) uploadUrl.clear();
                }
            }
        });

    }

    @Override
    public void onBackPressed() {
        setCallH5Method(0);
    }
    private void goBack(){
        if (!TextUtils.isEmpty(htmlStr)) {
            return;
        }
        super.onBackPressed();
    }

    @Override
    protected void cartClick() {
        super.cartClick();
        trackCart();
        JGTrackTopLevelKt.jgTrackCommonBtnClick(this, "购物车", url);
    }

    private void downloadFile(String url, String extension, boolean isVideo) {
        if (updateManagerV2 == null) {
            updateManagerV2 = new AppUpdateManagerV2();
        }

        // 提取文件名
        String fileName = url.substring(url.lastIndexOf("/") + 1);
        if (fileName.contains("?")) {
            fileName = fileName.substring(0, fileName.indexOf("?"));
        }

        updateManagerV2.setDownloadListener(new AppUpdateManagerV2.OnDownloadListener() {
            @Override
            public void onDownloadSuccess(File file) {
                dismissProgress();
                if (!isDestroyed()) {
                    // 保存到相册
                    saveMediaToGallery(file, isVideo);
                }
            }

            @Override
            public void onDownloading(int progress) {
                // 下载进度回调
            }

            @Override
            public void onDownloadFailed(Exception e) {
                dismissProgress();
                if (!isDestroyed()) {
                   runOnUiThread(() -> ToastUtils.showShort("文件下载失败"));
                }
            }
        });
        updateManagerV2.downFile(url, fileName);
    }
    private void saveMediaToGallery(File file, boolean isVideo) {
        try {
            ContentValues values = new ContentValues();
            values.put(MediaStore.MediaColumns.DISPLAY_NAME, file.getName());
            values.put(MediaStore.MediaColumns.MIME_TYPE,
                    isVideo ? "video/mp4" : "image/jpeg");

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ 使用 MediaStore API
                values.put(MediaStore.MediaColumns.RELATIVE_PATH,
                        isVideo ? Environment.DIRECTORY_MOVIES + "/药帮忙"
                                : Environment.DIRECTORY_PICTURES + "/药帮忙");

                Uri collection = isVideo ?
                        MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY) :
                        MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY);

                Uri uri = getContentResolver().insert(collection, values);

                try (OutputStream out = getContentResolver().openOutputStream(uri);
                     InputStream in = new FileInputStream(file)) {
                    byte[] buf = new byte[1024];
                    int len;
                    while ((len = in.read(buf)) > 0) {
                        out.write(buf, 0, len);
                    }
                }
            } else {
                // Android 9 及以下版本
                values.put(MediaStore.MediaColumns.DATA, file.getAbsolutePath());
                Uri uri = isVideo ?
                        MediaStore.Video.Media.EXTERNAL_CONTENT_URI :
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
                getContentResolver().insert(uri, values);

                // 触发媒体扫描
                MediaScannerConnection.scanFile(
                        this,
                        new String[]{file.getAbsolutePath()},
                        new String[]{isVideo ? "video/*" : "image/*"},
                        null
                );
            }

            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showShort("已保存到相册");
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
           runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showShort("保存到相册失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 调用H5方法并获取返回值
     * @param jsMethod H5方法名(如"window.getUserInfo")
     * @param callback 回调接口，用于接收返回值
     */
    public void callH5Method(String jsMethod, final JsCallback callback) {
        if (wbX5 == null) {
            callback.onResult(null);
            return;
        }

        wbX5.evaluateJavascript(jsMethod, new com.tencent.smtt.sdk.ValueCallback<String>() {
            @Override
            public void onReceiveValue(String s) {
                callback.onResult(s);
            }
        });
    }

    /**
     * JavaScript回调接口
     */
    public interface JsCallback {
        void onResult(String value);
    }

    @NonNull
    @Override
    public X5WebView getX5WebView() {
        return wbX5;
    }
}

package com.ybmmarket20.activity;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.InputType;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.github.mzule.activityrouter.annotation.Router;
import com.tencent.tauth.IUiListener;
import com.tencent.tauth.Tencent;
import com.tencent.tauth.UiError;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.PlanListAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.PlanAddBean;
import com.ybmmarket20.bean.PlanListData;
import com.ybmmarket20.bean.ShareInfoBean;
import com.ybmmarket20.bean.TabEntity;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.ViewOnClickListener;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.ConstantData;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.view.DividerLine;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 电子计划单  下掉
 */
@Deprecated
@Router("eplanlist")
public class ElectronicPlanListActivity extends BaseActivity implements
        CommonRecyclerView.Listener, PlanListAdapter.OnSwipeListener, PlanListAdapter.OnItemClickListener {

    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
//    @Bind(R.id.rb_electronic_plan)
//    RadioButton rbElectronicPlan;
//    @Bind(R.id.rb_image_cart)
//    RadioButton rbImageCart;
    @Bind(R.id.crv_list)
    CommonRecyclerView rvPlanList;
    @Bind(R.id.tv_btn_ok)
    TextView tvBtnOk;
    @Bind(R.id.ctl)
    CommonTabLayout ctl;

    private PlanListAdapter mAdapter;
    private List<PlanListData> mDataList = new ArrayList<>();
    private BaseQQShareListener mQQShareListener;
    private int mElectronicTab = 0;
    private String[] mTitles = {"电子计划单", "图片采购单"};
    private ArrayList<CustomTabEntity> mTabEntities = new ArrayList<>();

    @Override
    protected void initData() {
        setTitle("电子计划单");
        setRigthText(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.PLAN_EXPLAIN);
            }
        }, "使用帮助");
        mAdapter = new PlanListAdapter(R.layout.list_item_plan_list, mDataList, mElectronicTab);
        mAdapter.setOnSwipeListener(this);
        mAdapter.setOnItemClickListener(this);
        mAdapter.setEnableLoadMore(false);
        mAdapter.setEmptyView(this,R.layout.layout_empty_view, R.drawable.icon_empty, "您还没有计划单");
        rvPlanList.setListener(this);
        rvPlanList.setShowAutoRefresh(false);
        rvPlanList.setRefreshEnable(true);
        DividerLine divider = new DividerLine(DividerLine.VERTICAL);
        divider.setSize(1);
        divider.setColor(0xfff1f1f1);
        rvPlanList.addItemDecoration(divider);
        rvPlanList.setAdapter(mAdapter);

        for (int i = 0; i < mTitles.length; i++) {
            mTabEntities.add(new TabEntity(mTitles[i]));
        }
        ctl.setTabData(mTabEntities);
        ctl.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {

                if (position == 0) {
                    tvBtnOk.setText("新建电子计划单");
                    switchTab(0);
                } else {
                    tvBtnOk.setText("上传采购单图片");
                    switchTab(1);
                }

            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_electronic_plan_list;
    }

    @Override
    protected void onResume() {
        super.onResume();
        getPlanList(mElectronicTab);
    }

    @OnClick({R.id.ll_create_list})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ll_create_list:

                String str = tvBtnOk.getText().toString().trim();
                if (str.equals("新建电子计划单")) {

                    addPlanList(-1, null, true);
                } else {

                    RoutersUtils.open("ybmpage://uploadimagecartdetail/");
                }

                break;
        }
    }

    private void switchTab(int tab) {
        //如果点击的是同一个tab就不请求数据
        if (mAdapter == null || mElectronicTab == tab) {
            return;
        }
        mElectronicTab = tab;
//        rbElectronicPlan.setChecked(tab == 0);
//        rbImageCart.setChecked(tab == 1);

//        showProgress();
//        getRefreshOrLoadMoreResponse(mPage = 0);
        getPlanList(tab);
    }

    /**
     * 添加计划单第一步
     *
     * @param defaultStr
     * @param toDetail
     */
    private void addPlanList(final int planId, String defaultStr, final boolean toDetail) {
        final boolean isModify;
        final StringBuilder sb = new StringBuilder();
        String title, confirm, cancel;
        if (!TextUtils.isEmpty(defaultStr)) {
            isModify = true;
            if (defaultStr.length() > 10) {
                sb.append(defaultStr.substring(0, 10));
            } else {
                sb.append(defaultStr);
            }

            title = "重命名计划单";
            confirm = "确定";
            cancel = "我再想想";
        } else {
            isModify = false;
            Calendar calendar = Calendar.getInstance();
            sb.append(calendar.get(Calendar.MONTH) + 1).append("月").append(calendar.get(Calendar.DAY_OF_MONTH))
                    .append("日计划单");
            title = "新建补货计划单";
            confirm = "确定";
            cancel = "我再想想";
        }

        String tip = "请为计划单输入名称";
        DialogUtil.inputDialog(this, InputType.TYPE_CLASS_TEXT, title, tip, confirm, cancel, sb.toString(), new DialogUtil.DialogClickListener() {
            @Override
            public void confirm(String content) {
                if (TextUtils.isEmpty(content)) {
                    ToastUtils.showShort("计划单名称不能为空");
                    return;
                }
                modifyPlanList(isModify, planId, content, toDetail);
            }

            @Override
            public void cancel() {

            }

            @Override
            public void showSoftInput(View view) {
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
            }

        });
    }

    @Override
    public void onRefresh() {
        getPlanList(mElectronicTab);
    }

    @Override
    public void onLoadMore() {

    }

    @Override
    public void onRename(int position) {
        if (mDataList != null && mDataList.size() > position) {
            String name = mDataList.get(position).planningName;
            int id = mDataList.get(position).id;
            addPlanList(id, name, false);
        }
    }

    @Override
    public void onDelete(final int position) {
        final PlanListData item = (PlanListData) mAdapter.getItem(position);
        if (item == null) {
            return;
        }
        final AlertDialogEx alert = new AlertDialogEx(this);
        alert.setTitle("删除");
        alert.setMessage("您确认删除吗？");
        alert.setCancelButton("取消", null);
        alert.setConfirmButton("确定", new ViewOnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                deletePlan(item.id);
            }
        });
        alert.show();
    }

    @Override
    public void onShare(int position) {
        if (mDataList == null || mDataList.size() <= position) {
            return;
        }
        if (mDataList.get(position).productAmount <= 0) {
            ToastUtils.showShort("该计划单没有记录，无法转发");
            return;
        }
        final String planId = mDataList.get(position).id + "";
        DialogUtil.shareDialog(this, new DialogUtil.DialogSingleClick() {
            @Override
            public void click(String content) {
                if (TextUtils.isEmpty(content)) {
                    return;
                }
                sharePlanSchedule(planId, content);
            }
        });
    }

    @Override
    public void onSwipe(int position) {
    }

    @Override
    public void onItemClick(int position) {
        if (tvBtnOk == null || position == -1) {
            return;
        }
        if (mDataList != null && mDataList.size() > position) {
            switch (mElectronicTab) {
                case 0:
                    String name = mDataList.get(position).planningName;
                    int id = mDataList.get(position).id;
                    StringBuilder sb = new StringBuilder();
                    sb.append("ybmpage://plandetailactivity/");
                    sb.append(id).append("/").append(name);
                    RoutersUtils.open(sb.toString());
                    break;
                case 1:
                    id = mDataList.get(position).id;
                    RoutersUtils.open("ybmpage://imagecartdetail/" + id);
                    break;
            }
        }
    }

    /**
     * 获取分享的详情
     */
    private void sharePlanSchedule(String planId, final String shareType) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("planningScheduleId", planId);
        HttpManager.getInstance().post(AppNetConfig.PLAN_SCHEDULE_SHARE, params, new BaseResponse<ShareInfoBean>() {
            @Override
            public void onSuccess(String content, BaseBean<ShareInfoBean> obj, ShareInfoBean data) {
                if (obj == null || !obj.isSuccess() || obj.getData() == null) {
                    ToastUtils.showShort("获取分享信息失败");
                    return;
                }
                if (ElectronicPlanListActivity.this.isFinishing()) {
                    return;
                }
                share(shareType, obj.getData().shareUrl, obj.getData().title
                        , obj.getData().content, obj.getData().photo);
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                ToastUtils.showShort("获取分享信息失败");
            }
        });
    }

    private void share(String platform, String url, String title, String desc, String photo) {
        if (!ShareUtil.isWeChatOrQQInstall(platform)) {
            return;
        }
        if ("wx".equals(platform)) {
            Bitmap bmp = BitmapFactory.decodeResource(getResources(), R.drawable.logo);
            ShareUtil.shareWXPage(title, url, desc, bmp);
        } else if ("qq".equals(platform)) {
            mQQShareListener = new BaseQQShareListener();
            Tencent tencent = Tencent.createInstance(ConstantData.APP_ID_QQ, this);
            ShareUtil.shareQQCommon(this, title, url, photo, desc, tencent, mQQShareListener);
        }
    }

    /**
     * 获取计划单列表
     */
    private void getPlanList(int tab) {

        if (tab == 0) {
            RequestParams params = new RequestParams();
            params.put("merchantId", merchant_id);
            HttpManager.getInstance().post(AppNetConfig.PLAN_SCHEDULE_LIST, params, new BaseResponse<List<PlanListData>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<PlanListData>> obj, List<PlanListData> data) {
                    rvPlanList.setRefreshing(false);
                    if (obj == null || !obj.isSuccess()) {
                        return;
                    }
                    if (obj.getData() != null) {

                        if (mDataList == null) {
                            mDataList = new ArrayList<>();
                        }
                        mDataList.clear();

                        mDataList = obj.getData();
                        if (mAdapter == null) {
                            mAdapter = new PlanListAdapter(R.layout.list_item_plan_list, mDataList, tab);
                            rvPlanList.setAdapter(mAdapter);
                        } else {
                            mAdapter.setNewData(mDataList, tab);
                        }
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    rvPlanList.setRefreshing(false);
                }
            });
        } else {
            RequestParams params = new RequestParams();
            params.put("merchantId", merchant_id);
            HttpManager.getInstance().post(AppNetConfig.PLANNINGSCHEDULE_PICTURE_LIST, params, new BaseResponse<List<PlanListData>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<PlanListData>> obj, List<PlanListData> data) {
                    rvPlanList.setRefreshing(false);
                    if (obj == null || !obj.isSuccess()) {
                        return;
                    }
                    if (obj.getData() != null) {

                        if (mDataList == null) {
                            mDataList = new ArrayList<>();
                        }
                        mDataList.clear();

                        mDataList = obj.getData();
                        if (mAdapter == null) {
                            mAdapter = new PlanListAdapter(R.layout.list_item_plan_list, mDataList, tab);
                            rvPlanList.setAdapter(mAdapter);
                        } else {
                            mAdapter.setNewData(mDataList, tab);
                        }
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    rvPlanList.setRefreshing(false);
                }
            });
        }
    }

    /**
     * 修改或者创建计划单
     */
    private void modifyPlanList(final boolean isModify, int planId, final String planName, final boolean toDetail) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        //如果是重命名，需要参数
        if (isModify && planId > 0) {
            params.put("planningScheduleId", planId + "");
        }
        params.put("planningName", planName);

        HttpManager.getInstance().post(AppNetConfig.PLAN_ADD_SCHEDULE, params, new BaseResponse<PlanAddBean>() {
            @Override
            public void onSuccess(String content, BaseBean<PlanAddBean> obj, PlanAddBean data) {
                //如果是空列表创建，扫码的状况下，成功之后需要跳转到扫描的页面
                if (obj == null || !obj.isSuccess()) {
                    return;
                }
                if (toDetail) {
                    StringBuilder sb = new StringBuilder("ybmpage://plandetailactivity/");
                    sb.append(obj.getData().getPlanningScheduleId()).append("/").append(planName);
                    RoutersUtils.open(sb.toString());
                } else {
                    //直接请求接口刷新
                    getPlanList(mElectronicTab);
                }
            }

            @Override
            public void onFailure(NetError error) {
                String err = isModify ? "创建计划单失败" : "重命名失败";
                if (error != null) {
                    err = error.message;
                }
                ToastUtils.showShort(err);
            }
        });
    }

    /**
     * 删除计划单
     */
    private void deletePlan(int planId) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("planningScheduleId", planId + "");
        HttpManager.getInstance().post(AppNetConfig.PLAN_SCHEDULE_DELETE, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                //成功了就刷新一下列表
                if (obj == null || !obj.isSuccess()) {
                    return;
                } else {
                    ToastUtils.showShort("删除成功");
                    getPlanList(mElectronicTab);
                }
            }

            @Override
            public void onFailure(NetError error) {

            }
        });
    }

    private class BaseQQShareListener implements IUiListener {
        @Override
        public void onComplete(Object o) {
            ToastUtils.showShort("分享成功");
        }

        @Override
        public void onError(UiError uiError) {
            ToastUtils.showShort("分享失败");
        }

        @Override
        public void onCancel() {
            ToastUtils.showShort("用户取消");
        }

        @Override
        public void onWarning(int i) {
            ToastUtils.showShort("用户警告");
        }
    }

}

package com.ybmmarket20.activity;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 注册第二步-邀请码
 */
@Deprecated
@Router("authorization")
public class AuthorizationActivity extends BaseActivity {

    @Bind(R.id.authorization_et)
    EditText authorizationEt;
    @Bind(R.id.authorization_btn)
    TextView authorizationBtn;


    @Override
    protected void initData() {
        setRigthText(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        }, "登录");
        setTitle("激活账号");
        setLeftVisibility(View.INVISIBLE);
        authorizationEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String et = authorizationEt.getText().toString();
                authorizationBtn.setEnabled(!TextUtils.isEmpty(et));
            }
        });
        //authorizationTv.setText(Html.fromHtml(BaseYBMApp.getApp().getCurrActivity().getString(R.string.text_authorization)));

    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_authorization;
    }

    @OnClick({R.id.authorization_btn, R.id.authorization_kefu})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.authorization_btn:
                postRegisterResult();
                break;
            case R.id.authorization_kefu:
                RoutersUtils.telKefu(true);
                break;
        }
    }

    /**
     * 发送请求码完成注册
     * mobileNumber => 电话
     * authCode => 验证码
     * 注册邀请码url => "registerStep2"
     */
    private void postRegisterResult() {
        final String authorization = authorizationEt.getText().toString().trim();
        final String phone = SpUtil.readString("phone", "");

        if (TextUtils.isEmpty(authorization)) {
            ToastUtils.showShort("请输入邀请码");
            return;
        }
        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showShort("请重新注册");
            return;
        }
        showProgress();
        RequestParams params = new RequestParams();
        params.put("mobileNumber", phone);
        params.put("authCode", authorization);
        HttpManager.getInstance().post(AppNetConfig.REGISTER_STEP, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                dismissProgress();
                if (obj != null) {
                    if (obj.isSuccess()) {
                        SpUtil.writeString("phone", "");
                        gotoAtivity(LoginActivity.class, null);
                        //XyyIoUtil.track(XyyIoUtil.ACTION_ACTIVATION);
                        finish();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

        });
    }
}

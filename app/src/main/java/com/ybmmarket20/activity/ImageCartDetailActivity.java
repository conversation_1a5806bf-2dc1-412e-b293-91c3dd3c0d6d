package com.ybmmarket20.activity;

import android.graphics.Paint;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.view.WrapGridLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ImageCartBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;
import butterknife.OnClick;

/*
 * 查看图片采购单详情
 * */
@Router({"imagecartdetail", "imagecartdetail/:id"})
public class ImageCartDetailActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
    @Bind(R.id.list)
    RecyclerView list;
    @Bind(R.id.tv_name)
    TextView tvName;
    @Bind(R.id.tv_time)
    TextView tvTime;
    @Bind(R.id.tv_status)
    TextView tvStatus;
    @Bind(R.id.tv_headline)
    TextView tvHeadline;
    @Bind(R.id.ll_headline)
    LinearLayout llHeadline;


    private String id;

    private List<String> mData = new ArrayList<>();
    private YBMBaseAdapter mAdapter;
    private SimpleDateFormat dateFormat;
    private String planningScheduleId;
    private String electronicPlanName;


    @Override
    protected void initData() {

        setTitle("采购单图片");
        id = getIntent().getStringExtra("id");
        if (TextUtils.isEmpty(id)) {
            ToastUtils.showShort("参数错误");
            finish();
            return;
        }
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());

        mAdapter = new YBMBaseAdapter<String>(R.layout.item_image_cart_list, mData) {
            @Override
            protected void bindItemView(YBMBaseHolder ybmBaseHolder, final String bean) {

                ImageHelper.with(mContext).load(AppNetConfig.getCDNHost() + bean)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE).placeholder(R.drawable.jiazaitu_min)
                        .into(((ImageView) ybmBaseHolder.getView(R.id.iv)));

            }
        };
        list.setEnabled(false);
        list.setNestedScrollingEnabled(false);
        list.setLayoutManager(new WrapGridLayoutManager(this, 4));
        list.setAdapter(mAdapter);
        setTextLine();
        getData(id);
    }

    private void setTextLine() {
        if(tvHeadline==null){
            return;
        }

        tvHeadline.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        tvHeadline.getPaint().setAntiAlias(true);//抗锯齿

    }

    /*
     * 获取采购单图片数据
     * */
    private void getData(String id) {

        if (list == null) {
            return;
        }
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("id", id);
        HttpManager.getInstance().post(AppNetConfig.PLANNINGSCHEDULE_PICTURE_DETAILS, params, new BaseResponse<ImageCartBean>() {
            @Override
            public void onSuccess(String content, BaseBean<ImageCartBean> obj, ImageCartBean data) {

                if (obj != null && obj.isSuccess()) {

                    setData(data);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    /*
     * 设置数据
     * */
    private void setData(ImageCartBean data) {

        if (tvName == null) {
            return;
        }
        String format = dateFormat.format(new Date(data.subTime));

        tvName.setText(data.purchaseName);
        tvTime.setText(format);
        //状态：1：已创建计划单，0：未创建计划单
        tvStatus.setText(data.planStatus == 0 ? "未创建计划单" : "已创建计划单");
        tvHeadline.setText(data.electronicPlanName);
        this.electronicPlanName = data.electronicPlanName;
        this.planningScheduleId = data.planningScheduleId;
        llHeadline.setVisibility(TextUtils.isEmpty(data.electronicPlanName) ? View.GONE : View.VISIBLE);

        tvName.postDelayed(new Runnable() {
            @Override
            public void run() {

                if (data.picUrls != null) {

                    String[] urlStr = data.picUrls.split(";");

                    List<String> url = Arrays.asList(urlStr);

                    if (mData == null) {
                        mData = new ArrayList<>();
                    }
                    mData.clear();
                    mData.addAll(url);
                    mAdapter.setNewData(mData);
                }
            }
        }, 200);


    }

    @OnClick({R.id.ll_headline})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ll_headline:
                if (!TextUtils.isEmpty(planningScheduleId)) {
                    RoutersUtils.open("ybmpage://plandetailactivity/" + planningScheduleId + "/" + electronicPlanName);
                }
                break;
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_image_cart_detail;
    }

}

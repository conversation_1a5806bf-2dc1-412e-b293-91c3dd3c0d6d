package com.ybmmarket20.activity;

import android.content.Intent;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CartVoucher;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.RowsListBean;
import com.ybmmarket20.bean.VoucherListBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 优惠券可用商品
 */
@Deprecated
//@Router({"couponavailableactivity", "couponavailableactivity/:coupon_id"})
public class CouponAvailableProductActivity extends BaseProductActivity {

    @Bind(R.id.list_lv)
    CommonRecyclerView commonLv;
    @Bind(R.id.tv_tips)
    TextView tvTips;
    @Bind(R.id.tv_total)
    TextView tvTotal;
    @Bind(R.id.tv_go)
    TextView tvGo;

    private List<RowsBean> rows = new ArrayList<>();
    private int mPageSize = 10;
    private int mPage = 0;
    private String mCouponId;

    @Override
    protected void initData() {
        super.initData();
        setTitle("可用商品");
        String emptyStr = "此优惠券没有可购商品!";
        Intent data = getIntent();
        if (data != null) {
            mCouponId = data.getStringExtra("coupon_id");
        }
        detailAdapter = new GoodsListAdapter(R.layout.item_goods, rows, false, false);
        detailAdapter.openLoadMore(mPageSize, true);
        detailAdapter.setEmptyView(this,R.layout.layout_empty_view, R.drawable.icon_empty, emptyStr);
        commonLv.setEnabled(true);
        commonLv.setAdapter(detailAdapter);
        commonLv.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getLoadMoreResponse(0);
            }

            @Override
            public void onLoadMore() {
                getLoadMoreResponse(mPage);
            }
        });
        detailAdapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {
                if (rows != null) {
                    Intent intent = new Intent(CouponAvailableProductActivity.this, ProductDetailActivity.class);
                    intent.putExtra(IntentCanst.PRODUCTID, rows.getId() + "");
                    startActivity(intent);
                }
            }
        });
        getCouponData();
    }

    @Override
    protected String getRawAction() {
        return "ybmpage://couponavailableactivity/" + mCouponId;
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_coupon_product;
    }

    @Override
    protected void refreshPosition() {
        super.refreshPosition();
        getCouponData();
    }

    public void getCouponData() {
        if (TextUtils.isEmpty(mCouponId)) {
            return;
        }
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("voucherTemplateId", mCouponId);
        HttpManager.getInstance().post(AppNetConfig.SELECT_CART_VOUCHER, params, new BaseResponse<CartVoucher>() {

            @Override
            public void onSuccess(String content, BaseBean<CartVoucher> baseBean, CartVoucher data) {
                if(baseBean !=null && baseBean.isSuccess() && data !=null && data.getList() !=null && data.getList().size()>0) {
                    VoucherListBean bean = data.getList().get(0);
                    String desc = "";
                    if (bean.noEnoughMoney <= 0) {
                        desc = getResources().getString(R.string.cart_coupon_product_desc);
                        desc = String.format(desc,StringUtil.DecimalFormat2Double(bean.selectSkuAmount),StringUtil.DecimalFormat2Double(bean.moneyInVoucher));
                    } else {
                        desc = getResources().getString(R.string.cart_coupon_product_desc_2);
                        desc = String.format(desc,StringUtil.DecimalFormat2Double(bean.selectSkuAmount), StringUtil.DecimalFormat2Double(bean.noEnoughMoney));
                    }
                    tvTotal.setText(Html.fromHtml(desc));
                    tvTips.setText(bean.skuListTips);
                }
            }

            @Override
            public void onFailure(NetError error) {

            }
        });
    }

    public void getLoadMoreResponse(final int page) {
        if (TextUtils.isEmpty(mCouponId)) {
            return;
        }
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("voucherTemplateId", mCouponId);
        params.put("limit", String.valueOf(mPageSize));
        params.put("offset", String.valueOf(String.valueOf(page)));
        HttpManager.getInstance().post(AppNetConfig.VOUCHER_FIND_PRODUCE, params, new BaseResponse<RowsListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RowsListBean> brandBean, RowsListBean rowsBeans) {
                completion();
                if (brandBean != null && brandBean.isSuccess()) {
                    if (rowsBeans != null) {
                        if (rowsBeans.rows != null && rowsBeans.rows.size() > 0) {
                            if (page <= 0) {
                                CouponAvailableProductActivity.this.mPage = 1;
                            } else {
                                CouponAvailableProductActivity.this.mPage++;
                            }
                        }
                        if (page <= 0) {
                            if (rows == null) {
                                rows = new ArrayList<>();
                            }
                            if (rows.size() <= 0 && rowsBeans.rows != null) {
                                rows.addAll(rowsBeans.rows);
                            } else {
                                if (rowsBeans.rows != null && !rowsBeans.rows.isEmpty()) {
                                    for (RowsBean bean : rowsBeans.rows) {
                                        if (rows.contains(bean)) {
                                            rows.remove(bean);
                                        }
                                    }
                                    rows.addAll(0, rowsBeans.rows);
                                }
                            }
                            detailAdapter.setNewData(rows);
                            detailAdapter.notifyDataChangedAfterLoadMore(rows.size() >= mPageSize);
                        } else {
                            if (rowsBeans.rows == null || rowsBeans.rows.size() <= 0) {
                                detailAdapter.notifyDataChangedAfterLoadMore(false);
                            } else {
                                for (RowsBean bean : rowsBeans.rows) {
                                    if (rows.contains(bean)) {
                                        rows.remove(bean);
                                    }
                                }
                                rows.addAll(rowsBeans.rows);
                                detailAdapter.setNewData(rows);
                                detailAdapter.notifyDataChangedAfterLoadMore(rowsBeans.rows.size() >= mPageSize);
                            }
                        }
                    }
                } else {
                    detailAdapter.setNewData(rows);
                    detailAdapter.notifyDataChangedAfterLoadMore(false);
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                if (commonLv != null) {
                    commonLv.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                if (commonLv != null) {
                                    detailAdapter.setNewData(rows);
                                    detailAdapter.notifyDataChangedAfterLoadMore(false);
                                }
                            } catch (Throwable e) {
                                BugUtil.sendBug(e);
                            }
                        }
                    }, 300);
                }
            }
        });
    }

    private void completion() {
        if (commonLv != null) {
            try {
                commonLv.setRefreshing(false);
            } catch (Throwable e) {
                BugUtil.sendBug(e);
            }
        }
    }

    @Override
    protected void startAnim(boolean isAdd) {
        super.startAnim(isAdd);
        getCouponData();
    }

    @OnClick({ R.id.tv_go})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_go:
                cartClick();
                break;
        }
    }
}

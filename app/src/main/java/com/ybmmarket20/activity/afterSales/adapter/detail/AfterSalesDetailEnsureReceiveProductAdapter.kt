package com.ybmmarket20.activity.afterSales.adapter.detail

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.adapter.YBMSingleViewAdapter
import com.ybmmarket20.bean.aftersales.AfterSalesCompanyReceiveProductInfo
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundTextView


/**
 * 售后详情状态信息
 */
class AfterSalesDetailEnsureReceiveProductAdapter(
    singleData: AfterSalesCompanyReceiveProductInfo
) : YBMSingleViewAdapter<AfterSalesCompanyReceiveProductInfo>(
    R.layout.item_after_sales_detail_ensure_receive_product_info,
    singleData
) {
    override fun bindSingleView(holder: YBMBaseHolder, bean: AfterSalesCompanyReceiveProductInfo) {
        val rtvCopyReceive = holder.getView<RoundTextView>(R.id.rtvCopyReceive)
        val tvRecipient = holder.getView<TextView>(R.id.tvRecipient)
        val tvAddress = holder.getView<TextView>(R.id.tvAddress)
        val tvPhone = holder.getView<TextView>(R.id.tvPhone)
        val tvExpressTips = holder.getView<TextView>(R.id.tvExpressTips)
        tvRecipient.text = bean.recipient
        tvAddress.text = bean.deliveryAddress
        tvPhone.text = bean.receivingPhone
        tvExpressTips.text = bean.expressRemarks
        rtvCopyReceive.setOnClickListener {
            //获取剪贴板管理器：
            val cm: ClipboardManager =
                mContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            // 创建普通字符型ClipData
            val mClipData = ClipData.newPlainText("Label",
                """
                收件人：${bean.recipient}
                收货地址：${bean.deliveryAddress}
                联系电话：${bean.receivingPhone}
                快递说明：${bean.expressRemarks}
                """.trimIndent())
            // 将ClipData内容放到系统剪贴板里。
            cm.setPrimaryClip(mClipData)
            ToastUtils.showShort("复制成功")
        }
    }
}
package com.ybmmarket20.activity

import android.widget.TextView
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.ClerkInfoBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.ClerkInfoViewModel
import kotlinx.android.synthetic.main.activity_clerk_aptitude_authentication.*

/**
 * 店员信息
 */
@Router("clerkinfo")
class ClerkInfoActivity: BaseActivity() {

    var merchantId: String? = ""

    private val mViewModel: ClerkInfoViewModel by viewModels()
    private val mList = mutableListOf<ClerkInfoBean>()
    private val mAdapter = ClerkInfoAdapter(mList)

    override fun getContentViewId(): Int = R.layout.activity_clerk_info

    override fun initData() {
        setTitle("店员信息")
        merchantId = intent.getStringExtra("merchantId")
        rv.layoutManager = WrapLinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rv.adapter = mAdapter
        mAdapter.setEnableLoadMore(true)
        val emptyView = layoutInflater.inflate(R.layout.layout_empty_view, null)
        mAdapter.emptyView = emptyView
        initObserver()
        showProgress()
        mViewModel.getClerkInfoList(merchantId?: "")
        mAdapter.setOnLoadMoreListener {
            mViewModel.getClerkInfoList(merchantId?: "")
        }
    }

    private fun initObserver() {
        mViewModel.clerkInfoListLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                it.data.list?.let { it1 -> mList.addAll(it1) }
                mAdapter.notifyDataChangedAfterLoadMore(!it.data.isEnd())
                if (it.data.isEnd()) mAdapter.loadMoreComplete()
            }
        }
    }

    inner class ClerkInfoAdapter(list: MutableList<ClerkInfoBean>): YBMBaseAdapter<ClerkInfoBean>(R.layout.item_clerk_info, list) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ClerkInfoBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                val tv = holder.getView<TextView>(R.id.tvPhoneNo)
                tv.text = "手机号: ${bean.mobile}"
            }
        }

    }
}
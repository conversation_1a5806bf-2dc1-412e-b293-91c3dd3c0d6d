package com.ybmmarket20.activity;

import android.content.DialogInterface;
import android.text.TextUtils;

import com.github.mzule.activityrouter.annotation.Router;
import com.github.mzule.activityrouter.router.Routers;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;

/**
 * 优惠券接口分发页面，页面为透明,为动态配置使用
 */
@Router({"voucherdispatch","voucherdispatch/:receive_template_id","voucherdispatch/:receive_voucher_id", "voucherdispatch/:del_voucher_id","voucherdispatch/:api"})
public class VoucherDispatchActivity extends BaseActivity {

    private AlertDialogEx alert;
    protected String voucher_id;
    protected String receive_template_id;
    protected String del_voucher_id;
    protected String api;
    protected int defpageSize = 20;
    private String ok ="知道了";
    private String failOk ="知道了";

    @Override
    protected void initData() {
        voucher_id = getIntent().getStringExtra("receive_voucher_id");
        receive_template_id = getIntent().getStringExtra("receive_template_id");
        del_voucher_id = getIntent().getStringExtra("del_voucher_id");
        api = getIntent().getStringExtra("api");
        if(TextUtils.isEmpty(voucher_id) && TextUtils.isEmpty(del_voucher_id) && TextUtils.isEmpty(api) && TextUtils.isEmpty(receive_template_id)){
            finish();
            return;
        }else {
            if (!TextUtils.isEmpty(voucher_id) || !TextUtils.isEmpty(receive_template_id)) {//点击领取优惠券
               receiveVoucherShowDialog(voucher_id, receive_template_id, new BaseResponse<EmptyBean>() {
                    @Override
                    public void onSuccess(String content,BaseBean<EmptyBean> data ,EmptyBean obj) {
                        dismissProgress();
                        if (data != null && data.isSuccess()) {
                            showDialog("优惠券领取成功", ok);
                        }
                    }

                   @Override
                   public void onFailure(NetError error) {
                       super.onFailure(error);
                       dismissProgress();
                   }
               });
            }
            if (!TextUtils.isEmpty(del_voucher_id)) {
                delVocherShowDialog(del_voucher_id, new BaseResponse<EmptyBean>() {
                    @Override
                    public void onSuccess(String content,BaseBean<EmptyBean> data, EmptyBean obj) {
                        if (data != null && data.isSuccess()) {
                            showDialog("优惠券删除成功", ok);
                        }
                    }
                    @Override
                    public void onFailure(NetError error) {
                        super.onFailure(error);
                        dismissProgress();
                    }
                });
            }

            if (!TextUtils.isEmpty(api)) {
                String rawUrl = getIntent().getStringExtra(Routers.KEY_RAW_URL);
                if(rawUrl !=null && rawUrl.contains("http")) {//没有base64,
                    api = rawUrl.substring(rawUrl.indexOf("api=") + 4);
                }
                showProgress();
                HttpManager.getInstance().postParser(api, new BaseResponse<EmptyBean>() {
                    @Override
                    public void onSuccess(String content,BaseBean<EmptyBean> data ,EmptyBean obj) {
                        dismissProgress();
                        if(obj != null && data.isSuccess()){
                            finish();
                        }
                    }

                    @Override
                    public void onFailure(NetError error) {
                        super.onFailure(error);
                        dismissProgress();
                        finish();
                    }
                });
            }
        }
    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_voucher_disaptch;
    }

    public void showDialog(String msg,String bntMsg){
        if(alert == null) {
            alert = new AlertDialogEx(this);
        }
        alert.setMessage(msg).setCancelButton(bntMsg, new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
                finish();
            }
        }).setDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                finish();
            }
        }).show();
    }

    //领取优惠券,返回true 成功
    public void receiveVoucher(String voucher_id,String voucherTemplateId, BaseResponse handler) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        if(voucherTemplateId == null) {
            params.put("voucherId", voucher_id);
        }else {
            params.put("voucherTemplateId", voucherTemplateId);
        }
        commonRequest(AppNetConfig.RECEIVE_USABLE_VOUCHER, params, handler);
    }

    // //显示加载动画的领取优惠券,返回true 成功
    public void receiveVoucherShowDialog(String voucher_id,String voucherTemplateId,BaseResponse handler ) {
        showProgress();
        receiveVoucher(voucher_id,voucherTemplateId, handler);
    }

    //删除优惠券,返回true 成功
    public void delVoucher(String voucher_id, BaseResponse handler) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("voucherId", voucher_id);
        commonRequest(AppNetConfig.DEL_VOUCHER, params, handler);
    }

    //显示加载动画的删除
    public void delVocherShowDialog(String voucher_id, BaseResponse handler) {
        showProgress();
        delVoucher(voucher_id, handler);
    }

    private void commonRequest(String url, RequestParams params, BaseResponse handler) {
        HttpManager.getInstance().post(url, params, handler);
    }

}

package com.ybmmarket20.activity;

import static com.ybmmarket20.activity.AptitudeXyyEmailActivity.FLAG_ONE;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.ClipboardManager;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.viewpager.widget.ViewPager;

import com.github.barteksc.pdfviewer.PDFView;
import com.github.mzule.activityrouter.annotation.Router;
import com.google.gson.Gson;
import com.tencent.smtt.sdk.TbsReaderView;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.AptitudeXyyImageAdapter;
import com.ybmmarket20.bean.AptitudePdfUrlBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.common.AppUpdateManagerV2;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.OnClick;

/**
 * 小药药资质pdf详情页
 */
@Router({"aptitudexyypdf", "aptitudexyypdf/:title/:contractId/:shopCode"})
public class AptitudeXyyPdfActivity extends AptitudeXyyPdfAnalysisActivity implements TbsReaderView.ReaderCallback {
//    private TbsReaderView mTbsReaderView;
    private PDFView pdfView;
    private TextView tvQualificationName;
    private TextView tvQualificationCode;
    private LinearLayout llQualification;

    private String orgId;
    private String mFileUrl;
    private String mFileName;
    private String contractId;
    private ArrayList<String> links;
    private RelativeLayout mRootRl;
    private String mTitle;
    private File shareFile;
    private String shopCode;
    private TextView tvTip;
    //文件类型
    private String mFileType; //1：购销合同 11：购物金代收款说明 12:H5过来
    private String mOrderNo;
    private String qualificationName;
    private String qualificationCode;
    private String mIsOrder = null;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_aptitude_xyy_pdf;
    }

    @Override
    protected void initData() {
        setTitle("资质详情");
        setRigthImg(this::onViewClicked, R.drawable.icon_aptitude_xyy_pdf_share);
        orgId = getIntent().getStringExtra("orgId");
        mTitle = getIntent().getStringExtra("title");
        contractId = getIntent().getStringExtra("contractId");
        shopCode = getIntent().getStringExtra("shopCode");
        links = getIntent().getStringArrayListExtra("links");
        mFileUrl = getIntent().getStringExtra("pdfUrl");
        mFileType = getIntent().getStringExtra("fileType");
        mOrderNo = getIntent().getStringExtra("orderNo");
        qualificationName = getIntent().getStringExtra("qualificationName");
        qualificationCode = getIntent().getStringExtra("qualificationCode");
        mIsOrder = getIntent().getStringExtra("isOrder");
        if (!TextUtils.isEmpty(mTitle)) {
            setTitle(mTitle);
        }
        pdfView = findViewById(R.id.pdfAptitude);
        mRootRl = findViewById(R.id.rl_root);
        tvTip = findViewById(R.id.tv_tip);
        tvQualificationName = findViewById(R.id.tvQualificationName);
        tvQualificationCode = findViewById(R.id.tvQualificationCode);
        llQualification = findViewById(R.id.llQualification);
        setQualification();
        if (links == null) {
//            mTbsReaderView = new TbsReaderView(AptitudeXyyPdfActivity.this, this);
//            mRootRl.addView(mTbsReaderView, new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            pdfView.setVisibility(View.VISIBLE);
        } else {
//            List<String> linkList = new Gson().fromJson(links, new TypeToken<List<String>>() {
//            }.getType());
            setImages(links);
        }
        if (TextUtils.isEmpty(mFileUrl)) {
            getAptitudeXyyPdfUrl();
        } else {
            DownloadFile(mFileUrl);
        }
        if (TextUtils.equals(mFileType, "1")) {
            HashMap<String, String> trackParams = new HashMap<>();
            trackParams.put("pageName", "购销合同");
            XyyIoUtil.track("action_order_contract", trackParams);
        }
    }

    /**
     * 设置资质名称+备案号
     */
    private void setQualification() {
        tvQualificationName.setText(qualificationName);
        tvQualificationCode.setText("备案号：" + qualificationCode);
        if (TextUtils.isEmpty(qualificationName) || TextUtils.isEmpty(qualificationCode)) {
            llQualification.setVisibility(View.GONE);
        } else {
            llQualification.setVisibility(View.VISIBLE);
        }
        llQualification.setOnClickListener(v -> {
            ClipboardManager cm = (ClipboardManager) v.getContext().getSystemService(Context.CLIPBOARD_SERVICE);
            cm.setText(qualificationName + "\r\n备案号：" + qualificationCode);
            ToastUtils.showShort("已经复制");
        });
    }

    /**
     * 设置图片预览
     *
     * @param linkList
     */
    private void setImages(List<String> linkList) {
        if (linkList == null) return;
        View view = View.inflate(this, R.layout.layout_aptitude_xyy_pdf_vp, null);
        ViewPager vp = view.findViewById(R.id.vp);
        TextView tvCount = view.findViewById(R.id.tv_count);
        AptitudeXyyImageAdapter adapter = new AptitudeXyyImageAdapter(this, linkList);
        vp.setAdapter(adapter);
        mRootRl = findViewById(R.id.rl_root);
        mRootRl.addView(view, new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        tvCount.setText(1 + "/" + linkList.size());
        vp.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                tvCount.setText(position + 1 + "/" + linkList.size());
            }
        });
    }

    private void getAptitudeXyyPdfUrl() {//获取pdf合同的地址
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        if (!TextUtils.isEmpty(orgId)) {
            params.put("orgId", orgId);
        }
        if (shopCode != null) {
            params.put("shopCode", shopCode);
        }
        params.put("contractId", contractId);
        params.setUrl(links == null ? AppNetConfig.APTITUDE_XYY_PDF_DOWN_URL : AppNetConfig.APTITUDE_XYY_ZIP_POP_DOWN_URL);
        HttpManager.getInstance().post(params, new BaseResponse<AptitudePdfUrlBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudePdfUrlBean> baseBean, AptitudePdfUrlBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null) {
                        mFileUrl = data.url;
                        DownloadFile(mFileUrl);
                    }
                }
            }
        });
    }




    private void DownloadFile(String mFileUrl) {
        showProgress();
        mFileName = parseName(mFileUrl);

        AppUpdateManagerV2 updateManagerV2 = new AppUpdateManagerV2();
        updateManagerV2.setDownloadListener(new AppUpdateManagerV2.OnDownloadListener() {
            @Override
            public void onDownloadSuccess(File file) {
                dismissProgress();
                displayFile(file);
                shareFile = file;
            }

            @Override
            public void onDownloading(int progress) {

            }

            @Override
            public void onDownloadFailed(Exception e) {
                dismissProgress();
//                ToastUtils.showLongSafe("pdf文件下载失败");
                BugUtil.sendBug(new Throwable("pdf文件下载失败"));
            }
        });
        updateManagerV2.downFile(mFileUrl, mFileName);
    }

    @OnClick({R.id.iv_right, R.id.tv_download_to_email})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_right://分享到微信
                clickShareBtn(mOrderNo, mIsOrder);
                if (shareFile != null && shareFile.exists()) {
                    ShareUtil.sharePdfFileWechatFriend(getMySelf(), shareFile);
                    XyyIoUtil.track("action_order_contract_share");
                } else {
                    ToastUtils.showShort("分享的文件不存在");
                }
                break;
            case R.id.tv_download_to_email://下载到邮箱
                clickDownloadBtn(mOrderNo, mIsOrder);
                if (TextUtils.equals(mFileType, "1") || TextUtils.equals(mFileType, "11") || TextUtils.equals(mFileType, "12")) {
                  //1:购销合同 2:购物金代收款说明
                    RoutersUtils.open("ybmpage://aptitudexyyemail?fileType=" + mFileType + "&orderNo=" + mOrderNo);
                } else if (links == null) {
                    RoutersUtils.open("ybmpage://aptitudexyyemail?flag=" + FLAG_ONE + "&contractId=" + contractId);
                } else {
                    if (TextUtils.isEmpty(mFileUrl)) return;
                    RoutersUtils.open("ybmpage://aptitudexyyemail?zipurl=" + mFileUrl);
                }
                break;
        }
    }

    private String parseName(String url) {
        String fileName = null;
        try {
            if (url.contains("=")) {
                fileName = mTitle + url.substring(url.lastIndexOf("=") + 1);
            } else if (url.contains("/")) {
                fileName = mTitle + url.substring(url.lastIndexOf("/") + 1);
            }
        } catch (Exception e) {
            BugUtil.sendBug(e);
        } finally {
            if (TextUtils.isEmpty(fileName)) {
                fileName = mTitle + System.currentTimeMillis();
            }
        }
        if (!fileName.endsWith(".zip")) {
            if (TextUtils.equals(mFileType, "1")) {
                //购销合同
                return "购销合同_" + mOrderNo + ".pdf";
            }
            fileName = fileName + ".pdf";
        }
        if (fileName.contains("/")){
            fileName = fileName.replace("/","_");
        }
        return fileName;
    }


    private void displayFile(File file) {
        try {
            final Bundle bundle = new Bundle();
            bundle.putString("filePath", file.getAbsolutePath());
            bundle.putString("tempPath", FileUtil.getExternalFilePath());
            if (pdfView != null && !TextUtils.isEmpty(mFileName)) {
                pdfView.fromFile(file)
                        .enableSwipe(true)          // allows to block changing pages using swipe
                        .defaultPage(0)
                        .onError(t -> {
                            BugUtil.sendBug(new Throwable("userid = "+ SpUtil.getMerchantid() + ";error = "+t.toString() + ";pdfurl = "+mFileUrl));
//                        ToastUtils.showShort("文件加载错误");
                            tvTip.setVisibility(View.VISIBLE);
                        })
                        .enableAnnotationRendering(true) // render annotations (such as comments, colors or forms)
                        .load();
            }
        } catch (Exception e) {
            BugUtil.sendBug(e);
            Uri uri = Uri.parse(mFileUrl);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            startActivity(intent);
            finish();
        }
    }

    private String parseFormat(String fileName) {
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onCallBackAction(Integer integer, Object o, Object o1) {

    }
}

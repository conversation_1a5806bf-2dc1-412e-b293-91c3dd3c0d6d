package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.databinding.ItemAtmosphereHeadPictureBinding
import com.ybmmarket20.home.newpage.bean.AtmosphereImage
import com.ybmmarket20.home.newpage.bean.AtmosphereImageContentListBean
import com.ybmmarket20.xyyreport.SpmLogUtil

/**
 * @class   ConcatAtmosphereHeadPictureAdapter
 * <AUTHOR>
 * @date  2024/4/28
 * @description  氛围头图
 */
class ConcatAtmosphereHeadPictureAdapter : HomeComponentAnalysisAdapter<ConcatAtmosphereHeadPictureAdapter.AtmosphereHeadPictureVH>() {

	var mSuperData: AtmosphereImage? = null
		set(value) {
			field = value
			notifyDataSetChanged()
		}
	var mHomeJgspid: String? = null
	var mOnItemClickListener: ((AtmosphereImage?, AtmosphereImageContentListBean) -> Unit)? = null

	var navigation:String = ""

	private lateinit var mContext: Context

	//      value:当时埋点的时间戳
	private val resourceViewTrackMap = hashMapOf<String, Long>()

	companion object {
		private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
		private const val TRACK_RESOURCE_VIEW_KEY = "track_resource_view_key"
	}

	override fun onCreateViewHolder(
			parent: ViewGroup,
			viewType: Int
	): AtmosphereHeadPictureVH {
		mContext = parent.context
		return AtmosphereHeadPictureVH(DataBindingUtil.inflate(LayoutInflater.from(mContext), R.layout.item_atmosphere_head_picture, parent, false))
	}

	override fun getItemCount(): Int = mSuperData?.atmosphereImageContent?.atmosphereImageContentList?.let { if (it.size > 0) 1 else 0 } ?: 0

	override fun onBindViewHolder(
			holder: AtmosphereHeadPictureVH,
			position: Int
	) {
		onComponentExposure(mContext, mSuperData?.trackData, position) {
			SpmLogUtil.print("首页-组件-氛围头图曝光")
		}
		holder.mBinding.apply {
			mSuperData?.atmosphereImageContent?.atmosphereImageContentList?.let { data->
				val mData = if (data.size>0) data[0] else return@let
				mContext.glideLoadWithPlaceHolder(mData.imgUrl?:"",ivHead)
				root.setOnClickListener {
                    mOnItemClickListener?.invoke(mSuperData, mData)
					SpmLogUtil.print("首页-组件-氛围头图点击")
					onSubcomponentClick(mContext, mData.trackData)
                }

				resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY]?.let {
					if (System.currentTimeMillis() - it > TRACK_DURATION){
						resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
					}
				}?: kotlin.run {
					resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
				}
			}

		}
	}

	override fun onViewAttachedToWindow(holder: AtmosphereHeadPictureVH) {
		super.onViewAttachedToWindow(holder)
		val lp: ViewGroup.LayoutParams = holder.itemView.layoutParams
		if (lp is StaggeredGridLayoutManager.LayoutParams) {
			lp.isFullSpan = true
		}
	}

	class AtmosphereHeadPictureVH(val mBinding: ItemAtmosphereHeadPictureBinding) : RecyclerView.ViewHolder(mBinding.root)
}
package com.ybmmarket20.view

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.view.LayoutInflater
import android.view.View
import android.widget.*
import com.ybm.app.bean.NetError
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.*
import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject

/**
 * <AUTHOR>
 * 直播间下发优惠券弹框
 */

class VideoLiveCouponPopWindow {
    private lateinit var contentView: View
    private var popwindow: PopupWindow? = null
    private lateinit var tvPrice: TextView
    private lateinit var tvType: TextView
    private lateinit var tvUseRange: TextView
    private lateinit var tvPriceLimit: TextView
    private lateinit var tvGet: TextView
    private lateinit var rlCoupon: RelativeLayout
    private lateinit var ivIcon: ImageView
    private var ecLiveId: String? = ""
    private var couponMessageInfo: CouponInfoBean? = null
    private var rxTimer: RxTimer? = null

    init {
        initPop()
    }

    private fun initPop() {
        contentView = LayoutInflater.from(YBMAppLike.getApp().currActivity).inflate(R.layout.layout_tv_live_coupon_popwindow, null)
        tvPrice = contentView.findViewById<TextView>(R.id.tv_price)
        tvType = contentView.findViewById<TextView>(R.id.tv_type)
        tvUseRange = contentView.findViewById<TextView>(R.id.tv_use_range)
        tvPriceLimit = contentView.findViewById<TextView>(R.id.tv_price_limit)
        tvGet = contentView.findViewById<TextView>(R.id.tv_get)
        rlCoupon = contentView.findViewById<RelativeLayout>(R.id.rl_coupon)
        ivIcon = contentView.findViewById<ImageView>(R.id.iv_icon)
        popwindow = PopupWindow(this.contentView, LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT, true)
        popwindow?.setBackgroundDrawable(ColorDrawable(Color.parseColor("#00000000")))
        popwindow?.isFocusable = true
        popwindow?.isOutsideTouchable = true
        popwindow?.setOnDismissListener {
            // backgroundAlpha(1f)
        }
        // 只有可领取的券才展示领取按钮，其他情况都隐藏
        if (couponMessageInfo?.activityState == 2){
            tvGet.visibility = View.VISIBLE
        }else{
            tvGet.visibility = View.GONE
        }
        tvGet.setOnClickListener {
            if (couponMessageInfo?.activityState != 3 && couponMessageInfo?.activityState != 4 && couponMessageInfo?.activityState != 5) {
                getCoupon(couponMessageInfo?.templateId)
            }
        }
    }

    fun isShow(): Boolean {
        return popwindow?.isShowing ?: false
    }


    fun show(view: View) {
        if (view?.context == null) return

        if (popwindow == null) {
            initPop()
        }
        try {
            if (popwindow != null && popwindow!!.isShowing) {
                popwindow?.dismiss()
            }
        } catch (e: Exception) {
            return
        }

        try {
            popwindow?.showAsDropDown(view, 0, ConvertUtils.dp2px(10f))
            // 设置popWindow的显示和消失动画
            popwindow?.animationStyle = R.style.AnimCanter
        } catch (e: Exception) {
            return
        }

        //backgroundAlpha(0.3f)
        popwindow?.update()
    }

    fun dismiss() {
        if (popwindow != null) {
            try {
                // backgroundAlpha(1f)
                popwindow?.dismiss()
            } catch (e: Exception) {

            }

        }
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private fun backgroundAlpha(bgAlpha: Float) {
        val lp = BaseYBMApp.getApp().currActivity.window.attributes
        lp.alpha = bgAlpha //0.0-1.0
        BaseYBMApp.getApp().currActivity.window.attributes = lp
    }

    @SuppressLint("SetTextI18n")
    fun newData(couponMessageInfo: CouponInfoBean?, ecLiveId: String) {
        if (couponMessageInfo == null) {
            dismiss()
            return
        }
        this.couponMessageInfo = couponMessageInfo
        this.ecLiveId = ecLiveId

        var desc: SpannableStringBuilder
        if (couponMessageInfo.voucherState == 1) {
            val amount = UiUtils.transform2Int(couponMessageInfo.moneyInVoucher)
            desc = StringUtil.setDotAfterSize("${amount}折", 19)
        } else {
            desc = SpannableStringBuilder("¥${UiUtils.transformInt(couponMessageInfo.moneyInVoucher)}").apply {
                setSpan(AbsoluteSizeSpan(ConvertUtils.dp2px(15f)), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }
        tvPrice.text = desc

        if (UiUtils.transformInt(couponMessageInfo.moneyInVoucher).length > 3) {
            tvPrice.textSize = ConvertUtils.dp2px(10f).toFloat()
        }
        tvType.text = couponMessageInfo.voucherTypeDesc
        tvUseRange.text = couponMessageInfo.voucherTitle
        if (!TextUtils.isEmpty(couponMessageInfo.maxMoneyInVoucherDesc)) {
            tvPriceLimit.text = "${couponMessageInfo.minMoneyToEnableDesc} ${couponMessageInfo.maxMoneyInVoucherDesc}"
        } else {
            tvPriceLimit.text = couponMessageInfo.minMoneyToEnableDesc
        }

        // 只有可领取的券才展示领取按钮，其他情况都隐藏
        if (couponMessageInfo?.activityState == 2){
            tvGet.visibility = View.VISIBLE
        }else{
            tvGet.visibility = View.GONE
        }


        if (couponMessageInfo.activityState == 5) {//已抢光
            rlCoupon.setBackgroundResource(R.drawable.icon_tv_live_coupon_popwindow_no_bg)
            ivIcon.setImageResource(R.drawable.icon_tv_live_coupon_no)
        } else if (couponMessageInfo.activityState == 3 || couponMessageInfo.activityState == 4) {//已领取
            rlCoupon.setBackgroundResource(R.drawable.icon_tv_live_coupon_popwindow_have_yet_bg)
            ivIcon.setImageResource(R.drawable.icon_tv_live_coupon_have_yet)
        } else {//立即领取
            rlCoupon.setBackgroundResource(R.drawable.icon_tv_live_coupon_list_dialog_have_bg)
            ivIcon.setImageDrawable(null)
        }
        //展示时长
        rxTimer = RxTimer()
        rxTimer?.timer((couponMessageInfo.showtime * 1000).toLong()) {
            dismiss()
        }
    }

    private fun getCoupon(templateId: String?) {
        val merchantid = SpUtil.getMerchantid()
        val params = RequestParams()
        params.put("merchantId", merchantid)
        params.put("ecLiveId", ecLiveId)
        params.put("voucherTemplateId", templateId)
        params.url = AppNetConfig.TV_LIVE_GET_COUPON
        HttpManager.getInstance().post(params, object : BaseResponse<EmptyBean>() {

            override fun onSuccess(content: String, obj: BaseBean<EmptyBean>?, data: EmptyBean) {
                if (obj != null && obj.isSuccess) {
                    XyyIoUtil.track(XyyIoUtil.ACTION_WEBCAST_COUPON_GET, JSONObject().apply {
                        put("webcastId", ecLiveId)
                        put("couponId", templateId)
                        put("source", "2")  // 1.列表；2.卡片
                    })
                    DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, "领取成功")
                    dismiss()
                }
            }

            override fun onFailure(error: NetError) {
                ToastUtils.showShort(error.message)
            }
        })
    }

}
package com.ybmmarket20.view;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.CategoryLevel1AllPopWindowAdapter;
import com.ybmmarket20.bean.OneRowsBean;

import java.util.ArrayList;
import java.util.List;

public class CategoryLevel1AllPopWindow extends BaseFilterPopWindow {


    private RecyclerView rv;
    private ImageView iv_close;

    private CategoryLevel1AllPopWindowAdapter adapter;
    private List<OneRowsBean> list = new ArrayList<>();
    private int currentPositoin = 0;

    public void setSelectedPosition(int position) {
        currentPositoin = position;
        if (adapter != null) {
            adapter.setSelectedPosition(position);
        }
    }

    public void setNewData(List<OneRowsBean> list) {
        if (adapter != null) {
            adapter.setNewData(list);
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.layout_category_level1_all_popwindow;
    }

    @Override
    protected void initView() {
        rv = getView(R.id.rv);
        iv_close = getView(R.id.iv_close);

        rv.setLayoutManager(new GridLayoutManager(rv.getContext(), 4));
        adapter = new CategoryLevel1AllPopWindowAdapter(R.layout.item_category_level1_all_popwindow_1line, list);
        rv.setAdapter(adapter);

        setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));

        iv_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int i) {
                setSelectedPosition(i);
                if (listener != null) {
                    listener.onSelectedCallback(i);
                }
                dismiss();
            }
        });
    }

    private OnCallbackListener listener;
    public void setOnCallbackListener(OnCallbackListener listener){
        this.listener = listener;
    }
    public interface OnCallbackListener{
        void onSelectedCallback(int position);
    }
}

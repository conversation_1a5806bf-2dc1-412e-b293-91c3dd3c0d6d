package com.ybmmarket20.view;

import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout.LayoutParams;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.DebugAPIBean;
import com.ybmmarket20.common.YBMAppLike;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Iterator;

/**
 * 一个底部边出来的popwindows 窗口容器
 * 使用：new LeftPopWindow(R.layout.view).show
 */

public class DebugPopWindow {
	private final TextView tvResponse;
	private int height;
	private int width;
	private int x ;
	private PopupWindow popwindow;
	private View contentView ;
	private String str = "";
	private final LinearLayout ll_response;
	private final TextView tvUrl;
	private final TextView tvParam;
	private final Button btnCancel;

	public DebugPopWindow(){
		this.contentView = LayoutInflater.from(YBMAppLike.getAppContext()).inflate(R.layout.debug_api_pop, null);
		ll_response = (LinearLayout) contentView.findViewById(R.id.ll_response);
		btnCancel = (Button) contentView.findViewById(R.id.btn_cancel);
		btnCancel.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				dismiss();
			}
		});
		tvUrl = (TextView) contentView.findViewById(R.id.tv_url);
		tvParam = (TextView) contentView.findViewById(R.id.tv_param);
		tvResponse = (TextView) contentView.findViewById(R.id.tv_response);
		popwindow = new PopupWindow(this.contentView,
				LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT, true);
		initPop();
	}

	private void initView(DebugAPIBean bena) {
		JSONObject jsonObject = null;
		ll_response.removeAllViews();
		try {
			jsonObject = new JSONObject(bena.response);
		}catch (Exception e){
			jsonObject = new JSONObject();
			try {
				jsonObject.put("response", bena.response);
			}catch (Exception e1) {

			}
		}
		tvUrl.setText("地址: "+bena.url);
		tvParam.setText("参数："+bena.params);
		tvResponse.setText("返回结果：");
		pasteJsonObject(jsonObject,ll_response,true);
	}

	private void initPop(){
		popwindow.setAnimationStyle(R.style.mypopwindow_anim_style);
		popwindow.setFocusable(true);
		popwindow.setOutsideTouchable(false);
	}

	public void show(View token,DebugAPIBean bean) {
		if (popwindow == null) {
			return;
		}
		if(!this.str.equals(bean.response)){
			initView(bean);
			this.str = bean.response;
		}
		try {
			if (popwindow.isShowing()) {
				popwindow.dismiss();
			}
		}catch (Exception e){
			return;
		}
		try {
			popwindow.showAtLocation(token, Gravity.BOTTOM,  x, 0);
		}catch (Exception e){
			return;
		}
		popwindow.update();
	}

	public void dismiss(){
		if(popwindow !=null){
			try {
				popwindow.dismiss();
			}catch (Exception e){
			}
		}
	}


	public boolean isShow(){
		if(popwindow == null){
			return false;
		}
		return popwindow.isShowing();
	}


	private void pasteJsonObject(JSONObject jsonResponse, ViewGroup parent, boolean isVISIBLE){
		Iterator<String> iter = jsonResponse.keys();
		while (iter.hasNext()) {
			final String key = iter.next();
			try {
				Object value = jsonResponse.get(key);
				parent.addView(createView(value,key.toString() + " : ",parent,isVISIBLE));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private void pasteJsonArray(JSONArray jsonResponse, ViewGroup parent, boolean isVISIBLE){
		for (int i = 0; i< jsonResponse.length(); i++){
			try {
				Object value = jsonResponse.get(i);
				parent.addView(createView(value,"["+i+"]",parent,isVISIBLE));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private LinearLayout createView(Object value, String name, ViewGroup parent, boolean isVISIBLE ){
		LinearLayout cell = (LinearLayout) LayoutInflater.from(parent.getContext()).inflate(R.layout.debug_api_list_item,null);
		LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
		params.setMargins(30,0,0,0);
		cell.setLayoutParams(params);
		if(isVISIBLE){
			cell.setVisibility(View.VISIBLE);
		}else {
			cell.setVisibility(View.GONE);
		}
		TextView cellTitle = (TextView) cell.findViewById(R.id.name);
		TextView cellType = (TextView) cell.findViewById(R.id.type);
		TextView cellVaule = (TextView) cell.findViewById(R.id.vaule);
		cellTitle.setText(name);
		if (value == null || value.toString().equalsIgnoreCase("NULL")) {
			cellType.setText("Null");
			cellVaule.setText("null");
		} else if (value instanceof JSONArray) {
			cellType.setText("Array");
			cellVaule.setText("长度："+((JSONArray) value).length());
			cellTitle.setOnClickListener(new View.OnClickListener() {
				@Override
				public void onClick(View view) {
					ViewGroup viewGroup = (ViewGroup) view.getParent().getParent();
					boolean isOpen = (boolean) view.getTag();
					if (isOpen) {
						for(int a=1;a<viewGroup.getChildCount();a++){
							viewGroup.getChildAt(a).setVisibility(View.GONE);
						}
						((TextView) viewGroup.findViewById(R.id.status)).setText("+");
					} else {
						for(int a=1;a<viewGroup.getChildCount();a++){
							viewGroup.getChildAt(a).setVisibility(View.VISIBLE);
						}
						((TextView) viewGroup.findViewById(R.id.status)).setText("-");
					}
					view.setTag(!isOpen);
				}
			});
			cellTitle.setTag(false);
			((TextView) cell.findViewById(R.id.status)).setText("+");
			final JSONArray finalValue = (JSONArray) value;
			pasteJsonArray(finalValue, cell,false);
		} else if (value instanceof JSONObject) {
			cellType.setText("Object");
			cellVaule.setText("");
			cellTitle.setOnClickListener(new View.OnClickListener() {
				@Override
				public void onClick(View view) {
					ViewGroup viewGroup = (ViewGroup) view.getParent().getParent();
					boolean isOpen = (boolean) view.getTag();
					if (isOpen) {
						for(int a=1;a<viewGroup.getChildCount();a++){
							viewGroup.getChildAt(a).setVisibility(View.GONE);
						}
						((TextView) viewGroup.findViewById(R.id.status)).setText("+");
					} else {
						for(int a=1;a<viewGroup.getChildCount();a++){
							viewGroup.getChildAt(a).setVisibility(View.VISIBLE);
						}
						((TextView) viewGroup.findViewById(R.id.status)).setText("-");
					}
					view.setTag(!isOpen);
				}
			});
			cellTitle.setTag(false);
			((TextView) cell.findViewById(R.id.status)).setText("+");
			final JSONObject finalValue = (JSONObject) value;
			pasteJsonObject(finalValue, cell,false);
		} else if (value instanceof String) {
			value = value.toString();
			cellType.setText("String");
			cellVaule.setText(value + "");
		} else if (value instanceof Boolean) {
			value = value.toString();
			cellType.setText("Boolean");
			cellVaule.setText(value + "");
		} else if (value instanceof Integer) {
			value = value.toString();
			cellType.setText("Integer");
			cellVaule.setText(value + "");
		} else if (value instanceof Long) {
			value = value.toString();
			cellType.setText("Long");
			cellVaule.setText(value + "");
		} else if (value instanceof Double) {
			value = value.toString();
			cellType.setText("Double");
			cellVaule.setText(value + "");
		}else {
			value = value.toString();
			cellType.setText("其它");
			cellVaule.setText(value + "");
		}

		return cell;
	}

}

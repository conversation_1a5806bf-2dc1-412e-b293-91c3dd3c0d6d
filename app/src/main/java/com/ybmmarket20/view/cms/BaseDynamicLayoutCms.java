package com.ybmmarket20.view.cms;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ModuleViewItem;
import com.ybmmarket20.bean.SeckillGroupBean;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.bean.cms.ModuleContent;
import com.ybmmarket20.bean.cms.ModuleStyles;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.view.IBaseDynamicIntercept;
import com.ybmmarket20.view.IBaseDynamicLayout;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 动态布局通用功能实名
 * 功能需求：
 * 1，动态标题文案，跳转 ，图标，背景，高度，内边距，获取数据
 * 2, 生命周期调用
 */
public abstract class BaseDynamicLayoutCms<T> extends LinearLayout implements IBaseDynamicLayout {
    public TextView tvTitle;
    protected ItemClick itemClick = new ItemClick();
    public ModuleBeanCms<T> moduleBean;
    public ModuleContent content;
    public ModuleStyles styles;
    private boolean isRetry = false;
    private UpdateListener updateListener;
    private int position;
    public static int defBgColor = R.color.white;
    public static int deftvColor = R.color.transparent;
    public int defHeigth;
    public int defTitleHeigth = 30;
    private String imgHeader = AppNetConfig.CDN_HOST;
    private String apiHeader = AppNetConfig.HOST;
    private int retry = 0;
    private List<IBaseDynamicIntercept> intercepts = new ArrayList<>();

    public interface UpdateListener {
        void onUpdate(ModuleBeanCms view, int position);
    }

    public BaseDynamicLayoutCms(Context context) {
        this(context, null);
    }

    public BaseDynamicLayoutCms(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BaseDynamicLayoutCms(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setOrientation(VERTICAL);//
        this.setVisibility(View.GONE);
        views(!inflateAfterSetStyle(), getLayoutId());
    }

    private void views(boolean init, int layoutId) {//默认加载布局
        if (init) {
            if (layoutId <= 0) {
                layoutId = R.layout.base_dynamic_layout_view;
            }
            View.inflate(getContext(), layoutId, this);
            try {
                tvTitle = (TextView) findViewById(R.id.tv_title);
            } catch (Exception e) {
                tvTitle = null;
            }
            initViews();
        }
    }

    //加载固定布局
    public abstract void initViews();

    //加载固定布局
    public abstract boolean supportSetHei();

    //加载固定布局
    public abstract int getLayoutId();

    // 加载或者更新布局
    // isUpdate = ture  需要更新布局
    // isUpdate = false 不需更新布局
    public abstract void setItemData(ModuleBeanCms moduleBean, List<T> items, boolean isUpdate);

    /**
     * 判断模块子项数据是否更新,每个模块自己决定是否重写，这里只判断个数
     * 此方法只在初始化后更新数据时调用，初始化时不会调此方法
     *
     * @param moduleBean
     * @return
     */
    public boolean needUpdateItem(ModuleBeanCms<T> moduleBean, List<T> items){
       boolean keepOld = false;
        if (this.content!=null && moduleBean.content!=null && this.content.list!=null && moduleBean.content.list!=null && this.content.list.size() == moduleBean.content.list.size()){
            for (int a = 0; a< content.list.size();a++){
                if (!this.moduleBean.content.list.get(a).equals(moduleBean.content.list.get(a))) {
                    keepOld = false;
                    break;
                }
            }
        }else {
            keepOld = false;
        }

        return !keepOld;
    }

    //设置style
    public abstract void setStyle(int style);

    //设置图片
    public abstract void setImageView(ImageView view, T bean);

    /**
     * 开始就加载布局还是设置style后加载布局 false 加载值 直接加载布局
     *
     * @return
     */
    public boolean inflateAfterSetStyle() {
        return false;
    }

    public int getDefHeigth() {

//        SECKILL = "seckill";//秒杀

        switch (moduleBean.name) {
            // 图片模块
            case ModuleBeanCms.ADONE:
            case ModuleBeanCms.ADTWO:
            case ModuleBeanCms.BRAND_H:
            case ModuleBeanCms.STREAMER:
                defHeigth = 90;
                break;
            case ModuleBeanCms.ADTHREE:
                defHeigth = 195;
                break;
            case ModuleBeanCms.ADFOUR:
                defHeigth = 100;
                break;
            case ModuleBeanCms.ONEPLUSTWO:
            case ModuleBeanCms.TWOPLUSTWO:
                defHeigth = 185;
                break;
            case ModuleBeanCms.ONEPLUSTHREE:
            case ModuleBeanCms.ONEPLUSFOUR:
                defHeigth = 205;
                break;
            case ModuleBeanCms.MOREACTIVE:
            case ModuleBeanCms.WONDERACTIVE:
            case ModuleBeanCms.HEADLINE:
                defHeigth = 44;
                break;
        }
        return defHeigth;
    }

    public int getDefTitleHeigth() {
        return defTitleHeigth;
    }

    public int getDefBg() {
        return defBgColor;
    }

    public int getDefTitleBg() {
        return deftvColor;
    }

    //重新加载布局
    public void reInflate(int layoutId) {
        try {
            this.removeAllViews();
        } catch (Throwable e) {
            e.printStackTrace();
        }
        views(true, layoutId);
    }

    /**
     * @param moduleBean 数据
     */
    public final void bindData(ModuleBeanCms<T> moduleBean) {
        this.moduleBean = moduleBean;
        this.content = moduleBean.content;
        this.styles = moduleBean.styles;
        setStyle(32);//设置样式

        //背景
        setModuleBackgroundAndPadding(moduleBean);
        setTitle(moduleBean);
        setHeightAndMargin(moduleBean);
        if (moduleBean.name.equals(ModuleBeanCms.SECKILL)) {
            if (content.data != null && !TextUtils.isEmpty(content.data.api) && !content.data.api.equals(apiHeader)) {
                getItemData(content.data.api, moduleBean.name);
            }
        } else {
            if (moduleBean.name.equals(ModuleBeanCms.FLOORSPACING)
                    || moduleBean.name.equals(ModuleBeanCms.MOREACTIVE)
                    || moduleBean.name.equals(ModuleBeanCms.WONDERACTIVE)
                    || (moduleBean.content.list != null && moduleBean.content.list.size() > 0)) {
                bindItemData(moduleBean, moduleBean.content.list, true);
            }
        }
    }

    /**
     * 设置高度和margin
     *
     * @param moduleBean
     */
    public void setHeightAndMargin(ModuleBeanCms<T> moduleBean) {
        if (ModuleBeanCms.SEARCHBOX.equals(moduleBean.name)) {
            return;
        }
        if (supportSetHei()) {
            if (moduleBean.styles != null) {
                int hei = moduleBean.styles.height;
                if (hei <= 0) {
                    hei = getDefHeigth();
                }
                LayoutParams params = (LayoutParams) this.getLayoutParams();
                if (params == null) {
                    params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(hei));
                }
                params.height = dp2px(hei);
                if (moduleBean.styles.margin != null && moduleBean.styles.margin.size() >= 4) {
                    params.setMargins(dp2px(moduleBean.styles.margin.get(3)), dp2px(moduleBean.styles.margin.get(0)), dp2px(moduleBean.styles.margin.get(1)), dp2px(moduleBean.styles.margin.get(2)));
                } else {
                    params.setMargins(0, 0, 0, 0);
                }
                this.setLayoutParams(params);
            } else {
                LayoutParams params = (LayoutParams) this.getLayoutParams();
                if (params == null) {
                    params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(getDefHeigth()));
                }
                params.height = dp2px(getDefHeigth());
                this.setLayoutParams(params);
            }

        } else {
            LayoutParams params = (LayoutParams) this.getLayoutParams();
            if (params == null) {
                params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            }
            if (moduleBean.styles != null) {
                if (moduleBean.styles.margin != null && moduleBean.styles.margin.size() >= 4) {
                    params.setMargins(dp2px(moduleBean.styles.margin.get(3)), dp2px(moduleBean.styles.margin.get(0)), dp2px(moduleBean.styles.margin.get(1)), dp2px(moduleBean.styles.margin.get(2)));
                } else {
                    params.setMargins(0, 0, 0, 0);
                }
            }

            this.setLayoutParams(params);
        }
    }

    // 设置title
    private void setTitle(ModuleBeanCms<T> moduleBean) {
        if (tvTitle != null && (!TextUtils.isEmpty(moduleBean.titleText) || !TextUtils.isEmpty(moduleBean.titleRes))) {
        } else {
            if (tvTitle != null) {
                tvTitle.setVisibility(View.GONE);
                tvTitle.setOnClickListener(null);
            }
            this.setTag(R.id.tag_action, content.action);
            this.setOnClickListener(itemClick);
        }
    }

    /**
     * 设置模块的背景，padding
     */
    public void setModuleBackgroundAndPadding(ModuleBeanCms<T> moduleBean) {
        String backgroundRes = null;
        if (!TextUtils.isEmpty(moduleBean.content.bgRes)) {
            backgroundRes = moduleBean.content.bgRes;
        }

        // padding
        if (moduleBean.styles == null || moduleBean.styles.padding == null || moduleBean.styles.padding.size() < 4) {
            setNetBackground(backgroundRes, 0, 0, 0, 0);
        } else {
            setNetBackground(backgroundRes, moduleBean.styles.padding.get(3), moduleBean.styles.padding.get(0), moduleBean.styles.padding.get(1), moduleBean.styles.padding.get(2));
        }
    }

    /**
     * 相同的模块更新
     * 先更新样式
     * 再根据数据决定是否更新数据
     *
     * @param moduleBean
     */
    public void updateExistedModuleStyle(ModuleBeanCms moduleBean) {
        setModuleBackgroundAndPadding(moduleBean);
        setTitle(moduleBean);
        setHeightAndMargin(moduleBean);
        updateData(moduleBean);
        updateModuleItemStyle();
    }

    // 模块的margin padding 对模块内部item大小产生影响，需要具体的moudle继承并实现
    public void updateModuleItemStyle() {
    }

    // 更新数据，不用更新背景标题等等样式
    public void updateData(ModuleBeanCms moduleBean) {

        setHeightAndMargin(moduleBean);
        setModuleBackgroundAndPadding(moduleBean);
        if (moduleBean.name.equals(ModuleBeanCms.SECKILL) && content.data != null && !TextUtils.isEmpty(content.data.api) && !content.data.api.equals(apiHeader)) {
            getItemData(content.data.api, moduleBean.name);
        } else {//数据直接下载下来的
            bindItemData(moduleBean, moduleBean.content.list, needUpdateItem(moduleBean, moduleBean.content.list));
        }

        this.moduleBean = moduleBean;
        this.content = moduleBean.content;
        this.styles = moduleBean.styles;
    }


    // 拦截一层控制是否显示, 只有秒杀模块才会这么弄，以后逐步去掉
    public void bindItemData(List<T> items, boolean update) {
        if (!moduleBean.name.equals(ModuleBeanCms.FLOORSPACING)
                && !moduleBean.name.equals(ModuleBeanCms.SEARCHBOX)
                && !moduleBean.name.equals(ModuleBeanCms.MOREACTIVE)
                && !moduleBean.name.equals(ModuleBeanCms.WONDERACTIVE)
                && (items == null || items.size() <= 0)) {
            this.setVisibility(View.GONE);
            return;
        } else {
            setVisibility(VISIBLE);
        }
        retry = 0;
        setItemData(null, items, update);
    }

    // 拦截一层控制模块是否显示
    public void bindItemData(ModuleBeanCms moduleBean, List<T> items, boolean update) {
        if (!moduleBean.name.equals(ModuleBeanCms.FLOORSPACING)
                && !moduleBean.name.equals(ModuleBeanCms.SEARCHBOX)
                && !moduleBean.name.equals(ModuleBeanCms.MOREACTIVE)
                && !moduleBean.name.equals(ModuleBeanCms.WONDERACTIVE)
                && (items == null || items.size() <= 0)) {
            this.setVisibility(View.GONE);
            return;
        } else {
            setVisibility(VISIBLE);
        }
        retry = 0;
        setItemData(moduleBean, items, update);
    }


    //获取数据,成功后设置数据
    public void getItemData(final String api, final String name) {
        BaseResponse responese = null;
        responese = new BaseResponse<SeckillGroupBean>() {
            @Override
            public void onSuccess(String content, BaseBean<SeckillGroupBean> data, SeckillGroupBean obj) {
                if (data != null && data.isSuccess() && obj != null && obj.details != null) {
                    moduleBean.content.list = (List<T>) obj.details;
                    if (updateListener != null) {//数据更新完成了
                        updateListener.onUpdate(moduleBean, position);
                    }
                    bindItemData(moduleBean.content.list, true);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                if (error != null && error.errorCode == NetError.TIME_OUT_ERROR && !isRetry) {
                    getItemData(api, name);
                    isRetry = true;
                }
            }
        };
        HttpManager.getInstance().postParser(apiHeader + api, responese);
    }

//    private void updateSize() {
//        this.post(new Runnable() {
//            @Override
//            public void run() {
//                updateBg();
//            }
//        });
//    }

//    public void updateBg() {
//        if (!TextUtils.isEmpty(content.bgRes)) {
//            setNetBackground(this, content.bgRes);
//        }
//        if (!TextUtils.isEmpty(moduleBean.titleRes) && tvTitle != null) {
//            setNetBackground(tvTitle, moduleBean.titleRes);
//        }
//    }

    //通用点击跳转
    public class ItemClick implements OnClickListener {
        @Override
        public void onClick(final View v) {
            v.setEnabled(false);
            String action = (String) v.getTag(R.id.tag_action);
            String click_type = (String) v.getTag(R.id.tag_click_type);
            String banner_type= (String) v.getTag(R.id.tag_banner_type);
            // cms 的item点击埋点
            if (XyyIoUtil.ACTION_HOME_BANNER.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_SHORTCUT.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGE.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGE_2_LEFTONERIGHTONE.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGE_3_LEFTONERIGHTTWO_UD.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGE_3_LR.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGE_4_LR.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGE_4_LEFTONERIGHTTHREE.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGE_5_LEFTONERIGHTFOUR.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGE_4_SQUARE.equals(click_type)


                    || XyyIoUtil.ACTION_HOME_MAIN_AD.equals(click_type)

            ) {
                int index = (int) v.getTag(R.id.tag_2);
                JSONObject jsonObject = new JSONObject();
                try {
                    if (XyyIoUtil.ACTION_HOME_SHORTCUT.equals(click_type)) {
                        jsonObject.put("title", v.getTag(R.id.tag_title));
                    }

                    jsonObject.put("action", action);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                XyyIoUtil.track(click_type + ++index, jsonObject);
            }

            // 秒杀头部点击点击埋点
            // 集合推荐头部埋点
            // 图片标题
            // 文字标题
            if (XyyIoUtil.ACTION_HOME_SECKILL.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_COLLECTION_PRODUCT_TITLE_IMAGE.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_IMAGETITLE.equals(click_type)
                    || XyyIoUtil.ACTION_HOME_TEXTTITLE.equals(click_type)
            ) {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("action", action);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                XyyIoUtil.track(click_type, jsonObject);
            }

            click(action,banner_type);
            v.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (v != null) {
                        v.setEnabled(true);
                    }
                }
            }, 500);
        }
    }

    //执行点击
    public void click(String action,String bannerType) {
        if (TextUtils.isEmpty(action)) {
            return;
        }
        if (intercepts == null || intercepts.isEmpty()) {
            handleRoutersJump(action,bannerType);
        } else {
            Uri uri = Uri.parse(action);
            boolean isIntercept = false;
            for (IBaseDynamicIntercept intercept : intercepts) {
                if (intercept != null && intercept.onInterceptAction(uri)) {
                    isIntercept = true;
                    break;
                }
            }
            if (!isIntercept) {
                handleRoutersJump(action,bannerType);
            }
        }
    }

    //处理bannerType跳转 默认inLink内部 outLink外部
    private void handleRoutersJump(String action,String bannerType){
        if (TextUtils.isEmpty(bannerType)){
            RoutersUtils.open(action);
        }else {
            if ("inLink".equals(bannerType)){//内部跳转 走原有路由逻辑
                RoutersUtils.open(action);
            }else if ("outLink".equals(bannerType)){//外部跳转 调起手机浏览器访问
                RoutersUtils.openOutBrowser(getContext(),action);
            }
        }
    }

    private void setNetBackground(View view, Bitmap bitmap) {
        if (bitmap == null) {
            if (view instanceof TextView) {
                if (getDefTitleBg() > 0) {
                    view.setBackgroundResource(getDefTitleBg());
                } else {
                    view.setBackgroundDrawable(null);
                }
            } else {
                if (getDefBg() > 0) {
                    view.setBackgroundResource(getDefBg());
                } else {
                    view.setBackgroundDrawable(null);
                }
            }
        } else {
            view.setBackgroundDrawable(new BitmapDrawable(bitmap));
        }
    }

    public void setNetBackground(final String url) {
        setNetBackground(this, url);
    }

    //支持url的图片，可以#789877 的颜色
    protected void setNetBackground(final View view, String url) {
        if (view == null) {
            return;
        }
        if (TextUtils.isEmpty(url)) {
            setNetBackground(view, (Bitmap) null);
        } else {
            if (url.startsWith("#")) {//设置背景色
                int color = getColor(url);
                if (color != 0) {
                    view.setBackground(new ColorDrawable(color));
                } else {
                    setNetBackground(view, (Bitmap) null);
                }
            } else {
                try {
                    if (!url.startsWith("http")) {
                        url = imgHeader + url;
                    }
                    ImageHelper.with(getContext())
                            .load(url)
                            .asBitmap()
                            .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                            .into(new SimpleTarget<Bitmap>() {
                                @Override
                                public void onResourceReady(Bitmap resource, GlideAnimation<? super Bitmap> glideAnimation) {
                                    BitmapDrawable bitmapDrawable = new BitmapDrawable(resource);
                                    bitmapDrawable.setBounds(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
                                    view.setBackground(bitmapDrawable);
                                }
                            });
                } catch (Throwable e) {
                    BugUtil.sendBug(new Throwable("背景图设置失败"));
                }
            }
        }
    }

    public void downLoadImage(ModuleViewItem bean) {
        try {
            if (TextUtils.isEmpty(bean.imgUrl)) {
                return;
            } else {
                ImageHelper.with(getContext()).load(getImgUrl(bean.imgUrl)).diskCacheStrategy(DiskCacheStrategy.SOURCE).preload();
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public String getImgUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        if (url.startsWith("http") || url.startsWith("Http")) {
            return url;
        } else {
            return imgHeader + url;
        }
    }

    public void setNetBackground(final String url, int paddingLeft, int paddingTop, int paddingRigth, int paddingBottom) {
        setPadding(dp2px(paddingLeft), dp2px(paddingTop), dp2px(paddingRigth), dp2px(paddingBottom));
        setNetBackground(url);
    }

    protected int getColor(String color) {
        try {
            return Color.parseColor(color);
        } catch (Exception e) {
            return Color.parseColor("#ffffff");
        }
    }

    protected void setUpdateListener(int position, UpdateListener listener) {
        this.position = position;
        updateListener = listener;
    }

    //可以适配多手机
    public int dp2px(int dp) {
        if (dp == 0) {
            return 0;
        }
        return ConvertUtils.dp2px(dp);
    }

    public List<IBaseDynamicIntercept> getIntercepts() {
        return intercepts;
    }

    public void setIntercepts(List<IBaseDynamicIntercept> intercepts) {
        this.intercepts = intercepts;
    }

    //生命周期的调用
    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onRefresh() {

    }

    @Override
    public void onDestroy() {

    }

}

package com.ybmmarket20.view.cms;

import android.content.Context;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.ybmmarket20.R;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.adapter.ProductMultiAdapter;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.RoutersUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * 单品推荐 productExhibition
 */
public class DynamicProductMultiLayoutCms extends BaseDynamicLayoutCms<RowsBean> {

    private RecyclerView rcView;
    private ProductMultiAdapter adapter;
    private GridLayoutManager layoutManager;

    private int style;
    private boolean isWhiteBg;

    public DynamicProductMultiLayoutCms(Context context) {
        super(context);
    }

    public DynamicProductMultiLayoutCms(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicProductMultiLayoutCms(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        rcView = (RecyclerView) findViewById(R.id.rv_list);
        rcView.setNestedScrollingEnabled(false);
    }

    @Override
    public boolean supportSetHei() {
        return false;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_product_multi;
    }

    @Override
    public void setItemData(ModuleBeanCms moduleBean, List<RowsBean> items, boolean isUpdate) {
        if (items == null || items.size() <= 0) {
            return;
        }

        int itemNum = items.size() > 4 ? 4 : items.size();
        for (int i = 0; i < itemNum; i++) {
            if (i == 0) {
                RowsBean rowsBean = items.get(i);
                rowsBean.setItemType(RowsBean.content_11);
                rowsBean.spanSize = RowsBean.CONTENT_11_SPAN_SIZE;
            } else {
                RowsBean rowsBean = items.get(i);
                rowsBean.setItemType(RowsBean.content_31);
                rowsBean.spanSize = RowsBean.CONTENT_31_TEXT_SPAN_SIZE;
            }
        }
        try {
            adapter = new ProductMultiAdapter(items.subList(0, itemNum));
            adapter.setEnableLoadMore(false);
            layoutManager = new GridLayoutManager(getContext(), 3);
            adapter.setOnItemClickListener(new ProductMultiAdapter.OnListViewItemClickListener() {
                @Override
                public void onItemClick(RowsBean rows) {

                    // cms 集合推荐埋点
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put("id", rows.getId());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    XyyIoUtil.track(XyyIoUtil.ACTION_HOME_ITEMSHOW_PRODUCT, jsonObject, rows);
                    RoutersUtils.open("ybmpage://productdetail/" + rows.getId());
                }
            });
            adapter.setSpanSizeLookup(new ProductMultiAdapter.SpanSizeLookup() {
                @Override
                public int getSpanSize(GridLayoutManager gridLayoutManager, int position) {
                    return items.get(position).spanSize;
                }
            });

            rcView.setLayoutManager(layoutManager);
            rcView.setAdapter(adapter);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void setStyle(int style) {

        if (style <= 0 && getContext() != null && getContext() instanceof MainActivity) {
            style = 32;
        }
        if (this.style == style && adapter != null && isWhiteBg == isWhiteBg()) {
            return;
        }
        this.style = style;

    }

    @Override
    public void setImageView(ImageView view, RowsBean bean) {

    }

    @Override
    public boolean needUpdateItem(ModuleBeanCms<RowsBean> moduleBean, List<RowsBean> items) {
        return true;
    }

    private boolean isWhiteBg() {
        if (TextUtils.isEmpty(content.bgRes) || "#ffffff".equals(content.bgRes.toLowerCase())) {
            return false;
        }
        return true;
    }

    @Override
    public void onRefresh() {
        super.onRefresh();
        if (adapter != null && adapter.getData() != null && !adapter.getData().isEmpty()) {
            if (adapter.getCurrPosition() > 0 && adapter.getCurrPosition() < adapter.getData().size()) {
                adapter.notifyItemChanged(adapter.getCurrPosition());
            }
        }
    }
}

package com.ybmmarket20.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.utils.DrawableUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 全部药品筛选-一级分类
 */

public class FiltrateClassifyPop {

    private LeftPopWindow mLeftPopWindow;
    private ManufacturersPop mManufacturersPop;
    private LeftPopWindow.Listener<SearchFilterBean> listener;

    private TextView mTvDetails01, mTvDetails02, mTvDetails03, tvAllManufacture;
    private LinearLayout mLl01, mLl02, mLl03, mLl04;
    private EditText mPriceRangeFloor, mPriceRangeTop;
    private TextView mTvAvailable, mTvPromotion, mTvPriceRangeFloor, mTvPriceRangeTop;

    private boolean isAvailable = false, isPromotion = false
            , isClassA = false, isClassB = false
            , isClassRx = false, isClassElse = false;
    private boolean isShunfeng = false;
    private boolean isJD = false;
    private String key = "";
    private String value = "";
    protected String lastName = "全部厂家";
    private List<String> mList = new ArrayList<>();
    private TextView mClassA;
    private TextView mClassB;
    private TextView mClassRx;
    private TextView mClassElse;
    private int minSlop;
    private int lastAction;
    private float lastY;
    private InputMethodManager inputManager;
    private TextView tvYBM;
    private LinearLayout llYBM;
    private boolean isPlan;
    private String planId;
    private TextView tv_price_range;
    private LinearLayout ll_price_range;
    private LinearLayout llManufactureSelected;

    private  View mShopServiceTitle;
    private  View mShopServiceOptions;
    private  TextView mShopServiceShunfeng;
    private  TextView mShopServiceJd;

    // 商品规格,多个以逗号','分割
    private String spec;
    // 店铺code 多个以逗号分隔
    private String shopCodes;

    private RelativeLayout rlAllManufacture;

    //规格
    private List<SearchFilterBean> specficationBeans;
    //店铺
    private List<SearchFilterBean> shopStoreDatas;

    /**
     * 隐藏店铺服务
     * @param isHidden
     */
    public void hiddenShopService(boolean isHidden) {
        if (mLeftPopWindow != null) {
            mLeftPopWindow.hiddenShopService(isHidden);
        }
    }

    /**
     * 仅看有货
     */
    public void setAvailable(boolean isAvailable) {
        this.isAvailable = isAvailable;
        handler.sendMessage(handler.obtainMessage(20));
    }

    public void setExpressAll(boolean isExpress) {
        isJD = isExpress;
        isShunfeng = isExpress;
        mShopServiceShunfeng.setActivated(isExpress);
        mShopServiceJd.setActivated(isExpress);
        handler.sendMessage(handler.obtainMessage(80));
    }

    /**
     * 仅看有货
     */
    public void setPromotion(boolean isPromotion) {
        this.isPromotion = isPromotion;
        handler.sendMessage(handler.obtainMessage(30));
    }

    /*
     * 厂家
     * */
    public void setLastNames(List<String> lastNames) {
        handler.sendMessage(handler.obtainMessage(10, lastNames));
    }

    public void setDataType(String key, String value) {
        if (this.key.equals(key) && this.value.equals(value)) {
            return;
        } else {
            this.key = key;
            this.value = value;
        }
    }

    // 商品规格,多个以逗号','分割
    public void setSpec(String spec) {
        this.spec = spec;
    }

    // 店铺code 多个以逗号分隔
    public void setSelectedShopCodes(String shopCodes) {
        this.shopCodes = shopCodes;
    }

    public FiltrateClassifyPop() {
        init();
    }

    public FiltrateClassifyPop(String planId) {
        init();
        this.planId = planId;
        isPlan = true;
    }

    /**
     * 从商品搜索过来，需要隐藏（全部生产厂家）
     */
    public void hideManufactureTips() {
        if (tvAllManufacture != null) {
            rlAllManufacture.setVisibility(View.GONE);
        }
    }

    /**
     * 初始化
     */
    private void init() {

        mLeftPopWindow = new LeftPopWindow(R.layout.pop_filtrate_classify) {

            @Override
            protected void initView(View contentView) {
                tvYBM = contentView.findViewById(R.id.tv_ybm);
                llYBM = contentView.findViewById(R.id.ll_ybm);
                ImageView ivBack = (ImageView) contentView.findViewById(R.id.iv_back);
                TextView tvTitle =
                        (TextView) contentView.findViewById(R.id.tv_title);
                Button btnReset = (Button) contentView.findViewById(R.id.btn_reset);
                Button btnAffirm = (Button) contentView.findViewById(R.id.btn_affirm);
                rlAllManufacture = (RelativeLayout) contentView.findViewById(R.id.rl_all_manufacture);

                mTvDetails01 = (TextView) contentView.findViewById(R.id.tv_details_01);
                mTvDetails02 = (TextView) contentView.findViewById(R.id.tv_details_02);
                mTvDetails03 = (TextView) contentView.findViewById(R.id.tv_details_03);
                tvAllManufacture = contentView.findViewById(R.id.tv_all_manufacture);
                mLl01 = (LinearLayout) contentView.findViewById(R.id.ll_01);
                mLl02 = (LinearLayout) contentView.findViewById(R.id.ll_02);
                mLl03 = (LinearLayout) contentView.findViewById(R.id.ll_03);
                mLl04 = (LinearLayout) contentView.findViewById(R.id.ll_04);

                mTvAvailable = (TextView) contentView.findViewById(R.id.tv_available);
                mTvPromotion = (TextView) contentView.findViewById(R.id.tv_promotion);

                mPriceRangeFloor = (EditText) contentView.findViewById(R.id.price_range_floor);
                mPriceRangeTop = (EditText) contentView.findViewById(R.id.price_range_top);

                mTvPriceRangeFloor = (TextView) contentView.findViewById(R.id.tv_price_range_floor);
                mTvPriceRangeTop = (TextView) contentView.findViewById(R.id.tv_price_range_top);
                tv_price_range = contentView.findViewById(R.id.tv_price_range);
                ll_price_range = contentView.findViewById(R.id.ll_price_range);
                llManufactureSelected = contentView.findViewById(R.id.ll_manufacture_selected);

                mShopServiceTitle = contentView.findViewById(R.id.shop_service_title);
                mShopServiceOptions = contentView.findViewById(R.id.shop_service_options);
                mShopServiceShunfeng = contentView.findViewById(R.id.express_shunfeng);
                mShopServiceJd = contentView.findViewById(R.id.express_jd);


                mShopServiceShunfeng.setOnClickListener(view -> {
                    boolean activated = view.isActivated();
                    isShunfeng = !activated;
                    view.setActivated(!activated);
                });
                mShopServiceJd.setOnClickListener(view -> {
                    boolean activated = view.isActivated();
                    isJD = !activated;
                    view.setActivated(!activated);
                });

                mPriceRangeFloor.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                        if (TextUtils.isEmpty(charSequence)) {
                            mTvPriceRangeFloor.setVisibility(View.VISIBLE);
                        } else {
                            mTvPriceRangeFloor.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void afterTextChanged(Editable editable) {
                    }
                });
                mPriceRangeTop.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                        if (TextUtils.isEmpty(charSequence)) {
                            mTvPriceRangeTop.setVisibility(View.VISIBLE);
                        } else {
                            mTvPriceRangeTop.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void afterTextChanged(Editable editable) {
                    }
                });

                mClassA = (TextView) contentView.findViewById(R.id.class_a);
                mClassB = (TextView) contentView.findViewById(R.id.class_b);
                mClassRx = (TextView) contentView.findViewById(R.id.class_rx);
                mClassElse = (TextView) contentView.findViewById(R.id.class_else);

                tvTitle.setText("筛选");
                ivBack.setImageResource(R.drawable.icon_close);

                new DrawableUtil(mTvDetails01, new DrawableUtil.OnDrawableListener() {
                    @Override
                    public void onLeft(View v, Drawable left) {

                    }

                    @Override
                    public void onRight(View v, Drawable right) {
                        mLl01.setVisibility(View.GONE);
                        mLl04.setVisibility(View.GONE);
                        if (mTvDetails01 != null && mTvDetails01.getText() != null && mManufacturersPop != null) {
                            mManufacturersPop.removeKey(mTvDetails01.getText().toString());
                            mList.remove(mTvDetails01.getText().toString());
                            mTvDetails01.setText("");
                        }
                    }
                });

                new DrawableUtil(mTvDetails02, new DrawableUtil.OnDrawableListener() {
                    @Override
                    public void onLeft(View v, Drawable left) {

                    }

                    @Override
                    public void onRight(View v, Drawable right) {
                        mLl02.setVisibility(View.GONE);
                        mLl04.setVisibility(View.GONE);
                        if (mTvDetails02 != null) {
                            mManufacturersPop.removeKey(mTvDetails02.getText().toString());
                            mList.remove(mTvDetails02.getText().toString());
                            mTvDetails02.setText("");
                        }
                    }
                });
                new DrawableUtil(mTvDetails03, new DrawableUtil.OnDrawableListener() {
                    @Override
                    public void onLeft(View v, Drawable left) {

                    }

                    @Override
                    public void onRight(View v, Drawable right) {
                        mLl03.setVisibility(View.GONE);
                        mLl04.setVisibility(View.GONE);
                        if (mTvDetails03 != null) {
                            mManufacturersPop.removeKey(mTvDetails03.getText().toString());
                            mList.remove(mTvDetails03.getText().toString());
                            mTvDetails03.setText("");
                        }
                    }
                });

                //顶部title高度

                //仅看有货
                mTvAvailable.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isAvailable = !activated;
                    }
                });

                //有促销
                mTvPromotion.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isPromotion = !activated;
                    }
                });
                //甲类otc
                mClassA.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isClassA = !activated;
                    }
                });
                //乙类otc
                mClassB.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isClassB = !activated;
                    }
                });
                //处方药rx
                mClassRx.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isClassRx = !activated;
                    }
                });
                //其他
                mClassElse.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isClassElse = !activated;
                    }
                });
                //返回键
                contentView.findViewById(R.id.iv_back).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {

                        }
                        getResult();
                    }
                });
                //左边空白区域
                contentView.findViewById(R.id.view_bg).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {

                        }
                        getResult();
                    }
                });
                btnReset.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        reset(false);
                        //                        if (listener != null) {
                        //                            listener.onDismiss();
                        //                        }
                    }
                });

                btnAffirm.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        getResult();
                    }
                });

                rlAllManufacture.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        initManufacture();

                        String[] strings = setPriceRange();

                        if (mList == null) {
                            mList = new ArrayList<>();
                        }
                        if (mList.isEmpty()) {
                            mList.add(lastName);
                        }
                        mManufacturersPop.setDataType(key, value, isAvailable, isPromotion, isClassA, isClassB, isClassRx, isClassElse, spec, shopCodes, strings[0], strings[1], mList);
                        mManufacturersPop.show();
                    }
                });

            }
        };
        mLeftPopWindow.setClassify(true);
        mLeftPopWindow.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                isScroll(event);
                return false;
            }
        });
    }

    /**
     * 设置是否隐藏间隔区间选项
     *
     * @param isHidden
     */
    public void hiddenPriceRange(boolean isHidden) {
        if (isHidden) {
            tv_price_range.setVisibility(View.GONE);
            ll_price_range.setVisibility(View.GONE);
        } else {
            tv_price_range.setVisibility(View.VISIBLE);
            ll_price_range.setVisibility(View.VISIBLE);
        }
    }
    /**
     * 设置是否隐藏店铺服务选项
     */

    public void hideShopService(boolean isHidden){
        mShopServiceTitle.setVisibility(isHidden?View.GONE:View.VISIBLE);
        mShopServiceOptions.setVisibility(isHidden?View.GONE:View.VISIBLE);
    }

    /**
     * 设置是否隐藏药帮忙服务下的有促销选项
     *
     * @param isHidden
     */
    public void hiddenPromotion(boolean isHidden) {
        if (isHidden) {
            mTvPromotion.setVisibility(View.GONE);
        } else {
            mTvPromotion.setVisibility(View.VISIBLE);
        }
    }

    private void getResult() {

        setListContains(mList);

        String[] strings = setPriceRange();

        if (listener != null) {
            SearchFilterBean filterBean = new SearchFilterBean(isAvailable, isPromotion, isClassA
                    , isClassB, isClassRx, isClassElse, strings[0], strings[1], new ArrayList<>(), isShunfeng, isJD, true, true, false, null, null, null);
            listener.onResult(filterBean);
            listener.onDismiss();
        }
        mLeftPopWindow.dismiss(true);
    }

    private String[] setPriceRange() {

        String[] strings = new String[2];

        String priceRangeFloor = mPriceRangeFloor.getText().toString().trim();
        String priceRangeTop = mPriceRangeTop.getText().toString().trim();

        try {
            if (!TextUtils.isEmpty(priceRangeFloor) && !TextUtils.isEmpty(priceRangeTop)) {
                int priceRangeFloorNum = Integer.parseInt(priceRangeFloor);
                int priceRangeTopNum = Integer.parseInt(priceRangeTop);
                String str = "";
                if (priceRangeFloorNum > priceRangeTopNum) {
                    str = priceRangeFloor;
                    priceRangeFloor = priceRangeTop;
                    priceRangeTop = str;
                }
            }
        } catch (NumberFormatException e) {
            priceRangeFloor = "";
            priceRangeTop = "";
        }
        mPriceRangeFloor.setText(priceRangeFloor);
        mPriceRangeTop.setText(priceRangeTop);
        strings[0] = priceRangeFloor;
        strings[1] = priceRangeTop;

        return strings;
    }

    public void setListener(LeftPopWindow.Listener<SearchFilterBean> listener) {
        if (mLeftPopWindow == null) {
            init();
        }
        this.listener = listener;
    }


    public void hideYBM() {
        if (mLeftPopWindow == null) {
            init();
        }
        if (tvYBM != null) {
            tvYBM.setVisibility(View.GONE);
        }
        if (llYBM != null) {
            llYBM.setVisibility(View.GONE);
        }
    }

    public void reset(boolean isBrand) {

        if (mManufacturersPop != null) {
            mManufacturersPop.reset(isBrand);
        }

        if (mList == null) {
            mList = new ArrayList<>();
        }
        mList.clear();

        if (isBrand) {
            key = "";
            value = "";
        }

        isAvailable = false;
        isPromotion = false;

        isClassA = false;
        isClassB = false;
        isClassRx = false;
        isClassElse = false;
        spec = "";
        shopCodes = "";
        mPriceRangeFloor.setText("");
        mPriceRangeTop.setText("");
        isJD = false;
        isShunfeng = false;

        handler.sendMessage(handler.obtainMessage(10, mList));
        handler.sendMessage(handler.obtainMessage(20));
        handler.sendMessage(handler.obtainMessage(30));
        handler.sendMessage(handler.obtainMessage(40));
        handler.sendMessage(handler.obtainMessage(50));
        handler.sendMessage(handler.obtainMessage(60));
        handler.sendMessage(handler.obtainMessage(70));
        handler.sendMessage(handler.obtainMessage(80)); //处理京东和顺丰快递选项

    }

    public void setNewData(List<String> list) {
        setProductTexts(list);
    }

    private void setProductTexts(List<String> list) {

        if (list == null || list.isEmpty()) {
            mLl01.setVisibility(View.GONE);
            mLl02.setVisibility(View.GONE);
            mLl03.setVisibility(View.GONE);
            mLl04.setVisibility(View.GONE);
            return;
        }
        //设置商品
        if (list.size() > 0 && !TextUtils.isEmpty(list.get(0))) {
            mLl01.setVisibility(View.VISIBLE);
            mLl02.setVisibility(View.GONE);
            mLl03.setVisibility(View.GONE);
            mLl04.setVisibility(View.GONE);
            mTvDetails01.setText(list.get(0));
        }
        if (list.size() > 1 && !TextUtils.isEmpty(list.get(1))) {
            mLl02.setVisibility(View.VISIBLE);
            mLl03.setVisibility(View.GONE);
            mLl04.setVisibility(View.GONE);
            mTvDetails02.setText(list.get(1));
        }
        if (list.size() > 2 && !TextUtils.isEmpty(list.get(2))) {
            mLl03.setVisibility(View.VISIBLE);
            mLl04.setVisibility(View.VISIBLE);
            mTvDetails03.setText(list.get(2));
        }

    }

    public void show() {
        if (mLeftPopWindow == null) {
            init();
        }
        mLeftPopWindow.show();
    }


    public void dismissPop() {
        if (mManufacturersPop != null) {
            mManufacturersPop.dismiss();
        }
        dismiss();
    }

    public void dismiss() {
        if (mLeftPopWindow != null) {
            mLeftPopWindow.dismiss(true);
        }
    }

    public boolean isShow() {
        if (mLeftPopWindow != null) {
            return mLeftPopWindow.isShow();
        }
        return false;
    }

    /*
     * 生产厂家
     * final int pos, final SearchFilterBean bean
     * */
    private void initManufacture() {
        if (mManufacturersPop == null) {
            if (!isPlan) {
                mManufacturersPop = new ManufacturersPop();
            } else {
                mManufacturersPop = new ManufacturersPop(planId);
            }
            mManufacturersPop.setListener(new LeftPopWindow.Listener<List<String>>() {
                @Override
                public void onDismiss() {

                }

                @Override
                public void onResult(List<String> list) {

                    setListContains(list);
                    handler.sendMessage(handler.obtainMessage(10, list));
                }
            });

        }
    }

    /**
     * 如果list包含“全部厂家”就把list制空
     *
     * @param list
     */
    private void setListContains(List<String> list) {
        if (list.contains(lastName)) {
            list.clear();
        }
    }

    @SuppressLint("HandlerLeak")
    private Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 10) {
                if (mList == null) {
                    mList = new ArrayList<>();
                }
                mList.clear();
                mList.addAll((List<String>) msg.obj);
                setNewData(mList);
            } else if (msg.what == 20) {
                mTvAvailable.setActivated(isAvailable);
            } else if (msg.what == 30) {
                mTvPromotion.setActivated(isPromotion);
            } else if (msg.what == 40) {
                mClassA.setActivated(isClassA);
            } else if (msg.what == 50) {
                mClassB.setActivated(isClassB);
            } else if (msg.what == 60) {
                mClassRx.setActivated(isClassRx);
            } else if (msg.what == 70) {
                mClassElse.setActivated(isClassElse);
            } else if (msg.what == 80) {
                mShopServiceJd.setActivated(isJD);
                mShopServiceShunfeng.setActivated(isShunfeng);
            }
    }
};

    private boolean isScroll(MotionEvent ev) {
        if (minSlop <= 0) {
            minSlop = ViewConfiguration.get(BaseYBMApp.getAppContext()).getScaledTouchSlop();
        }
        LogUtils.d("action:" + ev.getAction());
        if (ev.getAction() != MotionEvent.ACTION_MOVE || lastAction != MotionEvent.ACTION_MOVE || (Math.abs(ev.getRawY() - lastY) < minSlop)) {
            lastAction = ev.getAction();
            lastY = ev.getRawY();
            return false;
        }
        lastAction = ev.getAction();
        lastY = ev.getRawY();
        LogUtils.d("滑动了啊----");
        hideSoftInput(mPriceRangeFloor, mPriceRangeTop);
        return true;
    }

    public void hideSoftInput(View view) {
        if (view == null) {
            return;
        }
        try {
            if (inputManager == null) {
                inputManager = (InputMethodManager) BaseYBMApp.getAppContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            }
            inputManager.hideSoftInputFromWindow(view.getWindowToken(), 0);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }


    public void hideSoftInput(View... views) {
        for (View v : views) {
            hideSoftInput(v);
        }
    }

}

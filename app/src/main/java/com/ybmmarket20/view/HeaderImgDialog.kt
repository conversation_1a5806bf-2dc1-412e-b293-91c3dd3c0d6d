package com.ybmmarket20.view

import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.core.content.ContextCompat
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import com.ybmmarket20.R
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.widget.dialog.DialogDescTextAttribute
import com.ybmmarket20.common.widget.dialog.listener.DismissOnBtnClickL
import kotlinx.android.synthetic.main.base_header_img_dialog.view.*

/**
 * @author: yuhaibo
 * @time: 2019-08-14 14:25.
 * projectName: ybm-android.
 * Description:
 */
open class HeaderImgDialog : DialogFragment() {
    var mConfirmClickListener: DismissOnBtnClickL? = null
    var mCancelClickListener: DismissOnBtnClickL? = null

    var mTitle: String? = ""
    var mDesc: String? = ""
    var mImgSrc: Int? = -1
    var mConfirmTxt: String? = ""
    var mCancelTxt: String? = ""
    var root: View? = null
    var descColor: Int? = -1
    var titleColor: Int? = -1
    var descFontSize: Int? = -1
    var titleFontSize: Int? = -1
    var dialogDescTextAttribute: DialogDescTextAttribute? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mTitle = arguments?.getString(Builder.KEY_TITLE)
        mDesc = arguments?.getString(Builder.KEY_DESC)
        mImgSrc = arguments?.getInt(Builder.KEY_IMG_SRC, -1)
        mConfirmTxt = arguments?.getString(Builder.KEY_CONFIRM_TXT)
        mCancelTxt = arguments?.getString(Builder.KEY_CANCEL_TXT)
        descColor = arguments?.getInt(Builder.DESC_COLOR, -1)
        titleColor = arguments?.getInt(Builder.TITLE_COLOR, -1)
        descFontSize = arguments?.getInt(Builder.DESCFONT_SIZE, -1)
        titleFontSize = arguments?.getInt(Builder.TITLEFONT_SIZE, -1)
        dialogDescTextAttribute = arguments?.getParcelable<DialogDescTextAttribute>(Builder.DESC_TEXT_TYPE)
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            dialog?.setCanceledOnTouchOutside(false)
            val dm = DisplayMetrics()
            activity?.windowManager?.defaultDisplay?.getMetrics(dm)
            dialog.window?.setLayout((dm.widthPixels * 0.72).toInt(), ViewGroup.LayoutParams.WRAP_CONTENT)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        root = inflater.inflate(R.layout.base_header_img_dialog, null, false)
        if (dialog?.window != null) {
            dialog?.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }
        //确定按钮监听
        root?.btn_confirm?.setOnClickListener {
            mConfirmClickListener?.onBtnClick()
            dismissAllowingStateLoss()
        }
        //取消按钮监听
        root?.btn_cancel?.setOnClickListener {
            mCancelClickListener?.onBtnClick()
            dismissAllowingStateLoss()
        }
        //头图
        if (mImgSrc != null && mImgSrc != -1) {
            root?.header_img?.visibility = View.VISIBLE
            root?.header_img?.setImageResource(mImgSrc!!)
        } else {
            root?.header_img?.visibility = View.GONE
        }
        //title
        if (TextUtils.isEmpty(mTitle)) {
            root?.tv_title?.visibility = View.GONE
            //动态更改描述的marginTop 为36dp
            if (root != null && root?.tv_desc != null) {
                val params = root?.tv_desc?.layoutParams as RelativeLayout.LayoutParams
                params.setMargins(0, ConvertUtils.dp2px(36f), 0, ConvertUtils.dp2px(12f))
                root?.tv_desc?.layoutParams = params
            }
        } else {
            root?.tv_title?.visibility = View.VISIBLE
            root?.tv_title?.text = mTitle
        }
        //描述
        if (TextUtils.isEmpty(mDesc)) {
            root?.tv_desc?.visibility = View.GONE
        } else {
            root?.tv_desc?.visibility = View.VISIBLE
            root?.tv_desc?.text = mDesc
        }

        //确定按钮的文案
        if (!TextUtils.isEmpty(mConfirmTxt)) {
            root?.btn_confirm?.text = mConfirmTxt
        }
        //取消按钮的文案
        if (!TextUtils.isEmpty(mCancelTxt)) {
            root?.btn_cancel?.text = mCancelTxt
        } else {
            root?.btn_cancel?.visibility = View.GONE
        }
        if (context != null) {
            if (descColor != -1 && descColor != null) {
                context?.let { root?.tv_desc?.setTextColor(ContextCompat.getColor(it, descColor!!)) }
            }
            if (titleColor != -1 && titleColor != null) {
                context?.let { root?.tv_title?.setTextColor(ContextCompat.getColor(it, titleColor!!)) }
            }
        }
        if (descFontSize != -1 && descFontSize != null) {
            root?.tv_desc?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, descFontSize?.toFloat()!!)
        }
        if (titleFontSize != -1 && titleFontSize != null) {
            root?.tv_title?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, titleFontSize?.toFloat()!!)
        }
        if (dialogDescTextAttribute != null) {
            if (!TextUtils.isEmpty(mDesc) && context != null) {
                val spannableString = SpannableString(mDesc)
                val contentSizeSpan = AbsoluteSizeSpan(dialogDescTextAttribute?.size!!, true)
                context?.let { spannableString.setSpan(ForegroundColorSpan(ContextCompat.getColor(it, dialogDescTextAttribute?.changeTxtColos!!)), dialogDescTextAttribute?.changeTxtStart!!, dialogDescTextAttribute?.changeTxtEnd!!, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE) }
                spannableString.setSpan(contentSizeSpan, dialogDescTextAttribute?.changeTxtStart!!, dialogDescTextAttribute?.changeTxtEnd!!, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                root?.tv_desc?.visibility = View.VISIBLE
                root?.tv_desc?.text = spannableString
            }
        }
        return root
    }

    override fun onDismiss(dialog: DialogInterface) {
        if (mDialogOutCloseListener != null) {
            mDialogOutCloseListener?.onOutCloseListener(dialog)
        }
        super.onDismiss(dialog)
    }

    class Builder(val fragmentManager: FragmentManager?) {
        companion object {
            const val KEY_TITLE = "title"
            const val KEY_DESC = "desc"
            const val KEY_IMG_SRC = "img_src"
            const val KEY_CONFIRM_TXT = "confirm_txt"
            const val KEY_CANCEL_TXT = "cancel_txt"
            const val DESC_COLOR = "desc_color"
            const val TITLE_COLOR = "title_color"
            const val DESCFONT_SIZE = "descfont_size"
            const val TITLEFONT_SIZE = "titlefont_size"
            const val DESC_TEXT_TYPE = "desc_text_type"
        }

        private val bundle = Bundle()

        private var confirmCallback: DismissOnBtnClickL? = null
        private var cancelCallback: DismissOnBtnClickL? = null

        fun show(): HeaderImgDialog {
            val ft = fragmentManager?.beginTransaction()
            val prev = fragmentManager?.findFragmentByTag("base_dialog")
            if (prev != null) {
                ft?.remove(prev)
            }
            ft?.addToBackStack(null)

            val newFragment = HeaderImgDialog()
            newFragment.arguments = bundle
            newFragment.mConfirmClickListener = confirmCallback
            newFragment.mCancelClickListener = cancelCallback

            try {
                if (newFragment.isAdded) {
                    newFragment.dismiss()
                } else {
                    ft?.let { newFragment.show(it, "base_dialog") }
                }
            } catch (e: Exception) {
            }
            return newFragment
        }

        /**
         * 设置title
         */
        fun setTitle(title: String?): Builder {
            bundle.putString(KEY_TITLE, title)
            return this
        }

        /**
         * 设置Desc
         */
        fun setDesc(desc: String?): Builder {
            bundle.putString(KEY_DESC, desc)
            return this
        }

        /**
         * 设置头部投片
         */
        fun setHeadImg(imgSrc: Int? = -1): Builder {
            if (imgSrc != null) {
                bundle.putInt(KEY_IMG_SRC, imgSrc)
            }
            return this
        }

        /**
         * 设置取消按钮的text
         */
        fun setBtCancelTxt(confirmTxt: String? = "取消"): Builder {
            bundle.putString(KEY_CANCEL_TXT, confirmTxt)
            return this
        }

        /**
         * 设置确定按钮的text
         */
        fun setBtConfirmTxt(confirmTxt: String? = "确认"): Builder {
            bundle.putString(KEY_CONFIRM_TXT, confirmTxt)
            return this
        }

        /**
         * 取消按钮的监听
         */
        fun setCancelClickListener(cancelCallback: DismissOnBtnClickL?): Builder {
            this.cancelCallback = cancelCallback
            return this
        }

        /**
         * 确定按钮的监听
         */
        fun setConfirmClickListener(confirmCallback: DismissOnBtnClickL?): Builder {
            this.confirmCallback = confirmCallback
            return this
        }

        /**
         * 设置Desc 的字体颜色
         */
        fun setDescTextColos(descColor: Int?): Builder {
            if (descColor != null) {
                bundle.putInt(DESC_COLOR, descColor)
            }
            return this
        }

        /**
         * 设置title 的字体颜色
         */
        fun setTitleTextColos(titleColor: Int?): Builder {
            if (titleColor != null) {
                bundle.putInt(TITLE_COLOR, titleColor)
            }
            return this
        }

        /**
         * 设置Desc 的字体大小
         */
        fun setDescFontSize(descFontSize: Int?): Builder {
            if (descFontSize != null) {
                bundle.putInt(DESCFONT_SIZE, descFontSize)
            }
            return this
        }

        /**
         * 设置title 的字体大小
         */
        fun setTitleFontSize(titleFontSize: Int?): Builder {
            if (titleFontSize != null) {
                bundle.putInt(TITLEFONT_SIZE, titleFontSize)
            }
            return this
        }

        /**
         * 改变描述的显示
         */
        fun setDescTextType(dialogDescTextAttribute: DialogDescTextAttribute?): Builder {
            if (dialogDescTextAttribute != null) {
                bundle.putParcelable(DESC_TEXT_TYPE, dialogDescTextAttribute)
            }
            return this
        }

    }

    private var mDialogOutCloseListener: DialogOutCloseListener? = null
    fun setOutCloseListener(mDialogOutCloseListener: DialogOutCloseListener?) {
        this.mDialogOutCloseListener = mDialogOutCloseListener
    }

    /**
     * Dialog关闭事件
     */
    interface DialogOutCloseListener {
        fun onOutCloseListener(dialog: DialogInterface?)
    }

}

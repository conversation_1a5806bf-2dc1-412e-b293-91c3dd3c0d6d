package com.ybmmarket20.view.homesteady

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Typeface
import androidx.core.content.ContextCompat
import androidx.cardview.widget.CardView
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.SeckillInfo
import com.ybmmarket20.bean.homesteady.SeckillProduct
import com.ybmmarket20.bean.homesteady.ShoppingGuideItem
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * @date 2020-05-11
 * @description 首页导购适配器
 */

//const val SECKILL_STATUS_WAIT = 1           //未开始
//const val SECKILL_STATUS_HAVE_IN_HAND = 2   //进行中
//const val SECKILL_STATUS_END = 3            //已结束
//const val SECKILL_CONVERSE = 4            // 将秒杀数据结构转化成其他结构
@Deprecated("已废弃，不区分ItemType，使用HomeSteadyShoppingGuideAllAdapter代替",replaceWith = ReplaceWith(expression = "HomeSteadyShoppingGuideAllAdapter"))
class HomeSteadyShoppingGuideAdapter(
        var shoppingGuideContext: Context,
        var list: MutableList<ShoppingGuideItem>
) : YBMBaseMultiItemAdapter<ShoppingGuideItem>(list) {

    private var licenseStatus = -1

    init {
        addItemType(HOME_STEADY_LAYOUT_REAL, R.layout.item_home_steady_shopping_guide2)
        addItemType(HOME_STEADY_LAYOUT_DEFAULT, R.layout.item_home_steady_shopping_guide_default)
        addItemType(HOME_STEADY_LAYOUT_TIMER, R.layout.item_home_steady_shopping_guide_kill)
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ShoppingGuideItem?) {
        t?.also { it ->
            when {
                it.itemType == HOME_STEADY_LAYOUT_REAL -> {
                    //不带倒计时
                    baseViewHolder?.setText(R.id.tv_major_title, t.module_title)
                    baseViewHolder?.setText(R.id.tv_minor_title, t.describe?.text)
                    if (t.products?.size ?: 0 >= 1) {
                        baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg2)?.visibility = View.INVISIBLE
                        setImage(baseViewHolder, "${t.products?.get(0)?.imageUrl}", R.id.iv_product1)
                    }
                    if (t.products?.size ?: 0 >= 2) {
                        setImage(baseViewHolder, "${t.products?.get(1)?.imageUrl}", R.id.iv_product2)
                        baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg2)?.visibility = View.VISIBLE
                    }
                    when (baseViewHolder?.layoutPosition) {
                        0 -> R.drawable.icon_home_steady_fast_entry_kill
                        1 -> R.drawable.icon_home_steady_fast_entry_sort
                        2 -> R.drawable.icon_home_steady_fast_entry_daily_discount
                        3 -> R.drawable.icon_home_steady_fast_entry_gross
                        4 -> R.drawable.icon_home_steady_fast_entry_brand_purchase
                        5 -> R.drawable.icon_home_steady_fast_entry_new_recommend
                        else -> 0
                    }.also { resId ->
                        baseViewHolder?.setImageResource(R.id.iv_fast_entry_item_bg, resId)
                    }
                    baseViewHolder?.getConvertView()?.setOnClickListener { v ->
                        val action = if (t.customizedType == 1) {
                            RoutersUtils.open(t.jump_url)
                            t.jump_url ?: ""
                        } else {
                            RoutersUtils.open("ybmpage://homeSubject?strategyTypeId=${it.strategyTypeId}&subjectType=${baseViewHolder.layoutPosition}&majorTitle=${it.module_title}")
                            "ybmpage://homeSubject?strategyTypeId=${it.strategyTypeId}&subjectType=${baseViewHolder.layoutPosition}&majorTitle=${it.module_title}"
                        }
                        var skuId = ""
                        t.products?.forEach {
                            skuId += "_${it.id ?: ""}"
                        }
                        if (skuId.isNotEmpty()) skuId = skuId.substring(1, skuId.length)
                        clickEvent(action, baseViewHolder.adapterPosition, t.module_title ?: "", skuId)
                    }
                }
                it.itemType == HOME_STEADY_LAYOUT_DEFAULT -> {
                    //占位样式
                }
                it.itemType == HOME_STEADY_LAYOUT_TIMER -> {
                    if (t.seckillInfo?.products?.size ?: 0 >= 1) {
                        baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg2)?.visibility = View.INVISIBLE
                        setImage(baseViewHolder, "${t.seckillInfo?.products?.get(0)?.imageUrl}", R.id.iv_product1)
                    }
                    if (t.seckillInfo?.products?.size ?: 0 >= 2) {
                        setImage(baseViewHolder, "${t.seckillInfo?.products?.get(1)?.imageUrl}", R.id.iv_product2)
                        baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg2)?.visibility = View.VISIBLE
                    }
                    baseViewHolder?.setImageResource(R.id.iv_fast_entry_item_bg, R.drawable.icon_home_steady_fast_entry_kill)
                    baseViewHolder?.setText(R.id.tv_major_title, t.module_title)
                    baseViewHolder?.setText(R.id.tv_kill_title, getSeckillName(t.seckillInfo))
                    if (t.seckillInfo?.products?.size ?: 0 >= 1) {
                        val resultStr = setPriceStatus(baseViewHolder?.getView(R.id.tv_price1), t.seckillInfo?.products?.get(0), licenseStatus)
                        if (resultStr.isEmpty()) setPrice(baseViewHolder?.getView(R.id.tv_price1), t.seckillInfo?.products?.get(0))
                    }
                    if (t.seckillInfo?.products?.size ?: 0 >= 2) {
                        val resultStr = setPriceStatus(baseViewHolder?.getView(R.id.tv_price2), t.seckillInfo?.products?.get(0), licenseStatus)
                        if (resultStr.isEmpty()) setPrice(baseViewHolder?.getView(R.id.tv_price2), t.seckillInfo?.products?.get(1))
                    }
                    if (t.seckillInfo?.status == SECKILL_STATUS_WAIT) {
                        baseViewHolder?.getView<TextView>(R.id.tv_kill_title)?.setBackgroundResource(R.drawable.shape_home_steady_kill_title_green)
                        baseViewHolder?.getView<View>(R.id.v_kill)?.setBackgroundResource(R.drawable.shape_home_steady_kill_green)
                        baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_00B377))
                        baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.text = "即将开始"
                    } else if (t.seckillInfo?.status == SECKILL_STATUS_HAVE_IN_HAND) {
                        baseViewHolder?.getView<TextView>(R.id.tv_kill_title)?.setBackgroundResource(R.drawable.shape_home_steady_kill_title_red)
                        baseViewHolder?.getView<View>(R.id.v_kill)?.setBackgroundResource(R.drawable.shape_home_steady_kill_red)
                        baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
                        baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.text = t.seckillInfo?.time
                    } else if (t.seckillInfo?.status == SECKILL_STATUS_END) {
                        baseViewHolder?.getView<TextView>(R.id.tv_kill_title)?.setBackgroundResource(R.drawable.shape_home_steady_kill_title_red)
                        baseViewHolder?.getView<View>(R.id.v_kill)?.setBackgroundResource(R.drawable.shape_home_steady_kill_red)
                        baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
                        baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.text = "已结束"
                    }
                    baseViewHolder?.getConvertView()?.setOnClickListener {
                        RoutersUtils.open(t.jump_url)
                        var skuId = ""
                        t.seckillInfo?.products?.forEach {
                            skuId += "_${it.id ?: ""}"
                        }
                        if (skuId.isNotEmpty()) skuId = skuId.substring(1, skuId.length)
                        clickEvent(t.jump_url ?: "", baseViewHolder.adapterPosition, t.module_title ?: "", skuId)
                    }
                }
            }
        }
    }

    /**
     * 点击事件埋点
     */
    private fun clickEvent(action: String, offset: Int, text: String, skuId: String) {
        try {
            val obj = JSONObject()
            obj.apply {
                put("action", action)
                put("offset", offset + 1)
                put("text", text)
                put("sku_id", skuId)
            }
            XyyIoUtil.track("action_Home_Card", obj)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置价钱
     */
    private fun setPrice(tv: TextView?, seckillProduct: SeckillProduct?) {
        try {
            tv?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
            tv?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
            val price = seckillProduct?.skuPrice ?: ""
            val builder = SpannableStringBuilder("¥$price")
            if (price.isNotEmpty()) {
                val symbolSpan = AbsoluteSizeSpan(ConvertUtils.dp2px(9f))
                val styleSpan = StyleSpan(Typeface.BOLD)
                builder.setSpan(symbolSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                builder.setSpan(styleSpan, 0, price.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            val fob = seckillProduct?.fob ?: ""
            if (fob.isNotEmpty()) {
                val fobBuilder = SpannableStringBuilder("¥$fob")
                val fobSpan = AbsoluteSizeSpan(ConvertUtils.dp2px(10f))
                val colorSpan = ForegroundColorSpan(ContextCompat.getColor(shoppingGuideContext, R.color.color_4d222222))
                val strikeSpan = StrikethroughSpan()
                fobBuilder.setSpan(fobSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                fobBuilder.setSpan(strikeSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                fobBuilder.setSpan(colorSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                builder.append(fobBuilder)
            }
            tv?.text = builder
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置 价格签署协议可见、暂无购买权限、价格认证资质可见
     */
    private fun setPriceStatus(tv: TextView?, seckillProduct: SeckillProduct?, licenseStatus: Int): String {
        tv?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_222))
        tv?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10f)
        if (licenseStatus == 1 || licenseStatus == 5) {
            //通过
            tv?.text = "价格认证资质可见"
            return "价格认证资质可见"
        }
        var priceStatusText = ""
        if (seckillProduct?.isControl == 1 && !seckillProduct.isPurchase) {
            priceStatusText = "暂无购买权限"
        } else {
            if (seckillProduct?.isOEM == true) {
                //是否签署协议
                if (seckillProduct.signStatus != 1) {
                    priceStatusText = "价格签署协议可见"
                }
            }

            //是否符合协议标准展示价格,1:符合0:不符合
            if (seckillProduct?.showAgree == 0) {
                priceStatusText = "价格签署协议可见"
            }
        }

        tv?.text = priceStatusText
        return priceStatusText
    }

    /**
     * 设置商品图片
     */
    private fun setImage(baseViewHolder: YBMBaseHolder?, img: String?, imageViewId: Int) {
        val imageUrl = img?.let {
            if (img.startsWith("http")) {
                img
            } else {
                "${AppNetConfig.LORD_IMAGE}${img}"
            }
        } ?: ""
        ImageHelper.with(shoppingGuideContext).load(imageUrl).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .dontAnimate().into(baseViewHolder?.getView(imageViewId))
    }

    /**
     * 获取xx点场次
     */
    @SuppressLint("SimpleDateFormat")
    fun getSeckillName(seckill: SeckillInfo?): String? = seckill?.let {
        val cal = Calendar.getInstance()
        cal.time = Date(it.startDate)
        val hours = SimpleDateFormat("HH").format(Date(it.startDate))
        return "${hours}点场"
    }

    /**
     * 设置一审状态
     */
    fun setLicenseStatus(licenseStatus: Int) {
        this.licenseStatus = licenseStatus
    }

}
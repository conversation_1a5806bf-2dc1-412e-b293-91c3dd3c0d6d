package com.ybmmarket20.view;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.ModuleViewItem;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import java.util.List;

/**
 * 横向滑动的图片列表
 */
public class DynamicImageListLayout extends BaseDynamicLayout<ModuleViewItem> {
    public int defHeigth = 150;
    protected RecyclerView listView;
    protected YBMBaseAdapter adapter;

    public DynamicImageListLayout(Context context) {
        super(context);
    }

    public DynamicImageListLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicImageListLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        listView = (RecyclerView) findViewById(R.id.rvc_list);
        listView.setNestedScrollingEnabled(false);
    }

    @Override
    public boolean supportSetHei() {
        return true;
    }

    @Override
    public int getDefHeigth() {
        return defHeigth;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_image_list;
    }

    @Override
    public void setItemData(List<ModuleViewItem> items) {
        if (adapter == null) {
            adapter = new YBMBaseAdapter<ModuleViewItem>(R.layout.dynamic_layout_image_list_item, items) {
                @Override
                protected void bindItemView(YBMBaseHolder baseViewHolder, ModuleViewItem moduleViewItem) {
                    ImageView iv = baseViewHolder.getView(R.id.iv);
                    setImageView(iv, moduleViewItem);
                    // 埋点　黄金广告位
                    iv.setTag(R.id.tag_action, moduleViewItem.action);
                    iv.setTag(R.id.tag_2, baseViewHolder.getAdapterPosition());
                    iv.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_HOME_MAIN_AD);
                    iv.setOnClickListener(itemClick);
                }
            };
            listView.setLayoutManager(new WrapLinearLayoutManager(getContext(), HORIZONTAL, false));
            listView.setAdapter(adapter);
        } else {
            adapter.setNewData(items);
        }
    }

    @Override
    public void setStyle(int style) {
        if (style <= 0) {
            return;
        }

    }
}

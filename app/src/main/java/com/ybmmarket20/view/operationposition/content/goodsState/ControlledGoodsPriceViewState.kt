package com.ybmmarket20.view.operationposition.content.goodsState

import android.view.View
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.utils.analysis.BaseFlowData

/**
 * 控销品
 */
class ControlledGoodsPriceViewState(itemView: View) : GoodsViewState(itemView) {

    override fun handleData(
            rowsBean: RowsBean,
            parentPosition: Int,
            isShowShop: Boolean,
            flowData: BaseFlowData?,
            position: Int
    ) {
        super.handleData(rowsBean, parentPosition, isShowShop, flowData,position)
        getGoodsPriceControlled().visibility = View.VISIBLE
        getGoodsPriceControlled().text = rowsBean.controlTitle
        getGoodsShop().visibility = View.GONE
    }
}
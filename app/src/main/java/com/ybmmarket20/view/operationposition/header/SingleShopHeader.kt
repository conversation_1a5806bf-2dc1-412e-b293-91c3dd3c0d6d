package com.ybmmarket20.view.operationposition.header

import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.bean.OperationPositionInfo
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarket20.view.operationposition.track.OPTrackManager
import com.ybmmarket20.view.operationposition.OPCardViewManager
import com.ybmmarketkotlin.utils.TextWithPrefixTag

/**
 * 单店铺头部
 */
class SingleShopHeader(info: OperationPositionInfo, opManager: OPCardViewManager):
    ShopHeader(info, opManager) {
    override fun handleVisibility() {
        opManager.showSingleShopHeader()
    }

    override fun handleData() {
        val logo = opManager.getView<ImageView>(R.id.ivOPSingleShopLogo)
        val tvOPSingleShopName = opManager.getView<TextView>(R.id.tvOPSingleShopName)
        val tvOPSingleShopEntry = opManager.getView<TextView>(R.id.tvOPSingleShopEntry)
        val tvOPSingleShopTags = opManager.getView<ShopNameWithTagView>(R.id.tvOPSingleShopTags)
        ImageUtil.load(
            opManager.getContext(),
//            AppNetConfig.LORD_IMAGE + info.shopLogo,
            AppNetConfig.CDN_HOST + info.shopLogo,
            logo
        )
        tvOPSingleShopName.TextWithPrefixTag(listOf(info.shopQualityTag), info.shopName)
        tvOPSingleShopEntry.visibility =
            if (TextUtils.isEmpty(info.shopUrl)) View.GONE else View.VISIBLE
        tvOPSingleShopEntry.setOnClickListener {
            OPTrackManager.opShopClickTrack(info)
            val mUrl = splicingUrlWithParams(info.shopUrl, hashMapOf(Pair("entrance","搜索-运营位入口")))
            RoutersUtils.open(mUrl)
        }
        tvOPSingleShopTags.bindData(info.shopPropTags)
    }
}
package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.GridView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.common.widget.RoundLinearLayout;

import java.util.Calendar;
import java.util.HashMap;

/**
 * 自定义的日历控件
 */
public class CalendarViewEx extends RoundLinearLayout {

    private GridView gridView; //用来显示日期信息
    private HashMap<Integer, Integer> selectDays = null;
    private CalendarViewAdapter mAdapter;

    public CalendarViewEx(Context context) {
        super(context);
    }

    public CalendarViewEx(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
    }

    //初始化数据
    private void init() {
        setOrientation(LinearLayout.VERTICAL);
        LayoutInflater flater = LayoutInflater.from(getContext());
        //将XML定义的周表头添加
        flater.inflate(R.layout.calendar_week_head, this);

        //获取当前时间
        Calendar calendar = Calendar.getInstance();

        //LinearLayout.LayoutParams params = new LayoutParams(LayoutParams.MATCH_PARENT,LayoutParams.MATCH_PARENT);
        gridView = new GridView(getContext());
        gridView.setVerticalScrollBarEnabled(false);
        gridView.setNumColumns(7);
        gridView.setVerticalSpacing(5);
        gridView.setHorizontalSpacing(5);
        gridView.setSelector(new ColorDrawable(Color.TRANSPARENT));

        this.addView(gridView);
        mAdapter = new CalendarViewAdapter(getContext(), calendar);
        gridView.setAdapter(mAdapter);
    }

    public void selectDayToNowMonth(HashMap<Integer, Integer> list) {
        this.selectDays = list;
        mAdapter.notifyDataSetChanged();
    }

    private class CalendarViewAdapter extends BaseAdapter {

        private Context mContext;
        private Calendar mCalendar = null;
        private int dayCount = 0; //月总天数
        private int nowDay = 0; //今天是本月的第几天
        private int dayOfWeek = 0; //本月一号是星期几

        private int dayToUpmonth = 0; //从上月的那天开始显示
        private LayoutInflater flater;

        public CalendarViewAdapter(Context context, Calendar calendar) {
            this.mContext = context;
            flater = LayoutInflater.from(getContext());
            setDateChanged(calendar);
        }

        private void setDateChanged(Calendar calendar) {
            if (calendar == null)
                return;
            this.mCalendar = calendar;
            //设置星期一为第一天
            //mCalendar.setFirstDayOfWeek(Calendar.MONDAY);
            //获取今天是本月的第几天
            nowDay = mCalendar.get(Calendar.DAY_OF_MONTH);
            //获取本月的总天数
            dayCount = mCalendar.getActualMaximum(Calendar.DATE);

            Calendar currentCal = (Calendar) mCalendar.clone();
            currentCal.set(Calendar.DAY_OF_MONTH, 1); //将日期设置为本月1号，用来获取本月一号的星期几
            dayOfWeek = currentCal.get(Calendar.DAY_OF_WEEK); //获取本月一号是本周的第几天 从星期日开始算

            currentCal.add(Calendar.MONTH, -1); //设置时间为上月1号
            int dayToMonth = currentCal.getActualMaximum(Calendar.DATE); //上月的总天数
            //上月总天数 - 本月1日所在星期  = 开始天数  （得到的是上周星期日的 日期）
            dayToUpmonth = dayToMonth - (dayOfWeek - 1);
        }

        @Override
        public int getCount() {
            //一个月最多跨6周  一周7天
            return 6 * 7;
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(final int position, View convertView, ViewGroup parent) {
            View rootView = null;
            if (convertView == null) {
                rootView = flater.inflate(R.layout.calendar_day_view, null);
            } else {
                rootView = convertView;
            }

            TextView text_day = (TextView) rootView.findViewById(R.id.txt_day);
            TextView text_integral = (TextView) rootView.findViewById(R.id.tv_integral);

            text_day.setBackgroundColor(Color.TRANSPARENT);
            //星期排布 7 1 2 3 4 5 6
            //星期序号 1 2 3 4 5 6 7
            int day = position + 1;
            //是否是下个月的时间
            boolean isNextMothDay = false;
            if (day >= dayOfWeek) {
                day = day - (dayOfWeek - 1);
                text_day.setTextColor(day > dayCount ? Color.LTGRAY : Color.DKGRAY);

                isNextMothDay = day > dayCount;
                day = isNextMothDay ? day - dayCount : day;
                //本月月份和下月份部分日期
                text_day.setText("" + day);
                if (isNextMothDay) {
                    text_day.setVisibility(View.INVISIBLE);
                    return rootView;
                }

                if (day <= dayCount) {
                    if (selectDays != null) {
//                        int index = selectDays.indexOf(day);
//                        if (index != -1) {
//                            //view.setTextColor(Color.RED);
//                            text_day.setBackgroundResource(R.drawable.calendar_day_all_select_bg);
//                        }
                        boolean b = selectDays.containsKey(day);
                        if (b) {
                            text_day.setBackgroundResource(R.drawable.sign_08);
                            text_integral.setVisibility(View.VISIBLE);
                            text_integral.setText("+" + selectDays.get(day));
                        }
                        if (day == nowDay) {//今天
                            text_day.setTextColor(mContext.getResources().getColor(R.color.text_calendar_day));
                        }
                    }
                }
            } else {
                //上月部分日期
                text_day.setText("" + (dayToUpmonth + day));
                text_day.setTextColor(Color.LTGRAY);
                text_day.setVisibility(View.INVISIBLE);
            }
            rootView.setOnClickListener(new mOnclickListener(day == nowDay));
            return rootView;
        }

    }

    private class mOnclickListener implements OnClickListener {

        private final boolean mIsClick;

        public mOnclickListener(boolean isClick) {
            this.mIsClick = isClick;
        }

        @Override
        public void onClick(View v) {
            if (mOnCalendarClickListener != null) {
                mOnCalendarClickListener.onCalendarClick(mIsClick);
            }
        }
    }

    public interface OnCalendarItemClickListener {
        void onCalendarClick(boolean isClick);
    }

    private OnCalendarItemClickListener mOnCalendarClickListener = null;

    public void setOnItemClickListener(OnCalendarItemClickListener listener) {
        this.mOnCalendarClickListener = listener;
    }


}

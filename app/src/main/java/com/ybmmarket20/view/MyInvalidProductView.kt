package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.OrderBuyAgainProduct
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil

/**
 * @class   MyInvalidProductView
 * <AUTHOR>
 * @date  2025/1/23
 * @description  失效品： 单个，2个，RecyclerView滑动样式 组合View
 */
class MyInvalidProductView(
        context: Context,
        attrs: AttributeSet? = null,
) : ConstraintLayout(context, attrs) {

    init {
        initView()
    }

    private lateinit var mIvOne:ImageView
    private lateinit var mIvTwo:ImageView
    private lateinit var mRecyclerView:RecyclerView
    private lateinit var groupOne:Group
    private lateinit var groupTwo:Group
    private var mDataList = arrayListOf<OrderBuyAgainProduct>()

    private fun initView() {
        val root = LayoutInflater.from(context).inflate(R.layout.view_my_invalid_product, this, true)
        mIvOne = root.findViewById(R.id.iv_goods_one)
        mIvTwo = root.findViewById(R.id.iv_goods_two)
        mRecyclerView = root.findViewById(R.id.rv_goods)
        groupOne = root.findViewById(R.id.group_one)
        groupTwo = root.findViewById(R.id.group_two)

    }

    fun setData(dataList: MutableList<OrderBuyAgainProduct>?) {
        mDataList.clear()
        mDataList.addAll(dataList?: arrayListOf())
        handleUIVisibility(dataList?: arrayListOf())
    }

    private fun handleUIVisibility(dataList: MutableList<OrderBuyAgainProduct>) {
        when (mDataList.size) {
            0-> {
                this.visibility = View.GONE
                groupOne.visibility = View.GONE
                groupTwo.visibility = View.GONE
                mRecyclerView.visibility = View.GONE
            }
            1 -> {
                this.visibility = View.VISIBLE
                groupOne.visibility = View.VISIBLE
                groupTwo.visibility = View.GONE
                mRecyclerView.visibility = View.GONE
                handleImageView(mIvOne,dataList[0].imageUrl?:"")
            }
            2 -> {
                this.visibility = View.VISIBLE
                groupOne.visibility = View.VISIBLE
                groupTwo.visibility = View.VISIBLE
                mRecyclerView.visibility = View.GONE
                handleImageView(mIvOne,dataList[0].imageUrl?:"")
                handleImageView(mIvTwo,dataList[1].imageUrl?:"")
            }
            else -> {
                this.visibility = View.VISIBLE
                groupOne.visibility = View.GONE
                groupTwo.visibility = View.GONE
                mRecyclerView.visibility = View.VISIBLE
                mRecyclerView.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                mRecyclerView.adapter = InvalidProductAdapter().apply {
                    mData = dataList
                }
            }
        }
    }

    private fun handleImageView(imageView: ImageView, data: String){
        ImageUtil.load(context, AppNetConfig.LORD_IMAGE + data,imageView)
    }


    private class InvalidProductAdapter : RecyclerView.Adapter<InvalidProductAdapter.MyViewHolder>() {

        var mData = mutableListOf<OrderBuyAgainProduct>()
            set(value) {
                field.clear()
                field.addAll(value)
                notifyDataSetChanged()
            }
        private lateinit var mContext: Context
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
            mContext = parent.context
            val view = LayoutInflater.from(parent.context).inflate(R.layout.item_cant_buy_invalid_product, parent, false)

            return MyViewHolder(view)
        }

        override fun getItemCount(): Int = mData.size

        override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
            val imageView = holder.itemView.findViewById<ImageView>(R.id.iv_goods)
            ImageUtil.load(mContext, (AppNetConfig.LORD_IMAGE + mData[position].imageUrl),imageView)
        }

        private class MyViewHolder(mView: View):RecyclerView.ViewHolder(mView)

    }
}
package com.ybmmarket20.view.searchFilter.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

//标题数据
const val SEARCH_FILTER_DATA_TYPE_TITLE = 1
//价格区间数据
const val SEARCH_FILTER_DATA_TYPE_PRICE_RANGE = 2
//规格数据
const val SEARCH_FILTER_DATA_TYPE_SPEC = 3
//厂家数据
const val SEARCH_FILTER_DATA_TYPE_MANUFACTURER = 4
//商家数据
const val SEARCH_FILTER_DATA_TYPE_SHOP = 5
//药品类型数据
const val SEARCH_FILTER_DATA_TYPE_DRUG_TYPE = 6
//服务数据
const val SEARCH_FILTER_DATA_TYPE_SERVICE = 7

//RecyclerView列
const val SEARCH_FILTER_SPAN_COUNT = 12

//标题spanSize
const val SEARCH_FILTER_SPAN_SIZE_SERVICE = SEARCH_FILTER_SPAN_COUNT
//标题spanSize
const val SEARCH_FILTER_SPAN_SIZE_TITLE = SEARCH_FILTER_SPAN_COUNT
//价格区间spanSize
const val SEARCH_FILTER_SPAN_SIZE_PRICE_RANGE = SEARCH_FILTER_SPAN_COUNT
//规格spanSize
const val SEARCH_FILTER_SPAN_SIZE_SPEC = SEARCH_FILTER_SPAN_COUNT / 3
//厂家spanSize
const val SEARCH_FILTER_SPAN_SIZE_MANUFACTURER = SEARCH_FILTER_SPAN_COUNT
//商家spanSize
const val SEARCH_FILTER_SPAN_SIZE_SHOP = SEARCH_FILTER_SPAN_COUNT / 2
//药品类型spanSize
const val SEARCH_FILTER_SPAN_SIZE_DRUG_TYPE = SEARCH_FILTER_SPAN_COUNT / 4

/**
 * 筛选条adapter
 */
class SearchFilterAdapter(list: MutableList<FullSearchFilterBean>): BaseSearchFilterAdapter<FullSearchFilterBean>(list) {

    private val titleAdapter = SearchFilterTitleAdapter()
    private val serviceAdapter = SearchFilterServiceAdapter()
    private val priceRangeAdapter = SearchFilterPriceRangeAdapter()
    private val specAdapter = SearchFilterSpecAdapter()
    private val manufacturerAdapter = SearchFilterManufacturerAdapter()
    private val shopAdapter = SearchFilterShopAdapter()
    private val drugTypeAdapter = SearchFilterDrugTypeAdapter()

    init {
        registerAdapter(mutableListOf(
            titleAdapter,
            serviceAdapter,
            specAdapter,
            priceRangeAdapter,
            manufacturerAdapter,
            shopAdapter,
            drugTypeAdapter
        ))
    }
}
package com.ybmmarket20.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.Gravity
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.marginStart
import com.google.android.material.internal.FlowLayout
import com.ybmmarket20.bean.RelatedAptitudeTag
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.utils.UiUtils

@SuppressLint("RestrictedApi")
class DownloadRelatedAptitudeTagsView(context: Context, attrs: AttributeSet?) :
    FlowLayout(context, attrs) {

    private var mTags: List<RelatedAptitudeTag>? = null
    private var mItemClickCallback: ((tag: RelatedAptitudeTag) -> Unit)? = null

    fun setData(tags: List<RelatedAptitudeTag>?) {
        tags?: return
        mTags = tags
        tags.forEach(::addTagView)
    }

    fun setOnItemClick(itemClickCallback: ((tag: RelatedAptitudeTag) -> Unit)?) {
        mItemClickCallback = itemClickCallback
    }

    fun getData() = mTags

    private fun addTagView(tag: RelatedAptitudeTag) {
        val tv = RoundTextView(context)
        tv.text = tag.msg
        tv.textSize = 12f
        tv.gravity = Gravity.CENTER
        tv.setPadding(10.dp, 5.dp, 10.dp, 5.dp)
        val lp = MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        lp.setMargins(10.dp, 0, 0, 0)
        tv.layoutParams = lp
        tv.setOnClickListener {
            if (tag.isEnable()) {
                tag.isSelected = !tag.isSelected
                setTagStyle(tag, tv)
            }
            mItemClickCallback?.invoke(tag)
        }
        addView(tv)
        setTagStyle(tag, tv)
    }

    private fun setTagStyle(tag: RelatedAptitudeTag, tv: RoundTextView) {
        tv.setCornerRadius(2.dp)
        if (!tag.isEnable()) {
            tv.setTextColor(Color.parseColor("#999999"))
            tv.setBackgroundColor(Color.parseColor("#F7F7F8"))
        } else if (tag.isSelected) {
            tv.setTextColor(Color.parseColor("#00B955"))
            tv.setStrokeWidth(0.5f.dp)
            tv.setStrokeColor(Color.parseColor("#00B955"))
            tv.setBackgroundColor(Color.parseColor("#F7F7F8"))
        } else {
            tv.setTextColor(Color.parseColor("#292933"))
            tv.setBackgroundColor(Color.parseColor("#F7F7F8"))
            tv.setStrokeWidth(0)
        }
    }

 }
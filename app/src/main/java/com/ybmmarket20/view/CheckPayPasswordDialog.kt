package com.ybmmarket20.view

import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Observer
import com.ybmmarket20.R
import com.ybmmarket20.activity.jdpay.SET_PAY_PASSWORD_MODIFY
import com.ybmmarket20.activity.jdpay.SET_PAY_PASSWORD_SETTING
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CheckPayPasswordBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.CheckPayPasswordViewModel

/**
 * 验证支付密码
 */
class CheckPayPasswordDialog(val activity: BaseActivity, val viewModel: CheckPayPasswordViewModel) :
    ShowAptitudeBottomAddImageDialog(activity), View.OnClickListener {

    private var tvForgetPw: TextView? = null
    private var tvErrorTip: TextView? = null
    private var ivBack: ImageView? = null
    private var tvTitle: TextView? = null
    private var receiveView: InputPasswordReceiveView? = null
    private var inputView: InputPasswordInputView? = null
    private var mOrderId: String? = null
    private var mOrderNo: String? = null
    private var mTranNo: String? = null
    var mIsPay = false
    private var mIsPayCheck = false

    private var checkPasswordCallback: ((CheckPayPasswordBean) -> Unit)? = null

    //dismiss()回调
    var closePayPWDialogCallback: (() -> Unit)? = null

    //忘记密码回调
    private var forgetPasswordCallback: (() -> Unit)? = null

    private var hasObserver = false

    override fun getLayoutId(): Int = R.layout.dialog_check_pay_passwork

    override fun initView() {
        super.initView()
        tvForgetPw = getView(R.id.tvForgetPw)
        tvErrorTip = getView(R.id.tvErrorTip)
        ivBack = getView(R.id.ivBack)
        receiveView = getView(R.id.receiveView)
        inputView = getView(R.id.inputView)
        tvTitle = getView(R.id.tvTitle)
        receiveView?.enablePwInputType()
    }

    fun setInit(isPayCheck: Boolean = false) {
        mIsPayCheck = isPayCheck
        if (!hasObserver) {
            hasObserver = true
            setObserver()
        }
        inputView?.setOnInputChange {type, num ->
            when (type) {
                INPUT_PASSWORD_INPUT_ITEM_TYPE_NUM -> {
                    //数字
                    receiveView?.inputWord("$num")
                }

                INPUT_PASSWORD_INPUT_ITEM_TYPE_DELETE -> {
                    //删除
                    receiveView?.deleteWord()
                }
            }
        }
        receiveView?.addTextChangeListener { _: EditText, code: String ->
            if (code.length == 6) {
                activity.showProgress()
                if (isPayCheck) {
                    viewModel.checkPayPasswordForPay(code, "1", orderId = mOrderId?: "", mOrderNo?: "", mTranNo?: "")
                } else {
                    viewModel.checkPayPassword(code, "1", orderId = mOrderId?: "", mOrderNo?: "")
                }
            }
        }
    }

    fun setOrderId(orderId: String) {
        mOrderId = orderId
    }

    fun setOrderNo(orderNo: String) {
        mOrderNo = orderNo
    }

    fun setTranNo(tranNo: String) {
        mTranNo = tranNo
    }

    private fun setObserver() {
        tvForgetPw?.setOnClickListener(this)
        ivBack?.setOnClickListener(this)
        val observer = Observer<BaseBean<CheckPayPasswordBean>> {
            activity.dismissProgress()
            if (it.isSuccess) {
                if (it.data.status == 1) {
                    it.data.isPay = mIsPay
                    checkPasswordCallback?.invoke(it.data)
                    dismiss()
                } else {
                    tvErrorTip?.text = it.data.msg
                    clearReceive()
                }
            }
            clearReceive()
        }
        if (mIsPayCheck) {
            viewModel.checkPayPasswordForPayLiveData.observe(activity, observer)
        } else {
            viewModel.checkPayPasswordLiveData.observe(activity, observer)
        }


    }

    fun setOnCheckPasswordCallback(callback: ((CheckPayPasswordBean) -> Unit)?) {
        checkPasswordCallback = callback
    }
    fun setOnForgetPassword(callback: (() -> Unit)?) {
        forgetPasswordCallback = callback
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.tvForgetPw -> {
                if (forgetPasswordCallback != null) {
                    forgetPasswordCallback?.invoke()
                    return
                }
                RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING", 100)
                dismiss()
            }

            R.id.ivBack -> {
                dismiss()
                closePayPWDialogCallback?.invoke()
            }
        }
    }

    fun setTitle(title: String) {
        tvTitle?.text = title
    }

    fun clearReceive() {
        receiveView?.clear()
    }

    fun cleanErrorMsg() {
        tvErrorTip?.text = ""
        receiveView?.clear()
    }

    override fun show() {
        tvErrorTip?.text = ""
        receiveView?.clear()
        super.show()
    }

    fun showAndCannotDismiss() {
        tvErrorTip?.text = ""
        receiveView?.clear()
        super.show()
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
    }
}
package com.ybmmarket20.view.databinding

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.ScreenUtils

/**
 * 设置RecyclerView Decoration
 */

/**
 * 搜索拼团
 */
//@BindingAdapter(value = ["searchSpellGroupItemDecoration"])
fun searchProductSpellGroup(view: RecyclerView, isShow: Boolean) {
    if (!isShow) return
    view.addItemDecoration(object : RecyclerView.ItemDecoration() {

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val childAdapterPosition = parent.getChildAdapterPosition(view)
            val layoutManager = parent.layoutManager
            if (layoutManager !is GridLayoutManager) return
            val dp = ScreenUtils.dip2px(view.context, 1f)
            when {
                childAdapterPosition % layoutManager.spanCount == 0 -> {
                    //最后一列
                    outRect.set(8 * dp, 0, 10 * dp, 12 * dp)
                }
                childAdapterPosition % layoutManager.spanCount == 1 -> {
                    //第一列
                    outRect.set(12 * dp, 0, 8 * dp, 12 * dp)
                }
                else -> {
                    //其他列
                    outRect.set(8 * dp, 0, 8 * dp, 12 * dp)
                }
            }
        }

    })
}
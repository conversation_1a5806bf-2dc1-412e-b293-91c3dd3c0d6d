package com.ybmmarket20.bean;

public class RefundOrderStatusBean {

    public int payType;//":3,支付类型，1在线支付2货到付款3线下转账
    public int refundMode;//":1,退款方式，退货退款  2仅退货
    public int auditProcessState;//":-1,
    public int auditState;//":0
    public int checkExpressState;//":1,运单信息状态 1正确   0错误
    public int cancelRefundOrderState;//":0,是否可以取消订单 1正确   0错误
    public int checkBankState;//":0,检查收汇款信息状态  1正确  0错误

}

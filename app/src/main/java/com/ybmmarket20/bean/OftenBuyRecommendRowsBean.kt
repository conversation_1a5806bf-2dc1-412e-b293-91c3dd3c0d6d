package com.ybmmarket20.bean

import com.ybm.app.bean.AbstractMutiItemEntity

/**
 * <AUTHOR>
 * @date 2022/11/28
 * @description
 */
/**
 * 常购清单Item
 */
class OftenBuyRecommendRowsBean: RowsBean() {
    override fun getItemType(): Int = OFTEN_BUY_RECOMMEND_ITEM
}

/**
 * 常购清单推荐Item
 */
class OftenBuyRecommendTitle(
    var title: String
): RowsBean() {
    override fun getItemType(): Int = OFTEN_BUY_RECOMMEND_TITLE_ITEM
}

/**
 * 常购清单空Item
 */
class OftenBuyEmptyBean : AbstractMutiItemEntity() {
    override fun getItemType(): Int = OFTEN_BUY_ITEM_EMPTY
}
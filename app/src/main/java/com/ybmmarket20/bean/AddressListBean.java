package com.ybmmarket20.bean;

import com.ybmmarket20.utils.StringUtil;

import java.io.Serializable;

/**
 * 收货地址
 */
public class AddressListBean implements Serializable {

    private static final long serialVersionUID = 9072580481642846634L;
    public int id;
    public String merchantId;
    public String province;
    public String city;
    public String district;
    public String address;
    public String contactor;
    public String mobile;
    public String telphone;
    public String remark;
    public String text;
    public boolean updateAddress;
    public long createtime;
    public boolean isdefault;
    public boolean isdefaultId;
    public int provinceCode;
    public int cityCode;
    public int areaCode;
    public String fullAddress;
    public String addressType;


    public boolean isIsdefault() {
        return isdefault;
    }

    public void setIsdefault(boolean isdefault) {
        this.isdefault = isdefault;
    }

    public boolean isIsdefaultId() {
        return isdefaultId;
    }

    public void setIsdefaultId(boolean isdefaultId) {
        this.isdefaultId = isdefaultId;
    }

    public String getFullAddress() {
        if (StringUtil.isEmpty(fullAddress)) {
            return "";
        }
        return fullAddress;
    }
    public String getProvince() {
        if (StringUtil.isEmpty(province)) {
            return "";
        }
        return province;
    }

    public String getCity() {
        if (StringUtil.isEmpty(city)) {
            return "";
        }
        return city;
    }

    public String getDistrict() {
        if (StringUtil.isEmpty(district)) {
            return "";
        }
        return district;
    }

    public String getAddress() {
        if (StringUtil.isEmpty(address)) {
            return "";
        }
        return address;
    }

    public String getContactor() {
        if (StringUtil.isEmpty(contactor)) {
            return "";
        }
        return contactor;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setContactor(String contactor) {
        this.contactor = contactor;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public boolean isUpdateAddress() {
        return updateAddress;
    }

    public void setUpdateAddress(boolean updateAddress) {
        this.updateAddress = updateAddress;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AddressListBean that = (AddressListBean) o;

        return id == that.id;

    }

    @Override
    public int hashCode() {
        return (id+"").hashCode();
    }
}

package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class Rows2Bean  implements Parcelable {

    public double productPrice;
    public int productAmount;
    public double subTotal;
    public String imageUrl;
    public String productName;
    public String spec;
    public long createTime;
    public String id;
    public String orderNo;


    protected Rows2Bean(Parcel in) {
        productPrice = in.readDouble();
        productAmount = in.readInt();
        subTotal = in.readDouble();
        imageUrl = in.readString();
        productName = in.readString();
        spec = in.readString();
        createTime = in.readLong();
        id = in.readString();
        orderNo = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeDouble(productPrice);
        dest.writeInt(productAmount);
        dest.writeDouble(subTotal);
        dest.writeString(imageUrl);
        dest.writeString(productName);
        dest.writeString(spec);
        dest.writeLong(createTime);
        dest.writeString(id);
        dest.writeString(orderNo);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Rows2Bean> CREATOR = new Creator<Rows2Bean>() {
        @Override
        public Rows2Bean createFromParcel(Parcel in) {
            return new Rows2Bean(in);
        }

        @Override
        public Rows2Bean[] newArray(int size) {
            return new Rows2Bean[size];
        }
    };
}
package com.ybmmarket20.bean;

import com.mcxtzhang.indexlib.IndexBar.bean.BaseIndexPinyinBean;

import java.io.Serializable;

/**
 *  药品厂家
 */

public class ManufacturersBean extends BaseIndexPinyinBean implements Serializable {
    public String id;
    public String manufacturer;
    public boolean isTop = false;
    public boolean isSelected;
    public boolean isTop() {
        return isTop;
    }

    public ManufacturersBean setTop(boolean top) {
        isTop = top;
        return this;
    }

    @Override
    public String getTarget() {
        return manufacturer;
    }

    public ManufacturersBean() {

    }

    public ManufacturersBean(String name) {
        this.manufacturer = name;
    }

    @Override
    public boolean isNeedToPinyin() {
        return !isTop;
    }

    @Override
    public boolean isShowSuspension() {
        return !isTop;
    }

}

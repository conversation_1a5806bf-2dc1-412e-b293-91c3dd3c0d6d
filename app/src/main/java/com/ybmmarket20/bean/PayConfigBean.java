package com.ybmmarket20.bean;

import com.ybmmarket20.bean.payment.VirtualGoldRechargeTextBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 支付配置接口
 */
public class PayConfigBean {
    public List<PayConfig> paymentlist;
    //折叠的支付项
    public List<PayConfig> extendPaymentList;
    public String payEndTime;
    public String tips;//温馨提示
    public String isChannelOrder;//1渠道订单,0订单详情
    public String guangfaStatus;//广发支付按钮状态，0.可用，1新用户，2置灰
    public String guangfaTips;//广发额度提示文案
    public String money;//付款金额
    public String payDiscountTips; //实付优惠提示
    public Long countDownTime;
    public List<PayTypeEntry> payTypeEntryList;
    private String prompt; //购物金充值支付方式切换 弹窗文案
    private ArrayList<VirtualGoldRechargeTextBean> rechargeTextList; //购物金收银台显示文案
    private ArrayList<VirtualGoldRechargeTextBean> resultTextList; //购物金支付成功页 tips
    private String resultType; // 购物金支付成功页展示类型 1 展示我的红包 2 展示我的优惠券

    public ArrayList<VirtualGoldRechargeTextBean> getRechargeTextList() {
        return rechargeTextList;
    }

    public void setRechargeTextList(ArrayList<VirtualGoldRechargeTextBean> rechargeTextList) {
        this.rechargeTextList = rechargeTextList;
    }

    public ArrayList<VirtualGoldRechargeTextBean> getResultTextList() {
        return resultTextList;
    }

    public void setResultTextList(ArrayList<VirtualGoldRechargeTextBean> resultTextList) {
        this.resultTextList = resultTextList;
    }

    public String getResultType() {
        return resultType;
    }

    public void setResultType(String resultType) {
        this.resultType = resultType;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
}

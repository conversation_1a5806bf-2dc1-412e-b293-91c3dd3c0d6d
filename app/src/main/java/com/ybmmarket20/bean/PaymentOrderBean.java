package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * Created by asus on 2016/4/13.
 */
public class PaymentOrderBean implements Parcelable{

    public int id;
    public String merchantId;
    public String orderNo;
    public int varietyNum;
    public String contactor;
    public String mobile;
    public String address;
    public String addressId;
    public double money;
    public int paytype;
    public String deliveryTime;
    public Object billInfo;
    public String remark;
    public Object status;
    public String creator;
    public Object createTime;
    public Object updator;
    public Object updateTime;
    public Object shipTime;
    public Object finishTime;
    public Object name;
    public Object imageUrl;
    public Object merchantName;
    public String cartIds;
    public double startingPrice;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    protected PaymentOrderBean(Parcel in) {
        id = in.readInt();
        merchantId = in.readString();
        orderNo = in.readString();
        varietyNum = in.readInt();
        contactor = in.readString();
        mobile = in.readString();
        address = in.readString();
        addressId = in.readString();
        money = in.readDouble();
        paytype = in.readInt();
        deliveryTime = in.readString();
        remark = in.readString();
        creator = in.readString();
        cartIds = in.readString();
        startingPrice = in.readDouble();
    }

    public static final Creator<PaymentOrderBean> CREATOR = new Creator<PaymentOrderBean>() {
        @Override
        public PaymentOrderBean createFromParcel(Parcel in) {
            return new PaymentOrderBean(in);
        }

        @Override
        public PaymentOrderBean[] newArray(int size) {
            return new PaymentOrderBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(merchantId);
        dest.writeString(orderNo);
        dest.writeInt(varietyNum);
        dest.writeString(contactor);
        dest.writeString(mobile);
        dest.writeString(address);
        dest.writeString(addressId);
        dest.writeDouble(money);
        dest.writeInt(paytype);
        dest.writeString(deliveryTime);
        dest.writeString(remark);
        dest.writeString(creator);
        dest.writeString(cartIds);
        dest.writeDouble(startingPrice);
    }
}

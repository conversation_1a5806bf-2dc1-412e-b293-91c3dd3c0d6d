package com.ybmmarket20.bean;

import java.util.List;

public class ActivityTagBean {

    public List<SkuTagNotesBean> skuTagNotes;//商品活动标签文案
    public int tagType; //标签类型 1-商品角标 2-活动主题
    public String tagUrl;//活动标签图标地址
    public String tagNoteBackGroupUrl;//标签文案背景图地址

    public String timeStr;              // 活动开始时间字符格式

    // 自然人店铺活动标签
    public String customTopNote;            //自定义顶部文案
    public String customBottomNote;         //自定义底部文案
    public int sourceType;                  //1:平台2.店铺

    public boolean isTagType() {
        return tagType == 2;
    }

}

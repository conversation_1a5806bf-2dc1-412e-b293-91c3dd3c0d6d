package com.ybmmarket20.bean

import com.ybmmarket20.view.ShopQualificationItem

/**
 * 店铺-数据
 */
data class OpenAccountMapping(
  var companyName: String, // 企业名称
  var customerServiceNumber: String, //客服电话
  var operatingRange: String, //经营范围
  var electronicName: String, //电子版名称
  var electronicType: Int, //电子版开户 0 无，1 有
  var electronicExplain: String, //电子版开户说明
  var pagerName: String, //纸质开户名称
  var pagerType: Int, //纸质开户 0 无， 1 有
  var pagerExplain: String,//纸质开户说明
  var receiver: String,//纸质版收件人
  var receiverPhone: String, //收件人电话
  var address: String,//地址
  var remark: String, //备注
  var openAccountInstruction: String, //开户说明
  var openAccountData: MutableList<ClientResourcesList>, //开户所需资料
  var openAccountModeList: MutableList<ShopQualificationItem>? //开户方式
) {
    constructor(): this("", "", "", "", 0, "", 
        "", 0, "", "", "", "", "", "", mutableListOf(), mutableListOf())
}
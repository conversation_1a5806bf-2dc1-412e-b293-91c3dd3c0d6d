package com.ybmmarket20.bean

/**
 * 找相似 商品bean
 */

// 失效商品
const val ITEM_TYPE_FIND_SAME_GOODS_INVALIDATED = -1
const val ITEM_TYPE_FIND_SAME_GOODS_NORMAL = 0


class FindSameGoodsRowsBean(
    //是否只有一个失效商品(无相似品)
    var isOnlyValidate: Boolean = false
): RowsBean() {

    override fun getItemType(): Int = if (itemType == 0) ITEM_TYPE_FIND_SAME_GOODS_NORMAL else ITEM_TYPE_FIND_SAME_GOODS_INVALIDATED
}
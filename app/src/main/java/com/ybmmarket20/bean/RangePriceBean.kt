package com.ybmmarket20.bean

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import com.ybmmarket20.bean.homesteady.SpellGroupActRangePrice
import com.ybmmarket20.utils.UiUtils

/**
 * 区间价
 */
data class RangePriceBean(
    var minSkuPrice: String?,
    var maxSkuPrice: String?,
    var startingPriceShowText: String?,
    var rangePriceShowText: String?
)


/**
 * 是否是阶梯价
 */
fun RangePriceBean?.isStep(): Boolean = this != null

/**
 * 商品列表阶梯价
 * ￥xxx~xxx
 */
fun RangePriceBean?.getDoubleStepSpannableForGoodsList(): SpannableStringBuilder {
    return getDoubleStepSpannable(11, 16)
}

/**
 * 商品列表阶梯价
 * ￥xxx起
 */
fun RangePriceBean?.getSingleStepSpannableForGoodsList(): SpannableStringBuilder {
    return getSingleStepSpannable(11, 16)
}

/**
 * 运营位商品列表阶梯价
 * ￥xxx起
 */
fun RangePriceBean?.getSingleStepSpannableForOPGoods(): SpannableStringBuilder {
    return getSingleStepSpannable(14, 18, 11)
}

/**
 * 商详阶梯价
 * ￥xxx起
 */
fun RangePriceBean?.getSingleStepSpannableForCommodity(): SpannableStringBuilder {
    return getSingleStepSpannable(16, 25, 14)
}

/**
 * 商详阶梯价-同频规格
 * ￥xxx起
 */
fun RangePriceBean?.getSingleStepSpannableForCommoditySpecification(): SpannableStringBuilder {
    return getSingleStepSpannable(10, 14)
}
fun SpellGroupActRangePrice?.getSingleStepSpannableForCommoditySpecification(): SpannableStringBuilder {
    return getSingleStepSpannable(10, 14)
}

/**
 * 商详阶梯价
 * ￥xxx起
 */
fun RangePriceBean?.getSingleStepSpannableForCommodityHorizontalRecommend(): SpannableStringBuilder {
    return getSingleStepSpannable(16, 25, 14)
}

/**
 * 首页阶梯价
 * ￥xxx起
 */
fun RangePriceBean?.getSingleStepSpannableForHome(): SpannableStringBuilder {
    return getSingleStepSpannable(11, 17)
}

/**
 * 店铺列表阶梯价
 * ￥xxx起
 */
fun RangePriceBean?.getSingleStepSpannableForShopList(): SpannableStringBuilder {
    return getSingleStepSpannable(10, 14)
}

/**
 * ￥xxx~xxx
 */
fun RangePriceBean?.getDoubleStepSpannable(smallSize: Int, bigSize: Int): SpannableStringBuilder {
    if (this?.minSkuPrice?.isEmpty() == true || this?.maxSkuPrice?.isEmpty() == true) return SpannableStringBuilder()
    return try {
        val symbolicSpannable = SpannableStringBuilder("¥")
        symbolicSpannable.setSpan(AbsoluteSizeSpan(smallSize, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        val step1 = priceSpannable(this!!.minSkuPrice!!, smallSize, bigSize)
        val middle = SpannableStringBuilder("~")
        middle.setSpan(AbsoluteSizeSpan(bigSize, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        val step2 = priceSpannable(this.maxSkuPrice!!, smallSize, bigSize)
        symbolicSpannable.append(step1).append(middle).append(step2)
    } catch (e: Exception) {
        SpannableStringBuilder()
    }
}

/**
 * ￥xxx起
 */
fun RangePriceBean?.getSingleStepSpannable(smallSize: Int, bigSize: Int, suffixSize: Int = -1): SpannableStringBuilder {
    if (this?.minSkuPrice?.isEmpty() == true) return SpannableStringBuilder()
    return try {
        val symbolicSpannable = SpannableStringBuilder("¥")
        symbolicSpannable.setSpan(AbsoluteSizeSpan(smallSize, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        val step1 = priceSpannable(this!!.minSkuPrice!!, smallSize, bigSize)
        val suffix = SpannableStringBuilder(" 起")
        suffix.setSpan(AbsoluteSizeSpan(if (suffixSize == -1) smallSize else suffixSize, true), 0, 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        symbolicSpannable.append(step1).append(suffix)
    } catch (e: Exception) {
        SpannableStringBuilder()
    }
}

/**
 * ￥xxx起
 */
fun SpellGroupActRangePrice?.getSingleStepSpannable(smallSize: Int, bigSize: Int, suffixSize: Int = -1): SpannableStringBuilder {
    if (this?.minSkuPrice?.isEmpty() == true) return SpannableStringBuilder()
    return try {
        val symbolicSpannable = SpannableStringBuilder("¥")
        symbolicSpannable.setSpan(AbsoluteSizeSpan(smallSize, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        val step1 = priceSpannable(this!!.minSkuPrice!!, smallSize, bigSize)
        val suffix = SpannableStringBuilder(" 起")
        suffix.setSpan(AbsoluteSizeSpan(if (suffixSize == -1) smallSize else suffixSize, true), 0, 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        symbolicSpannable.append(step1).append(suffix)
    } catch (e: Exception) {
        SpannableStringBuilder()
    }
}


/**
 * 单阶梯字符串
 */
fun RangePriceBean?.getSingleStepPriceStr(): String {
    if (this?.minSkuPrice?.isEmpty() == true) return "¥0.00"
    return "¥${this!!.minSkuPrice}起"
}

/**
 * 多阶梯字符串
 */
fun RangePriceBean?.getDoubleStepPriceStr(): String {
    if (this?.minSkuPrice?.isEmpty() == true || this?.maxSkuPrice?.isEmpty() == true) return "¥0.00"
    return "¥${this!!.minSkuPrice}~${this.maxSkuPrice}"
}



/**
 * 获取价格样式
 */
fun priceSpannable(price: String, smallSize: Int, bigSize: Int): SpannableStringBuilder = try{
    val ssb = SpannableStringBuilder(UiUtils.transform(price))
    ssb.setSpan(AbsoluteSizeSpan(bigSize, true), 0, ssb.length - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    ssb.setSpan(AbsoluteSizeSpan(smallSize, true), ssb.length - 2, ssb.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    ssb
} catch (e: Exception) {
    SpannableStringBuilder()
}



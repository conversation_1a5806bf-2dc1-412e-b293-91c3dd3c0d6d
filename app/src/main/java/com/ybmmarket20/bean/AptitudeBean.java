package com.ybmmarket20.bean;

import java.io.Serializable;
/**
 * 单据bean
 */
public class AptitudeBean implements Serializable {

    private static final int STATUS_UNCOMMITTED = 1;//草稿
    private static final int STATUS_REJECTED = 4;//已驳回
    private static final int STATUS_STEP1 = 2;//一审中
    private static final int STATUS_STEP2 = 3;//二审中
    private static final int STATUS_AWAITS_RECYCLING = 5;//资质待回收
    private static final int STATUS_CANCELLATION = 7;//已作废
    private static final int STATUS_RECYCLED = 6;//资质已回收
    private static final int STATUS_DISQUALIFICATION = 8;//资质不合格
    private static final int STATUS_PAST_DUE = 9;//单据过期


    public static final String PARAMS_WAITING = STATUS_UNCOMMITTED + "," + STATUS_REJECTED;

    public static final String PARAMS_AUDIT = STATUS_STEP1 + "," + STATUS_STEP2 + "," + STATUS_AWAITS_RECYCLING;

    public static final String PARAMS_FINISH = STATUS_RECYCLED + "," + STATUS_DISQUALIFICATION + "," + STATUS_PAST_DUE + "," + STATUS_CANCELLATION;

    public static final String PARAMS_ALL = null;


    private String sysUserJob;//销售

    private String sysUserPhone;//销售

    private String sysUserName;//销售

    private long createTime;

    private String auditId;

    private String id;

    private String merchantId;

    private String merchantName;

    private String code;// 单据编号

    private int recoveryStatus;

    private int type; //1首营 2更新

    private String holder; //持有人 不为空的时候表示已认领

    public String getSysUserJob() {
        return sysUserJob;
    }

    public void setSysUserJob(String sysUserJob) {
        this.sysUserJob = sysUserJob;
    }

    public String getSysUserPhone() {
        return sysUserPhone;
    }

    public void setSysUserPhone(String sysUserPhone) {
        this.sysUserPhone = sysUserPhone;
    }

    public String getSysUserName() {
        return sysUserName;
    }

    public void setSysUserName(String sysUserName) {
        this.sysUserName = sysUserName;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getRecoveryStatus() {
        return recoveryStatus;
    }

    public void setRecoveryStatus(int recoveryStatus) {
        this.recoveryStatus = recoveryStatus;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getHolder() {
        return holder;
    }

    public void setHolder(String holder) {
        this.holder = holder;
    }

    public int getAudit1Status() {
        return audit1Status;
    }

    public void setAudit1Status(int audit1Status) {
        this.audit1Status = audit1Status;
    }

    public int getAudit2Status() {
        return audit2Status;
    }

    public void setAudit2Status(int audit2Status) {
        this.audit2Status = audit2Status;
    }

    public int getDeleteStatus() {
        return deleteStatus;
    }

    public void setDeleteStatus(int deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取单据状态
     *
     * @return
     */
    public int getStatus() {
        return statusCode;
    }
    public String getBdNameWithId() {
        return sysUserName + "(" + sysUserJob + ")";
    }
    //一审状态
    private int audit1Status;

    //二审状态
    private int audit2Status;

    /**
     * audit1Status  -1 草稿(未提交)  0 待审核（一审） 1审核通过 2驳回(驳回)
     * <p>
     * audit2Status （audit1Status=1） 0未处理(二审) 1已处理(二审) 2驳回(驳回)
     * <p>
     * recoveryStatus （audit2Status=1）0资质未回收(待回收资质) 1资质已回收(已完成) 2资质不合格(驳回)
     */
    //删除
    private int deleteStatus;//0作废 1正常
    private String statusName;
    private int statusCode;
    private String remark;

    //7.11资质优化资质变更列表  涉及到的新的变更字段 取新的字段
    public String applicationNumber;//单据编号 新字段
    public String auditStatusName;///状态名称 新字段
    public String orgName;          // 店铺名称
    public int auditStatus;//状态码 一审二审...
    public String customerCode;//
    public String customerName;//
    public String operateType;//
    public String paperRecyclStatus;//
    public String source;//
    //public String type;//首营1还是变更

}


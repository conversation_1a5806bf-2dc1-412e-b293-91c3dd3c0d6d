package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by asus on 2016/3/24.
 */
public class PromotionDetailBean implements Parcelable {

    private int id;
    private int productAmount;
    private int personalAmount;
    private int sort;
    private String raminTime;
    private PromotionBean promotion;

    public PromotionBean getPromotion() {
        return promotion;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.id);
        dest.writeInt(this.productAmount);
        dest.writeInt(this.personalAmount);
        dest.writeInt(this.sort);
        dest.writeString(this.raminTime);
        dest.writeParcelable(this.promotion, flags);
    }

    public PromotionDetailBean() {
    }

    protected PromotionDetailBean(Parcel in) {
        this.id = in.readInt();
        this.productAmount = in.readInt();
        this.personalAmount = in.readInt();
        this.sort = in.readInt();
        this.raminTime = in.readString();
        this.promotion = in.readParcelable(PromotionBean.class.getClassLoader());
    }

    public static final Parcelable.Creator<PromotionDetailBean> CREATOR = new Parcelable.Creator<PromotionDetailBean>() {
        @Override
        public PromotionDetailBean createFromParcel(Parcel source) {
            return new PromotionDetailBean(source);
        }

        @Override
        public PromotionDetailBean[] newArray(int size) {
            return new PromotionDetailBean[size];
        }
    };
}

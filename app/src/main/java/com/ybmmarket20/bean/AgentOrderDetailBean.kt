package com.ybmmarket20.bean

import com.google.gson.annotations.SerializedName

data class AgentOrderDetailBean(
    val branchCode: String?,
    val confirmExpireHour: Int,
    val confirmExpireTime: Long,
    val createTime: Long,
    val id: Int,
    val imageUrl: String?,
    val merchantId: Int,
    val merchantName: String?,
    val money: String?,
    @SerializedName("orderSproutDetailBusinessDtoList")
    val orderDetailDtoList: List<OrderDetailDto>?,
    val orderNo: String?,
    val orderSource: Int,
    val productNum: Int,
    val refuseExplan: String?,
    val refuseReason: String?,
    val saleMobile: String?,
    val saleName: String?,
    val salesId: Int,
    val status: Int,
    val varietyNum: Int,
    val orderId: String?,
    val currentDate: Long,
    val purchaseNo: String?
)
package com.ybmmarket20.business.comment.ui;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.ScrollView;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CommentLabelBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.business.comment.adapter.PicAdapter;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;

import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

public class CommentActivity extends BaseActivity {

    private CommentItemFactory.CommentItemView bdView;
    private CommentItemFactory.CommentItemView serviceView;
    private CommentItemFactory.CommentItemView expressView;


    @Bind(R.id.content)
    public LinearLayout container;
    @Bind(R.id.iv_back)
    public ImageView ivBack;

    @Bind(R.id.bt_commit)
    public Button btCommit;

    @Bind(R.id.cb_advice)
    public CheckBox cbAdvice;

    @Bind(R.id.sc)
    public ScrollView sc;

    @Bind(R.id.errorView)
    public LinearLayout errorView;

    private String orderNo;

    public static void launch(Context mContext, String orderNo) {
        Intent intent = new Intent(mContext, CommentActivity.class);
        intent.putExtra("orderNo", orderNo);
        mContext.startActivity(intent);

    }

    @Override
    protected void initCommon() {
        super.initCommon();
        ivBack.setImageResource(R.drawable.icon_comment_dialog_delete);
        ivBack.setOnClickListener((view) -> {
            AlertDialogEx exitDialog = new AlertDialogEx(CommentActivity.this);
            exitDialog.setTitle("确认取消评价?");
            exitDialog.setMessage("取消后当前评价信息不会保留");
            exitDialog.setConfirmButton("确认取消", (dialog, button) -> {
                dialog.dismiss();
                finish();


            }).setCancelButton("再想想", (dialog, button) -> {
                dialog.dismiss();

            });
            exitDialog.show();
        });
    }

    @Override
    protected void initData() {

        setTitle("服务评价");
        orderNo = getIntent().getStringExtra("orderNo");


        showProgress();
        HttpManager.getInstance().post(AppNetConfig.COMMENT_LABEL, null, new BaseResponse<List<CommentLabelBean>>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                errorView.setVisibility(View.VISIBLE);
            }

            @Override
            public void onSuccess(String content, BaseBean<List<CommentLabelBean>> response, List<CommentLabelBean> dataList) {
                if (response.isSuccess()) {
                    dismissProgress();
                    CommentLabelBean.CommentLabelResponse commentLabelResponse = new CommentLabelBean.CommentLabelResponse(dataList);

                    bdView = CommentItemFactory.getCommentItemView(CommentItemFactory.TYPE_BD, commentLabelResponse.bdLabelList, container);
                    serviceView = CommentItemFactory.getCommentItemView(CommentItemFactory.TYPE_SERVICE, commentLabelResponse.serviceLabelList, container);
                    expressView = CommentItemFactory.getCommentItemView(CommentItemFactory.TYPE_EXPRESS, commentLabelResponse.expressLabelList, container);
                    bdView.setListener(dataChangeListener);
                    serviceView.setListener(dataChangeListener);
                    expressView.setListener(dataChangeListener);

                    container.addView(bdView.getView(), 0);
                    container.addView(serviceView.getView(), 1);
                    container.addView(expressView.getView(), 2);

                    cbAdvice.setVisibility(View.VISIBLE);
                    btCommit.setVisibility(View.VISIBLE);
                } else {
                    errorView.setVisibility(View.VISIBLE);

                }
            }
        });
    }

    private CommentItemFactory.CommentItemView.DataChangeListener dataChangeListener = () -> {
        if (bdView.getResultData() != null && serviceView.getResultData() != null && expressView.getResultData() != null) {
            btCommit.setEnabled(true);
        } else {
            btCommit.setEnabled(false);
        }

    };
    private PicAdapter currentPicAdapter;

    private CommentExtraInfoDialog commentExtraInfoDialog;

    private CommentExtraInfoDialog.ExtraInfo extraInfo;

    @OnClick({R.id.cb_advice, R.id.bt_commit})
    public void onClick(View view) {

        switch (view.getId()) {

            case R.id.cb_advice:

                if (cbAdvice.isChecked()) {
                    if (commentExtraInfoDialog == null) {
                        commentExtraInfoDialog = new CommentExtraInfoDialog(CommentActivity.this);
                        commentExtraInfoDialog.setItemSelectListener((itemInfo) -> {
                            if (itemInfo == null) {
                                //用户主动关闭对话框
                                cbAdvice.setChecked(false);
                            } else {
                                extraInfo = (CommentExtraInfoDialog.ExtraInfo) itemInfo.extraInfo;
                            }
                        });
                    }
                    commentExtraInfoDialog.show();
                }
                break;

            case R.id.bt_commit:
                CommentItemFactory.ResultData bdViewResultData = bdView.getResultData();
                CommentItemFactory.ResultData serviceViewResultData = serviceView.getResultData();
                CommentItemFactory.ResultData expressViewResultData = expressView.getResultData();
                if (bdViewResultData.content.length() > 150 || serviceViewResultData.content.length() > 150 || expressViewResultData.content.length() > 150) {
                    ToastUtils.showShort("评论字数超过了最大限制~");
                    return;
                }

                RequestParams requestParams = createSaveParams(bdViewResultData, serviceViewResultData, expressViewResultData, extraInfo);
                HttpManager.getInstance().post(AppNetConfig.COMMENT_SAVE, requestParams, new BaseResponse<EmptyBean>() {
                    @Override
                    public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean emptyBean) {
                        super.onSuccess(content, obj, emptyBean);
                        if (obj.isSuccess()) {
                            UiUtils.showToast(CommentActivity.this, "提交成功！", 500);
                            LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH));

                            finish();
                        }

                    }

                    @Override
                    public void onFailure(NetError error) {
                        super.onFailure(error);

                    }
                });
                break;

        }


    }

    private RequestParams createSaveParams(CommentItemFactory.ResultData bdViewResultData, CommentItemFactory.ResultData serviceViewResultData, CommentItemFactory.ResultData expressViewResultData, CommentExtraInfoDialog.ExtraInfo extraInfo) {
        RequestParams requestParams = new RequestParams();

        requestParams.put("orderNo", orderNo);
        requestParams.put("merchantId", SpUtil.getMerchantid());
        if (bdViewResultData != null) {

            if (!TextUtils.isEmpty(bdViewResultData.labels)) {
                requestParams.put("saleServiceLabelIds", bdViewResultData.labels);

            }
            if (!TextUtils.isEmpty(bdViewResultData.images)) {
                requestParams.put("saleServicePhotoUrlStr", bdViewResultData.images);

            }
            if (!TextUtils.isEmpty(bdViewResultData.content)) {

                requestParams.put("saleServiceText", bdViewResultData.content);

            }
            requestParams.put("saleServiceLabelProperty", bdViewResultData.level + "");

        }

        if (serviceViewResultData != null) {
            if (!TextUtils.isEmpty(serviceViewResultData.labels)) {
                requestParams.put("clientServiceLabelIds", serviceViewResultData.labels);

            }
            if (!TextUtils.isEmpty(serviceViewResultData.images)) {
                requestParams.put("clientServicePhotoUrlStr", serviceViewResultData.images);

            }
            if (!TextUtils.isEmpty(serviceViewResultData.content)) {
                requestParams.put("clientServiceText", serviceViewResultData.content);

            }
            requestParams.put("clientServiceLabelProperty", serviceViewResultData.level + "");
        }

        if (expressViewResultData != null) {
            if (!TextUtils.isEmpty(expressViewResultData.labels)) {
                requestParams.put("transportServiceLabelIds", expressViewResultData.labels);

            }
            if (!TextUtils.isEmpty(expressViewResultData.images)) {
                requestParams.put("transportServicePhotoUrlStr", expressViewResultData.images);

            }
            if (!TextUtils.isEmpty(expressViewResultData.content)) {
                requestParams.put("transportServiceText", expressViewResultData.content);

            }
            requestParams.put("transportServiceLabelProperty", expressViewResultData.level + "");
        }

        if (extraInfo != null) {

            requestParams.put("needContact", "1");
            requestParams.put("appellation", extraInfo.name);
            requestParams.put("contactNo", extraInfo.phoneNumber);
            requestParams.put("sex", extraInfo.gender + "");
        }


        return requestParams;
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (currentPicAdapter != null) {
            currentPicAdapter.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_comment;
    }

    public void setPicAdapter(PicAdapter picAdapter) {
        currentPicAdapter = picAdapter;
    }


    public MyTreeObserver myTreeObserver = new MyTreeObserver();

    public class MyTreeObserver implements ViewTreeObserver.OnGlobalLayoutListener {
        @Override
        public void onGlobalLayout() {
            Rect r = new Rect();
            //获取当前界面可视部分
            getWindow().getDecorView().getWindowVisibleDisplayFrame(r);
            //获取屏幕的高度
            int screenHeight = getWindow().getDecorView().getRootView().getHeight();
            //此处就是用来获取键盘的高度的， 在键盘没有弹出的时候 此高度为0 键盘弹出的时候为一个正数
            int heightDifference = screenHeight - r.bottom;
            showAViewOverKeyBoard(heightDifference);


        }

        private PopupWindow recorderPop;

        private EditText editText;


        private TextView tvCounter;

        public void setEditText(EditText editText, TextView tvCounter) {
            this.editText = editText;
            this.tvCounter = tvCounter;

            editText.getViewTreeObserver().addOnGlobalLayoutListener(this);
            editText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {

                    int textLength = editable.toString().length();
                    String counterStr;
                    if (textLength >= 150) {
                        counterStr = "<font color='#FF0000'>" + textLength + "</font>" + "/150";

                        if (recorderPop != null) {
                            View contentView = recorderPop.getContentView();
                            ImageView imageView = recorderPop.getContentView().findViewById(R.id.iv_bg);
                            imageView.setImageResource(R.drawable.bg_comment_recorder_gray);
                            contentView.setOnClickListener(null);
                        }


                    } else {
                        counterStr = +textLength + "/150";

                        if (recorderPop != null) {
                            View contentView = recorderPop.getContentView();
                            ImageView imageView = recorderPop.getContentView().findViewById(R.id.iv_bg);
                            imageView.setImageResource(R.drawable.bg_comment_commit_round_green);
                            contentView.setOnClickListener(onClickListener);
                        }

                    }
                    tvCounter.setText(Html.fromHtml(counterStr));

                }
            });

        }

        private View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                recorderPop.dismiss();
                InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                if (inputMethodManager != null) {
                    inputMethodManager.hideSoftInputFromWindow(editText.getWindowToken(), 0);
                }
                CommentRecorderDialog commentRecorderDialog = new CommentRecorderDialog(CommentActivity.this, editText);
                commentRecorderDialog.show();
            }
        };

        private View recorderView;

        private void showAViewOverKeyBoard(int heightDifference) {
            if (heightDifference > UiUtils.getScreenHeight() / 3) {//显示
                if (recorderPop == null) {
                    recorderView = LayoutInflater.from(CommentActivity.this).inflate(R.layout.item_recorder_buttom, container, false);
                    recorderPop = new PopupWindow(recorderView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
                    recorderPop.setTouchable(true);
                    recorderPop.setOutsideTouchable(false);
                    recorderPop.setFocusable(false);
                    recorderPop.setInputMethodMode(PopupWindow.INPUT_METHOD_NEEDED);
                    recorderView.setOnClickListener(onClickListener);
                }
                //解决遮盖输入法
                if (!recorderPop.isShowing()) {

                    if (editText.getText().length() >= 150) {
                        recorderView.setOnClickListener(null);
                        ImageView imageView = recorderView.findViewById(R.id.iv_bg);
                        imageView.setImageResource(R.drawable.bg_comment_recorder_gray);
                    } else {
                        ImageView imageView = recorderView.findViewById(R.id.iv_bg);
                        imageView.setImageResource(R.drawable.bg_comment_commit_round_green);
                        recorderView.setOnClickListener(onClickListener);
                    }
                    recorderPop.showAtLocation(container, Gravity.BOTTOM, UiUtils.getScreenWidth() / 2, heightDifference);
                    editText.post(new Runnable() {
                        @Override
                        public void run() {
                            int[] locationEt = new int[2];
                            editText.getLocationOnScreen(locationEt);
                            if (locationEt[1] > heightDifference) {
                                sc.scrollBy(0, locationEt[1] - heightDifference);
                            }


                        }
                    });


                }

            } else {//隐藏
                if (recorderPop != null && recorderPop.isShowing()) {
                    recorderPop.dismiss();

                }
            }
        }

    }


}

package com.ybmmarket20.business.order.ui

import com.ybmmarket20.bean.Coupon
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.home.MainActivity
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.page.orderList.OrderListReport
import com.ybmmarket20.xyyreport.page.orderList.OrderListReportUtil
import com.ybmmarket20.xyyreport.page.rebateVoucher.RebateVoucherReport
import com.ybmmarket20.xyyreport.spm.SpmUtil

abstract class MineOrderAnalysisFragment: BaseFragment() {

    private var isFirstReportPv = false
    var mTargetTabIndex = -1

    var mAnalysisCouponInfo: Coupon? = null
    var mIsShownRebateVoucher = false

    /**
     * pv
     */
    fun pvTrack() {
        if (mTargetTabIndex != getVpCurrIndex()) return
        mTargetTabIndex = -1
        pvTrackInternal()
    }

    private fun pvTrackInternal() {
        try {
            val orderStatus = getOrderListAnalysisParams()?.getOrderStatus()?: 0
            val orderStatusText = OrderListReportUtil.getOrderStatusInfoByStatus(orderStatus)?.orderReportTag
            OrderListReport.pvOrderList(requireActivity(), orderStatusText)
            SpmUtil.checkAnalysisContext(requireActivity()) {
                (notNullActivity as MainActivity).trackCommonTabComponentExposure(it)
                //有优惠券信息才埋点
                if (mAnalysisCouponInfo != null) {
                    OrderListReport.onOrderListCouponExposure(it, mAnalysisCouponInfo?.sceneGroupId, "去使用", mAnalysisCouponInfo?.couponId)
                }
                //消费返
                if (mIsShownRebateVoucher) {
                    RebateVoucherReport.trackRebateVoucherComponentExposureForOrderList(it)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun pvTrackFirst() {
        if (!isFirstReportPv) {
            isFirstReportPv = true
            mTargetTabIndex = -1
            pvTrackInternal()
        }
    }

    fun onCouponTipClick() {
        OrderListReport.onOrderListCouponClick(requireActivity(), mAnalysisCouponInfo?.sceneGroupId, "去使用", mAnalysisCouponInfo?.couponId)
    }

    open fun getOrderListAnalysisParams(): IOrderListAnalysisParams? = null

    abstract fun getVpCurrIndex(): Int
}